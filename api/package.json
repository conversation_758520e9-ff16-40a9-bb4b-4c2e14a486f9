{"name": "@fintary/api", "version": "6.11.5", "required_fe_version": "2025-06-25 00:00", "preferred_fe_version": "2025-06-25 00:00", "private": true, "scripts": {"postinstall": "prisma generate", "tsc": "tsc", "dev-win": "set PORT=3000 && dotenv -e .env.dev next", "prod-win": "set PORT=3000 && dotenv -e .env.prod next", "dev-local": "npm run local", "gcloud:login": "gcloud auth list --filter='status:ACTIVE' --format='value(account)' || gcloud auth login", "gcloud:project:dev": "gcloud config set project fintary-dev", "gcloud:project:prod": "gcloud config set project fintary-prod", "gcloud:login:dev": "npm run gcloud:login && npm run gcloud:project:dev", "gcloud:login:prod": "npm run gcloud:login && npm run gcloud:project:prod", "preload-secrets:dev": "npm run gcloud:login:dev && PROJECT_ID=fintary-dev ts-node scripts/load-secrets-to-env.ts", "preload-secrets:prod": "npm run gcloud:login:prod && PROJECT_ID=fintary-prod ts-node scripts/load-secrets-to-env.ts", "dev": "npm run preload-secrets:dev && PROJECT_ID=fintary-dev RUNTIME=DEV NODE_OPTIONS=--max-old-space-size=10000 export DATABASE_URL=$(gcloud secrets versions access latest --secret=DATABASE_URL_LOCAL) && prisma generate && cross-env PORT=3000 dotenv -e .env next", "prod": "cross-env PORT=3000 dotenv -e .env.prod next", "preview": "npm run preload-secrets:prod && PROJECT_ID=fintary-prod RUNTIME=PREVIEW NODE_OPTIONS=--max-old-space-size=10000 export DATABASE_URL=$(gcloud secrets versions access latest --secret=DATABASE_URL_LOCAL) && prisma generate && cross-env PORT=3000 dotenv -e .env next", "build": "next build", "clean": "rm -rf .next", "deep-clean": "npm run clean && rm -rf node_modules", "start": "cross-env NODE_ENV=production node server.js", "local": "PORT=3000 dotenv -e .env.local next", "local:db-generate-migration": "prisma generate && dotenv -e .env.local -- prisma migrate dev", "local:db-generate-migration-create-only": "prisma generate && dotenv -e .env.local -- prisma migrate dev --create-only", "local:db-seed": "dotenv -e .env.local -- prisma db seed", "local:db-seed-local-database-from-dev": "ts-node scripts/seed-local-database/seed-local-database.ts", "integration:db-seed": "dotenv -e .env.local -- ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.integration.ts", "integration:migrate": "prisma generate && dotenv -e .env.local -- prisma migrate deploy", "lint": "eslint . --max-warnings=0", "lint-fix": "eslint . --fix", "release": "npm run release --prefix ../", "changelog": "npm run changelog --prefix ../", "introspect": "dotenv -e .env.local prisma introspect", "test:unit": "cross-env TZ=UTC vitest -c ./vitest.config.unit.ts --run", "test:db": "cross-env TZ=UTC vitest -c ./vitest.config.db.ts --run", "test:integration": "./http/run-integration-tests.sh", "test:unit:ui": "cross-env TZ=UTC vitest -c ./vitest.config.unit.ts --ui", "deploy-dev": "npm run deploy-dev --prefix ../", "deploy-prod": "npm run deploy-prod --prefix ../", "load-payout-report-dev": "npx dotenv -e .env.dev -- ts-node ./payout-report-loader/csv-loader.ts", "load-payout-report-prod": "npx dotenv -e .env.prod -- ts-node ./payout-report-loader/csv-loader.js", "check-deps": "madge -c --extensions ts,tsx --ts-config ./tsconfig.json ./"}, "dependencies": {"@adobe/pdfservices-node-sdk": "^4.1.0", "@aws-sdk/client-s3": "^3.806.0", "@casl/ability": "^6.7.3", "@google-cloud/documentai": "^9.1.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@google-cloud/tasks": "^6.0.1", "@google-cloud/vertexai": "^1.10.0", "@next-auth/prisma-adapter": "1.0.7", "@prisma/client": "^5.22.0", "@react-pdf/renderer": "^4.3.0", "@sendgrid/mail": "^8.1.5", "@sentry/nextjs": "^8.55.0", "archiver": "^7.0.1", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "bluebird": "^3.7.2", "cheerio": "^1.0.0", "chrono-node": "2.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "common": "0.24.12", "crypto-hash": "^3.1.0", "csv-parser": "^3.2.0", "currency.js": "^2.0.4", "damerau-levenshtein": "^1.0.8", "dayjs": "^1.11.13", "didyoumean2": "^7.0.4", "fast-csv": "^5.0.2", "fast-xml-parser": "^4.5.3", "fastest-levenshtein": "^1.0.16", "firebase-admin": "^13.3.0", "formidable": "^3.5.4", "googleapis": "^150.0.1", "inversify": "^6.2.2", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "mathjs": "^14.4.0", "module-alias": "^2.2.3", "nanoid": "^5.1.5", "next": "^14.2.30", "next-api-decorators": "^2.0.2", "next-auth": "^4.24.11", "object-sizeof": "^2.6.5", "p-retry": "^6.2.1", "path-to-regexp": "^6.3.0", "pg-format": "^1.0.4", "pgvector": "^0.2.0", "plaid": "^36.0.0", "prisma-extension-pagination": "^0.7.5", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "soap": "^1.1.11", "statsig-node": "^5.33.0", "stripe": "^17.7.0", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yup": "^1.6.1", "zod": "^3.24.2", "zod-openapi": "^4.2.4"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@types/adobe__pdfservices-node-sdk": "^2.2.3", "@types/bluebird": "^3.5.42", "@types/cheerio": "^0.22.35", "@types/formidable": "^3.4.5", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.0", "@types/react": "^18.3.23", "@types/swagger-ui-react": "^5.18.0", "@vitest/ui": "^3.1.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "globals": "^15.15.0", "jest-worker": "^29.7.0", "madge": "^8.0.0", "next-swagger-doc": "^0.4.1", "prettier": "^3.6.2", "prisma": "^5.22.0", "swagger-ui-react": "^5.20.7", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.1.1", "vitest-mock-extended": "^3.1.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.1"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}