import { ExportOptions } from 'common/exports';

import {
  ExtNextApiRequest,
  ExtNextApiResponse,
  savedReportsGroupsTemplates,
} from '@/types';

export type ExportParams = {
  exportOptions: ExportOptions;
  res: ExtNextApiResponse;
  req: ExtNextApiRequest;
  groupData: {
    template: savedReportsGroupsTemplates;
    saved_reports: any[];
    created_at: string;
    account: any;
  };
  summaryResults: any;
  table: any;
  roleForExport: any;
  compReportSettings?: any;
};
