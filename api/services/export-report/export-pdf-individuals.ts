import * as Sentry from '@sentry/nextjs';
import { formatCurrency } from 'common/helpers/formatCurrency';
import { CompReportViewTypes } from 'common/globalTypes';

import { savedReportsGroupsTemplates } from '@/types';
import { exportTotalRows } from '@/pages/api/export/exportFields';
import Formatter from '@/lib/Formatter';
import CommissionPayoutTemplate from '@/lib/templates/CommissionPayoutTemplate';
import {
  consolidateGroupedRecords,
  getStorageSignedUrl,
  arrayToObjByKey,
} from '@/lib/helpers';
import {
  createZipStream,
  isReportInSelectedData,
  truncateName,
} from '@/services/export-report/utils';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import { ExportParams } from './types';
import { getExportData } from '@/pages/api/export/base';
import { processContacts } from '@/lib/helpers/processContacts';

const createSummarySheet = async ({
  groupData,
  summaryResults,
  zip,
  endAdornment,
}: Pick<ExportParams, 'groupData' | 'summaryResults'> & {
  zip: any;
  endAdornment: string;
}) => {
  // Add summary file only for commission payout
  if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
    const pdfData = {};
    pdfData['summary'] = formatPdfCurrency(summaryResults);

    try {
      const pdfResult = await CommissionPayoutTemplate(pdfData);
      zip.append(pdfResult, { name: `Summary${endAdornment}.pdf` });
    } catch (error) {
      console.error(`Error: ${error.message}`);
      Sentry.captureException(error);
      zip.append('', { name: `ERROR-Summary.pdf` });
    }
  }
};

export const exportPdfIndividuals = async (params: ExportParams) => {
  const {
    groupData,
    res,
    req,
    summaryResults,
    exportOptions,
    table,
    roleForExport,
    compReportSettings,
  } = params;

  let logoImg = '';
  if (groupData.account.white_label_mode) {
    logoImg = await getStorageSignedUrl(groupData.account.logo_url);
  }
  const zip = createZipStream({ res });
  const dateSuffix = exportOptions?.date_suffix;
  const endAdornment = dateSuffix ? ` - ${dateSuffix}` : '';
  createSummarySheet({ groupData, summaryResults, zip, endAdornment });

  for (const report of groupData.saved_reports) {
    // Check for create reports of selected data
    if (!isReportInSelectedData(report, exportOptions.selected_data)) {
      continue;
    }

    const commissionAmount = +Object.values(
      report.snapshot_data?.data?.totals?.agent_commissions
    )?.[0];
    // Apply amount due threshold filter for commission payout
    if (
      groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT &&
      exportOptions &&
      exportOptions.amount_due_threshold &&
      +exportOptions.amount_due_threshold > 0
    ) {
      if (commissionAmount < +exportOptions.amount_due_threshold) {
        continue;
      }
    }

    // Check grouping options
    if (exportOptions.grouping === 'policyNumber') {
      const groupedRecords = arrayToObjByKey(
        report.snapshot_data.data.data,
        'policy_id',
        true
      );
      const consolidatedData = consolidateGroupedRecords(groupedRecords);

      report.snapshot_data.data.data = consolidatedData;
    }

    await processContacts(report.snapshot_data.data.data);

    let formatData = await getExportData({
      data: report.snapshot_data.data.data,
      table,
      roleId: roleForExport,
      accountId: req.account_id,
      templateType: groupData.template,
      uid: req.uid,
      contactStrId:
        req.query.view === CompReportViewTypes.PRODUCER_VIEW
          ? report.snapshot_data.data.contactStrId
          : undefined,
    });

    const totalRow = await exportTotalRows(formatData);

    formatData = formatPdfCurrency(formatData);
    const pdfData = {
      reportData: {
        data: [],
        totals: [],
        reportInfo: [],
      },
    };

    pdfData.reportData.data.push(formatData);
    pdfData.reportData.totals.push(totalRow);
    pdfData.reportData.reportInfo.push({
      name: report.name,
      commissionAmount: `Total producer commission: ${formatCurrency(commissionAmount)}`,
      createdAt: Formatter.date(groupData.created_at),
      logoImg: logoImg,
      accountName: groupData.account.name,
      customTermsEnabled: compReportSettings?.enable_custom_terms,
      customTermsText: compReportSettings?.custom_terms_text,
    });

    // Create pdf using a template
    try {
      console.log(`Generating PDF: ${report.name}`);
      const pdfResult = await CommissionPayoutTemplate(pdfData);
      const truncatedName = truncateName(report.name, groupData.template);
      zip.append(pdfResult, { name: `${truncatedName}${endAdornment}.pdf` });
    } catch (error) {
      const truncatedName = truncateName(report.name, groupData.template);
      console.error(`Error: ${error.message}`);
      Sentry.captureException(error);
      zip.append('', { name: `ERROR-${truncatedName}${endAdornment}.pdf` });
    }
  }

  try {
    // This finalizes the archive and sends it to the client
    await zip.finalize();
  } catch (error) {
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
