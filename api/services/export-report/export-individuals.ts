import * as Sentry from '@sentry/nextjs';
import * as XLSX from 'xlsx';

import { createZipStream, truncateName } from './utils';
import { ExportParams } from './types';
import {
  createReportWorksheet,
  createSummarySheet,
  processReportData,
} from './export-report-helpers';

export const exportIndividuals = async (params: ExportParams) => {
  const { groupData, res, summaryResults, exportOptions } = params;
  try {
    const zip = createZipStream({ res });
    const dateSuffix = exportOptions?.date_suffix;
    const endAdornment = dateSuffix ? ` - ${dateSuffix}` : '';
    const summarySheet = createSummarySheet({ groupData, summaryResults });
    if (summarySheet) {
      zip.append(Buffer.from(summarySheet), {
        name: `Summary${endAdornment}.xlsx`,
      });
    }

    for (const report of groupData.saved_reports) {
      const processedData = await processReportData({
        report,
        ...params,
      });

      if (!processedData) continue;

      const { formatData, filteredTotalRowFormatted, commissionAmount } =
        processedData;

      const individualReportWorkbook = XLSX.utils.book_new();

      const worksheet = createReportWorksheet({
        report,
        formatData,
        filteredTotalRowFormatted,
        commissionAmount,
        groupData,
      });

      const truncatedName = truncateName(report.name, groupData.template);

      XLSX.utils.book_append_sheet(
        individualReportWorkbook,
        worksheet,
        truncatedName
      );

      const wbout = XLSX.write(individualReportWorkbook, {
        bookType: 'xlsx',
        type: 'buffer',
      });

      zip.append(Buffer.from(wbout), {
        name: `${truncatedName}${endAdornment}.xlsx`,
      });
    }

    try {
      // This finalizes the archive and sends it to the client
      await zip.finalize();
    } catch (error) {
      console.error(`Error: ${error.message}`);
      Sentry.captureException(error);
      res.status(500).json({ error: error.message });
    }
  } catch (error) {
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
