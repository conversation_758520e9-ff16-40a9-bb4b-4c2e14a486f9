import { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { inject, injectable } from 'inversify';
import { AccountIds, WorkerNames } from 'common/constants';
import dayjs from 'dayjs';

import { DataProcessService } from '@/services/data_processing';
import { DataProcessingTypes, DataProcessingStatuses } from '@/types';
import { AppLoggerService } from '@/services/logger/appLogger';
import {
  AgentDatum,
  BaseWorker,
  CompanyDatum,
  DBData,
  IDataSyncWorker,
  PolicyDatum,
  ProductDatum,
} from '@/services/queue/worker/base';
import { SmartOfficeService } from '@/services/smart-office';
import {
  AgentStatusMap,
  AgentTypeLookupMap,
  Contact,
  ContactSchema,
  InterestParty,
  Operation,
  Policy,
  PremModeMap,
  Product,
  ProductSchema,
  Role,
  StateCodeMap,
  Vendor,
} from '@/services/smart-office/interface';
import { getValidDate, limitConcurrency } from '@/lib/helpers';
import { ExtAccountInfo } from '@/types';
import { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { Cache } from '@/lib/decorators';
import { ContactService } from '@/services/contact';
import { CloudTaskService } from '@/services/cloud-task';

// Integration docs: https://help.smartofficecrm.com/si/index.htm#t=Get_Operation.htm
// Portal: https://uat.smartofficeonline.com/pprodnew/index.htm
@injectable()
export class SmartOfficeWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.SmartOfficeWorker;

  @inject(SmartOfficeService)
  smartOfficeService: SmartOfficeService;

  @inject(DataProcessService)
  dataProcessingService: DataProcessService;

  @inject(ContactService)
  contactService: ContactService;

  @inject(CloudTaskService) cloudTaskService: CloudTaskService;

  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: WorkerNames.SmartOfficeWorker },
  });

  constructor() {
    super();
    this.registerDataSource({
      carriersAndProducts: this.fetchProductsAndCarriers.bind(this),
      policies: this.fetchPolicies.bind(this),
      agents: this.fetchContacts.bind(this),
    });
    this.registerTransformer({
      productTransform: {
        getData: this.getProductData.bind(this),
      },
      companyTransform: {
        getData: this.getCarrierData.bind(this),
      },
    });
  }

  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        username: string;
        siteName: string;
        apiKey: string;
        apiSecret: string;
        endpoint?: string;
      }>
    >({ account_id: account.account_id, worker: this.name });
    this.smartOfficeService.loadConfig(config?.value?.credentials);
  }

  @Cache('fetchProductsAndCarriers')
  async fetchProductsAndCarriers() {
    const products = await this.pagination<Product>(async ({ searchId }) => {
      return await this.smartOfficeService.getProducts({ searchId });
    });
    const carriers = await this.pagination<Vendor>(async ({ searchId }) => {
      return await this.smartOfficeService.getVendors({
        searchId,
        schema: {
          CarrierID: null,
          Contact: {
            Name: null,
          },
        },
      });
    });
    return { products, carriers };
  }

  async fetchContacts() {
    const contactObjectsIds = await this.pagination<Contact>(
      async ({ searchId }) => {
        return await this.smartOfficeService.queryEntityIds('Contact', {
          searchId,
          condition: {
            expr: {
              '@prop': 'Category',
              '@op': Operation.EQUAL,
              v: 'Advisor/Agency',
            },
          },
        });
      }
    );
    const contacts = await limitConcurrency(
      async (contactId) => {
        return await this.smartOfficeService.get({
          schema: {
            Contact: {
              ...ContactSchema,
              '@id': contactId['@id'],
            },
          },
        });
      },
      contactObjectsIds,
      100
    );
    return contacts;
  }
  async searchMktManager(name: string) {
    const [firstName, lastName] = name.split(' ');
    const result = await this.smartOfficeService.queryEntityIds('Contact', {
      condition: {
        expr: [
          {
            '@prop': 'Category',
            '@op': Operation.EQUAL,
            v: 'Advisor/Agency',
          },
          {
            '@prop': 'FirstName',
            '@op': Operation.EQUAL,
            v: firstName,
          },
          {
            '@prop': 'LastName',
            '@op': Operation.EQUAL,
            v: lastName,
          },
        ],
      },
    });
    const contact = result?.data?.[0];
    if (result.total === 0 || !contact) {
      this.logger.warn(`No MktManager found for ${name}`);
      return {};
    }
    const detail = await this.smartOfficeService.get({
      schema: {
        Contact: {
          ...ContactSchema,
          '@id': contact['@id'],
        },
      },
    });
    return detail;
  }

  getProductData(data: { products: Product[]; carriers: Vendor[] }) {
    return data?.products;
  }

  getCarrierData(data: { products: Product[]; carriers: Vendor[] }) {
    return data?.carriers;
  }

  async pagination<T>(
    fetchFn: (params: { searchId: string }) => Promise<{
      data: T[];
      total: number;
      searchId: string;
      more: boolean;
      pageSize: number;
      page: number;
    }>
  ) {
    let cursor = '';
    let hasMore = true;
    const result: T[] = [];
    while (hasMore) {
      const ret = await fetchFn({ searchId: cursor });
      this.logger.info(`fetched ${ret.data?.length} items`);
      if (!ret.more) {
        hasMore = false;
      }
      cursor = ret.searchId;
      result.push(...(ret?.data ?? []));
    }
    this.logger.info(`fetched ${result.length} items in total`);
    return result;
  }

  async fetchPolicies() {
    // Check if isFullSync is specified in the task payload
    const isFullSync = this.task?.payload?.isFullSync === true;

    const lastSync = isFullSync
      ? null
      : await this.dataProcessingService.getLastTask({
          account_id: this.task.account.account_id,
          type: DataProcessingTypes.data_sync,
          status: DataProcessingStatuses.COMPLETED,
          worker: this.name,
          entity: 'policies',
        });

    if (isFullSync) {
      this.logger.info('Full sync requested, ignoring last sync timestamp');
    } else if (lastSync) {
      // Subtract 7 days from the last sync date to ensure we don't miss any updates
      const syncDate = dayjs(lastSync.created_at).subtract(7, 'day');
      this.logger.info(
        `Incremental sync from ${syncDate.format('YYYY-MM-DD')} (7 days earlier than last sync)`
      );
    } else {
      this.logger.info('No previous sync found, performing full sync');
    }

    const policies = await this.pagination<Policy>(async ({ searchId }) => {
      const isBrokersCentral =
        this.task?.account?.account_id === AccountIds.BROKERS_CENTRAL;
      const condition =
        lastSync && !isFullSync
          ? {
              expr: [
                {
                  '@prop': 'ModifiedOn',
                  '@op': Operation.GREATER_THAN,
                  v: dayjs(lastSync.created_at)
                    .subtract(7, 'day')
                    .format('YYYY-MM-DD'),
                },
              ],
            }
          : { expr: [] };
      if (isBrokersCentral) {
        condition.expr.push({
          '@prop': 'PolicyStatus',
          '@op': Operation.EQUAL,
          v: '1',
        });
      }
      return await this.smartOfficeService.getPolicies({
        searchId,
        pageSize: 2000,
        schema: {
          UniqueID: null,
          CarrierName: null,
          PolicyNumber: null,
          Age: null,
          AnnualPlcyFee: null,
          AnnualPremium: null,
          BeneficaryName: null,
          Description: null,
          Duration: null,
          MktManager: null,
          InsuredName: null,
          OwnerName: null,
          PaymentMethod: null,
          PlanType: null,
          PolicyDate: null,
          RenewalDate: null,
          PolicyStatus: null,
          PolicyStatusText: null,
          ModifiedDate: null,
          PolicyType: null,
          Premium: null,
          PrimaryAgentName: null,
          PremiumMode: null,
          ProductID: null,
          ProductIDVal: null,
          IssuedDate: null,
          WriteState: null,
          PendingCase: {
            CommPrem: null,
            CommAnnPrem: null,
            TeamText: null,
          },
          // The following object will be retrieved in subsequent get request
          Carrier: null,
          Contact: null,
          PrimaryAdvisor: null,
          Product: null,
          InterestParties: {
            InterestParty: {
              InterestPercent: null,
              Name: null,
              PartyRole: null,
              PartyRoleText: null,
              ContactID: null,
              Contact: {
                MktManager: null,
                Agent: {
                  CaseManagerID: null,
                  Type: null,
                  BrokerDealer: null,
                },
                NewBusinesses: {
                  NewBusiness: {
                    TeamText: null,
                  },
                },
              },
              PolicyID: null,
            },
          },
        },
        // Add filter condition for ModifiedOn if there was a last successful sync and not a full sync
        condition,
      });
    });

    const mktManagers = [
      ...new Set(policies.map((r) => r?.MktManager).filter(Boolean)),
    ];
    const mktManagerDetails = await limitConcurrency(
      async (name) => {
        return await this.searchMktManager(name);
      },
      mktManagers,
      100
    );

    const productIds = [
      ...new Set(
        policies
          .filter((r) => r.Product)
          ?.map((p) => p.Product?.['@id'])
          .filter(Boolean)
      ),
    ];
    const products = await limitConcurrency(
      async (productId) => {
        return await this.smartOfficeService.get({
          schema: {
            Product: {
              ...ProductSchema,
              '@id': productId,
            },
          },
        });
      },
      productIds,
      100
    );
    const productMap = new Map(products.map((p) => [p['@id'], p]));
    const managerMap = new Map(
      mktManagerDetails.map((m) => [`${m.FirstName} ${m.LastName}`, m])
    );

    return policies.map((p) => ({
      ...p,
      MktManagerId: managerMap.get(p.MktManager)?.['@id'],
      Product: p.Product ? productMap.get(p.Product['@id']) : undefined,
    }));
  }

  productTransform(data: Product, dbData?: DBData): ProductDatum {
    return {
      sync_id: data['@id'],
      product_name: data?.Name,
      product_type: data?.ProductTypeText,
      company_id: dbData?.companies?.get(data.FullContactID?.Vendor?.['@id'])
        ?.id,
    };
  }

  companyTransform(datum: Vendor): CompanyDatum {
    return {
      sync_id: datum?.['@id'],
      company_name: datum?.Contact?.Name,
      company_id: datum?.CarrierID?.toString(),
      type: ['Carrier'],
    };
  }

  agentTransform(datum: Contact): AgentDatum {
    const businessAddress = Array.isArray(datum?.Addresses?.Address)
      ? datum?.Addresses?.Address?.find((r) => r.AddressType === 2)
      : undefined;
    return {
      sync_id: datum?.['@id'],
      name: datum?.Name?.toString(),
      middle_name: datum?.MiddleName?.toString(),
      first_name: datum?.FirstName?.toString(),
      last_name: datum?.LastName?.toString(),
      company_name: datum?.EmployerName?.toString(),
      agent_code: datum?.Agent?.Code?.toString(),
      birthday: getValidDate(datum?.Person?.Dob),
      gender: datum?.Person?.GenderText?.toString(),
      city: businessAddress?.City?.toString(),
      country: businessAddress?.Country?.toString(),
      geo_state: businessAddress?.State?.toString(),
      title: datum?.Person?.JobTitle?.toString(),
      zip: businessAddress?.Postal?.toString(),
      email: datum?.PreferredEmailAddress?.WebAddress?.Address?.toString(),
      phone: datum?.Phones?.Phone?.[0]?.PureNumber?.toString(),
      phone2: datum?.Phones?.Phone?.[1]?.PureNumber?.toString(),
      phone_type: datum?.Phones?.Phone?.[0]?.PhoneTypeText?.toString(),
      phone2_type: datum?.Phones?.Phone?.[1]?.PhoneTypeText?.toString(),
      status: AgentStatusMap.get(datum?.Agent?.Status),
      notes: datum?.Remark?.toString(),
    };
  }

  getInterestParty(datum: Policy): InterestParty[] {
    const parties = Array.isArray(datum?.InterestParties?.InterestParty)
      ? datum?.InterestParties?.InterestParty
      : [datum?.InterestParties?.InterestParty];
    return parties.filter((r) => r && r.InterestPercent && r.ContactID);
  }

  getContactsFromInterestParties(datum: Policy, dbData?: DBData): string[] {
    const syncingRoles = [
      Role.SALES_REP,
      Role.PRIMARY_AGENT,
      Role.WRITING,
      Role.ADDITIONAL_WRITING_ADVISOR,
    ];
    const interestParties = this.getInterestParty(datum);
    const contacts = interestParties
      ?.filter((r) => syncingRoles.includes(r.PartyRole))
      .map((r) => {
        const contactId = r.ContactID;
        return dbData?.contacts?.get(contactId.toString());
      })
      .filter(Boolean);
    return contacts.map((c) => c?.str_id);
  }

  getSplitsForPolicy(datum: Policy, dbData?: DBData): Record<string, number> {
    const interestParties = this.getInterestParty(datum);
    const splits = interestParties?.reduce(
      (acc: Record<string, number>, curr) => {
        const contactId = curr.ContactID;
        const contact = dbData?.contacts?.get(contactId.toString());
        if (contact) {
          acc[contact.str_id] = curr.InterestPercent;
        }
        return acc;
      },
      {}
    );
    return Object.keys(splits ?? {})?.length ? splits : undefined;
  }

  /**
   * Registry of account-specific policy transformers
   * This allows for extensible account-specific transformations without hardcoding
   */
  private accountPolicyTransformers: Map<
    string,
    (datum: PolicyDatum, data: Policy, dbData?: DBData) => PolicyDatum
  > = new Map([
    [AccountIds.BGA, this.bgaAccountPolicyTransform.bind(this)],
    [AccountIds.BROKERS_CENTRAL, this.brokerCentralsPolicyTransform.bind(this)],
    [AccountIds.DMI, this.dmiPolicyTransform.bind(this)],
    // Add more account-specific transformers here:
    // [AccountIds.WORLD_CHANGERS, this.worldChangersAccountPolicyTransform.bind(this)],
    // [AccountIds.BROKERS_ALLIANCE, this.brokersAllianceAccountPolicyTransform.bind(this)],
  ]);

  /**
   * Apply account-specific policy transformations
   * This method delegates to the appropriate account-specific transformer
   */
  applyAccountSpecificTransforms(
    datum: PolicyDatum,
    data: Policy,
    dbData?: DBData
  ): PolicyDatum {
    const accountId = this.task?.account?.account_id;
    if (!accountId) {
      return datum;
    }

    const transformer = this.accountPolicyTransformers.get(accountId);
    return transformer ? transformer(datum, data, dbData) : datum;
  }

  /**
   * BGA-specific policy transformation
   * Adds case manager contact when case manager exists in interest parties
   */
  bgaAccountPolicyTransform(datum: PolicyDatum, data: Policy): PolicyDatum {
    const caseManagerStrId = '2Cbzq_yQza-VshprIi6vn'; // BGA case manager pool id

    const caseManager = data?.InterestParties?.InterestParty?.some(
      (r) => r.Contact?.Agent?.CaseManagerID
    );

    if (caseManager) {
      return {
        ...datum,
        contacts: [...(datum?.contacts ?? []), caseManagerStrId],
      };
    }
    return datum;
  }

  brokerCentralsPolicyTransform(
    datum: PolicyDatum,
    data: Policy & { MktManagerId?: string },
    dbData: DBData
  ): PolicyDatum {
    const premium = data?.PendingCase?.CommPrem;
    const annualPremium = data?.PendingCase?.CommAnnPrem;
    const contacts = datum?.contacts || [];
    const contacts_split = datum?.contacts_split || {};
    const mktManager = dbData?.contacts?.get(
      data?.MktManagerId?.toString()
    )?.str_id;

    if (mktManager) {
      contacts_split[mktManager] = 0;
    }

    return {
      ...datum,
      contacts: mktManager ? [...new Set([...contacts, mktManager])] : contacts,
      contacts_split,
      premium_amount: premium,
      commissionable_premium_amount: annualPremium,
    };
  }

  /**
   * Returns a lookup map of Sales VP names to their SmartOffice Contact IDs.
   * Used for mapping Sales VP names to their corresponding contact IDs.
   */
  getSyncSalesVP(name: string) {
    const lookup = new Map<string, string>([
      ['Keith Schettino', '_QpMunmMw7lFI34Be_i-s'],
      ['Tyrell Jensen', 'JrZrp6hv42lpW_SfW_8KT'],
      ['Joshua Rhem', 'I4fAjCesU_b91CAwxo6so'],
      ['Erick Lindewall', 'ujKVBodLmTnEZ1VGdEXpS'],
      ['Cory Adamson', 'FWYtWTA7kBXxY3iYGjXfN'],
      ['Phil Ferrara', '8QT98w81L-Kr3NwchSWOT'],
    ]);
    return lookup.get(name) || undefined;
  }

  /**
   * DMI-specific policy transformation
   */
  dmiPolicyTransform(datum: PolicyDatum, data: Policy): PolicyDatum {
    const parties = this.getInterestParty(data);
    let groupName: string | undefined;
    let transactionType: string | undefined;
    let teamText: string | undefined;

    if (parties && parties.length > 0) {
      // Filter for writing agents
      const writingAgents = parties.filter((p) => p.Contact?.Agent);
      teamText = data?.InterestParties?.InterestParty?.find(
        (p) => p.Contact?.NewBusinesses?.NewBusiness?.TeamText
      )?.Contact?.NewBusinesses?.NewBusiness?.TeamText;

      if (writingAgents.length > 0) {
        // Find the writing agent with the highest split
        const topWritingAgent = writingAgents.reduce((prev, curr) =>
          (curr.InterestPercent ?? 0) > (prev.InterestPercent ?? 0)
            ? curr
            : prev
        );

        transactionType = AgentTypeLookupMap.get(
          topWritingAgent.Contact?.Agent?.Type
        );

        groupName = topWritingAgent?.Contact?.Agent?.BrokerDealer;
      }
    }
    const splits = datum?.contacts_split || {};
    let contacts = datum?.contacts || [];
    if (splits && teamText) {
      const agentStrId = this.getSyncSalesVP(teamText);
      if (agentStrId) {
        splits[agentStrId] = 0; // Assign 100% split to the Sales VP
      }
      contacts = [...new Set([...contacts, agentStrId])]; // Ensure unique contacts
    }

    return {
      ...datum,
      transaction_type: transactionType,
      group_name: groupName,
      contacts_split: splits,
      contacts,
    };
  }

  policyTransform(
    datum: Omit<Policy, 'Carrier'>,
    dbData?: DBData
  ): PolicyDatum {
    const splits = this.getSplitsForPolicy(datum, dbData);
    const contacts = this.getContactsFromInterestParties(datum, dbData);
    const policyDatum = {
      sync_id: datum?.['@id'],
      policy_id: datum?.PolicyNumber?.toString(),
      product_name: datum?.Product?.Name,
      writing_carrier_name: datum?.CarrierName,
      payment_mode: PremModeMap.get(datum?.PremiumMode),
      company_id: dbData?.companies?.get(datum?.Product?.VendorContactID)?.id,
      commissionable_premium_amount: datum?.AnnualPremium,
      policy_status: datum?.PolicyStatusText,
      geo_state: StateCodeMap.get(datum?.WriteState),
      product_sub_type: datum?.PlanType,
      issue_age: datum?.Age,
      agent_name: datum?.PrimaryAgentName,
      product_type: datum?.Product?.ProductTypeText,
      customer_name: datum?.InsuredName,
      contacts_split: splits,
      effective_date: getValidDate(datum?.IssuedDate),
      contacts: contacts?.length ? contacts : undefined,
    };
    return this.applyAccountSpecificTransforms(policyDatum, datum, dbData);
  }
}
