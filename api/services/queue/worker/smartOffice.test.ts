import { describe, it, expect, beforeEach } from 'vitest';
import { AccountIds } from 'common/constants';

import { SmartOfficeWorker } from './smartOffice';
import { Role } from '@/services/smart-office/interface';
import type { Policy, InterestParty } from '@/services/smart-office/interface';
import type { DBData, PolicyDatum } from './base';

describe('SmartOfficeWorker', () => {
  let worker: SmartOfficeWorker;

  beforeEach(() => {
    worker = new SmartOfficeWorker();
  });

  const createMockContact = (
    id: number,
    strId: string,
    firstName: string,
    lastName: string
  ) => ({
    str_id: strId,
    id,
    first_name: firstName,
    last_name: lastName,
    name: `${firstName} ${lastName}`,
    account_id: '1',
    uid: '1',
    state: 'active',
    created_at: new Date(),
    created_by: '1',
    created_proxied_by: '1',
    updated_at: new Date(),
    updated_by: '1',
    updated_proxied_by: '1',
    agent_code: `A00${id}`,
    bank_info: null,
    balance: null,
    company_name: null,
    company: null,
    contact_group_id: null,
    email: null,
    notes: null,
    phone: null,
    title: null,
    type: null,
    status: 'active',
    birthday: null,
    city: null,
    country: null,
    geo_state: null,
    middle_name: null,
    nickname: null,
    phone2: null,
    phone2_type: null,
    phone_type: null,
    zip: null,
    customer_id: null,
    gender: null,
    sync_id: `${id}`,
    level: null,
    log: null,
    reports: [],
    payable_status: null,
    config: null,
    user_str_id: null,
    sync_worker: null,
    account_role_settings_id: null,
  });

  const createMockDBData = (mocks: Partial<DBData>): DBData => {
    const dbData: DBData = {
      contacts: new Map([]),
      contact_levels: new Map([]),
      companies: new Map([]),
      company_products: new Map([]),
      contact_hierarchy: new Map([]),
      report_data: new Map([]),
      comp_grid_levels: new Map([]),
      comp_grids: new Map([]),
      comp_grid_rates: new Map([]),
      comp_grid_products: new Map([]),
      comp_grid_criteria: new Map([]),
      agent_commission_schedule_profiles: new Map([]),
      agent_commission_schedule_profiles_sets: new Map([]),
      contacts_agent_commission_schedule_profiles_sets: new Map([]),
      date_ranges: new Map([]),
      documents: new Map([]),
      customers: new Map([]),
    };

    Object.entries(mocks).forEach(([entity, data]) => {
      if (entity && data) {
        dbData[entity] = data;
      }
    });

    return dbData;
  };

  const createMockInterestParty = (
    contactId: number,
    partyRole: number,
    interestPercent: number
  ): InterestParty => ({
    ContactID: contactId,
    PartyRole: partyRole,
    InterestPercent: interestPercent,
    Name: `Contact ${contactId}`,
    PolicyID: 123,
    PartyRoleText: 'Agent',
  });

  const createMockPolicy = (interestParties: InterestParty[] = []): Policy => ({
    InterestParties: {
      InterestParty: interestParties,
    },
    UniqueID: 'POL123',
    PolicyNumber: 'POL123',
    WriteState: 1,
  });

  describe('getInterestParty', () => {
    it('should return filtered interest parties with InterestPercent and ContactID', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
        createMockInterestParty(2, Role.WRITING, 30),
        { ContactID: 3, PartyRole: Role.SALES_REP }, // Missing InterestPercent
        { InterestPercent: 20, PartyRole: Role.SALES_REP }, // Missing ContactID
        createMockInterestParty(4, Role.ADDITIONAL_WRITING_ADVISOR, 20),
      ];

      const policy = createMockPolicy(interestParties);
      const result = worker.getInterestParty(policy);

      expect(result).toHaveLength(3);
      expect(result).toEqual([
        expect.objectContaining({ ContactID: 1, InterestPercent: 50 }),
        expect.objectContaining({ ContactID: 2, InterestPercent: 30 }),
        expect.objectContaining({ ContactID: 4, InterestPercent: 20 }),
      ]);
    });

    it('should return empty array when no InterestParties exist', () => {
      const policy: Policy = {
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
      };

      const result = worker.getInterestParty(policy);

      expect(result).toEqual([]);
    });

    it('should return empty array when InterestParties.InterestParty is undefined', () => {
      const policy: Policy = {
        InterestParties: {
          InterestParty: undefined,
        },
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
      };

      const result = worker.getInterestParty(policy);

      expect(result).toEqual([]);
    });

    it('should handle policy with undefined datum', () => {
      const result = worker.getInterestParty(undefined as any);

      expect(result).toEqual([]);
    });

    it('should filter out interest parties missing required fields', () => {
      const interestParties: InterestParty[] = [
        { ContactID: 1 }, // Missing InterestPercent
        { InterestPercent: 50 }, // Missing ContactID
        { PartyRole: Role.PRIMARY_AGENT }, // Missing both
        createMockInterestParty(2, Role.WRITING, 30), // Valid
      ];

      const policy = createMockPolicy(interestParties);
      const result = worker.getInterestParty(policy);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(
        expect.objectContaining({ ContactID: 2, InterestPercent: 30 })
      );
    });
  });

  describe('getContactsFromInterestParties', () => {
    it('should return contact str_ids for syncing roles that exist in dbData', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
        createMockInterestParty(2, Role.WRITING, 30),
        createMockInterestParty(3, Role.SALES_REP, 20),
        createMockInterestParty(4, Role.ADDITIONAL_WRITING_ADVISOR, 10),
        createMockInterestParty(5, Role.CASE_MANAGER, 5), // Not a syncing role
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['2', createMockContact(2, 'contact_002', 'Agent', 'Two')],
          ['3', createMockContact(3, 'contact_003', 'Agent', 'Three')],
          ['4', createMockContact(4, 'contact_004', 'Agent', 'Four')],
          ['5', createMockContact(5, 'contact_005', 'Agent', 'Five')],
        ]),
      });

      const result = worker.getContactsFromInterestParties(policy, mockDbData);

      expect(result).toHaveLength(4);
      expect(result).toEqual([
        'contact_001',
        'contact_002',
        'contact_003',
        'contact_004',
      ]);
    });

    it('should handle missing contacts in dbData', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
        createMockInterestParty(99, Role.WRITING, 30), // Contact not in dbData
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
      });

      const result = worker.getContactsFromInterestParties(policy, mockDbData);

      expect(result).toHaveLength(1);
      expect(result).toEqual(['contact_001']);
    });

    it('should return empty array when no dbData is provided', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
      ];

      const policy = createMockPolicy(interestParties);
      const result = worker.getContactsFromInterestParties(policy);

      expect(result).toEqual([]);
    });

    it('should return empty array when no interest parties exist', () => {
      const policy = createMockPolicy([]);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
      });

      const result = worker.getContactsFromInterestParties(policy, mockDbData);

      expect(result).toEqual([]);
    });

    it('should filter by syncing roles only', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50), // Syncing role
        createMockInterestParty(2, Role.CASE_MANAGER, 30), // Not a syncing role
        createMockInterestParty(3, 9999, 20), // Unknown role
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['2', createMockContact(2, 'contact_002', 'Agent', 'Two')],
          ['3', createMockContact(3, 'contact_003', 'Agent', 'Three')],
        ]),
      });

      const result = worker.getContactsFromInterestParties(policy, mockDbData);

      expect(result).toHaveLength(1);
      expect(result).toEqual(['contact_001']);
    });
  });

  describe('getSplitsForPolicy', () => {
    it('should return splits for contacts that exist in dbData', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
        createMockInterestParty(2, Role.WRITING, 30),
        createMockInterestParty(3, Role.SALES_REP, 20),
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['2', createMockContact(2, 'contact_002', 'Agent', 'Two')],
          ['3', createMockContact(3, 'contact_003', 'Agent', 'Three')],
        ]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toEqual({
        contact_001: 50,
        contact_002: 30,
        contact_003: 20,
      });
    });

    it('should exclude contacts not found in dbData', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
        createMockInterestParty(99, Role.WRITING, 30), // Contact not in dbData
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toEqual({
        contact_001: 50,
      });
    });

    it('should return undefined when no valid splits exist', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(99, Role.PRIMARY_AGENT, 50), // Contact not in dbData
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toBeUndefined();
    });

    it('should return undefined when no dbData is provided', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50),
      ];

      const policy = createMockPolicy(interestParties);
      const result = worker.getSplitsForPolicy(policy);

      expect(result).toBeUndefined();
    });

    it('should return undefined when no interest parties exist', () => {
      const policy = createMockPolicy([]);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
        ]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toBeUndefined();
    });

    it('should handle empty splits object', () => {
      const policy = createMockPolicy([]);
      const mockDbData = createMockDBData({
        contacts: new Map([]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toBeUndefined();
    });

    it('should handle mixed valid and invalid interest parties', () => {
      const interestParties: InterestParty[] = [
        createMockInterestParty(1, Role.PRIMARY_AGENT, 50), // Valid
        { ContactID: 2, PartyRole: Role.WRITING }, // Missing InterestPercent
        createMockInterestParty(3, Role.SALES_REP, 30), // Valid
      ];

      const policy = createMockPolicy(interestParties);
      const mockDbData = createMockDBData({
        contacts: new Map([
          ['1', createMockContact(1, 'contact_001', 'Agent', 'One')],
          ['2', createMockContact(2, 'contact_002', 'Agent', 'Two')],
          ['3', createMockContact(3, 'contact_003', 'Agent', 'Three')],
        ]),
      });

      const result = worker.getSplitsForPolicy(policy, mockDbData);

      expect(result).toEqual({
        contact_001: 50,
        contact_003: 30,
      });
    });
  });

  describe('bgaAccountPolicyTransform', () => {
    const BGA_CASE_MANAGER_STR_ID = '2Cbzq_yQza-VshprIi6vn';

    const createMockPolicyDatum = (
      overrides?: Partial<PolicyDatum>
    ): PolicyDatum => ({
      sync_id: 'test-sync-id',
      agent_name: 'Test Agent',
      writing_carrier_name: 'Test Carrier',
      customer_name: 'Test Customer',
      effective_date: new Date('2023-01-01'),
      policy_id: 'POL123',
      policy_status: 'Active',
      contacts: ['existing-contact-1', 'existing-contact-2'],
      ...overrides,
    });

    const createMockPolicyWithCaseManager = (
      hasCaseManager: boolean = true
    ): Policy => ({
      UniqueID: 'POL123',
      PolicyNumber: 'POL123',
      WriteState: 1,
      InterestParties: {
        InterestParty: hasCaseManager
          ? [
              {
                ContactID: 1,
                PartyRole: Role.PRIMARY_AGENT,
                InterestPercent: 50,
                Name: 'Agent One',
                PolicyID: 123,
                PartyRoleText: 'Agent',
                Contact: {
                  Agent: {
                    CaseManagerID: 123,
                  },
                },
              },
            ]
          : [
              {
                ContactID: 1,
                PartyRole: Role.PRIMARY_AGENT,
                InterestPercent: 50,
                Name: 'Agent One',
                PolicyID: 123,
                PartyRoleText: 'Agent',
              },
            ],
      },
    });

    beforeEach(() => {
      // Reset the worker's task to ensure clean state
      worker.task = undefined;
    });

    it('should add case manager contact for BGA account when case manager exists', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy = createMockPolicyWithCaseManager(true);

      // Mock task with BGA account
      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result).not.toBe(mockDatum); // Should return a new object
      expect(result.contacts).toEqual([
        'existing-contact-1',
        'existing-contact-2',
        BGA_CASE_MANAGER_STR_ID,
      ]);
      expect(result.sync_id).toBe(mockDatum.sync_id);
      expect(result.agent_name).toBe(mockDatum.agent_name);
    });

    it('should return datum unchanged for BGA account when no case manager exists', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy = createMockPolicyWithCaseManager(false);

      // Mock task with BGA account
      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result).toEqual(mockDatum);
      expect(result).toBe(mockDatum);
    });

    it('should handle datum with no existing contacts', () => {
      const mockDatum = createMockPolicyDatum({ contacts: undefined });
      const mockPolicy = createMockPolicyWithCaseManager(true);

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result.contacts).toEqual([BGA_CASE_MANAGER_STR_ID]);
    });

    it('should handle datum with empty contacts array', () => {
      const mockDatum = createMockPolicyDatum({ contacts: [] });
      const mockPolicy = createMockPolicyWithCaseManager(true);

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result.contacts).toEqual([BGA_CASE_MANAGER_STR_ID]);
    });

    it('should handle policy with undefined InterestParties', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy: Policy = {
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
        InterestParties: undefined,
      };

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result).toEqual(mockDatum);
      expect(result).toBe(mockDatum);
    });

    it('should handle policy with undefined InterestParty array', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy: Policy = {
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
        InterestParties: { InterestParty: undefined },
      };

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result).toEqual(mockDatum);
      expect(result).toBe(mockDatum);
    });

    it('should handle policy with empty InterestParty array', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy: Policy = {
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
        InterestParties: { InterestParty: [] },
      };

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result).toEqual(mockDatum);
      expect(result).toBe(mockDatum);
    });

    it('should handle multiple interest parties with mixed case manager presence', () => {
      const mockDatum = createMockPolicyDatum();
      const mockPolicy: Policy = {
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
        InterestParties: {
          InterestParty: [
            {
              ContactID: 1,
              PartyRole: Role.PRIMARY_AGENT,
              InterestPercent: 50,
              Name: 'Agent One',
              PolicyID: 123,
              PartyRoleText: 'Agent',
              // No Contact.Agent.CaseManagerID
            },
            {
              ContactID: 2,
              PartyRole: Role.WRITING,
              InterestPercent: 30,
              Name: 'Agent Two',
              PolicyID: 123,
              PartyRoleText: 'Agent',
              Contact: {
                Agent: {
                  CaseManagerID: 456,
                },
              },
            },
          ],
        },
      };

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result.contacts).toEqual([
        'existing-contact-1',
        'existing-contact-2',
        BGA_CASE_MANAGER_STR_ID,
      ]);
    });

    it('should not duplicate case manager contact if already present', () => {
      const mockDatum = createMockPolicyDatum({
        contacts: ['existing-contact-1', BGA_CASE_MANAGER_STR_ID],
      });
      const mockPolicy = createMockPolicyWithCaseManager(true);

      worker.task = {
        account: { account_id: AccountIds.BGA },
      } as any;

      const result = worker.bgaAccountPolicyTransform(mockDatum, mockPolicy);

      expect(result.contacts).toEqual([
        'existing-contact-1',
        BGA_CASE_MANAGER_STR_ID,
        BGA_CASE_MANAGER_STR_ID, // This will be duplicated - the current implementation doesn't check for duplicates
      ]);
    });
  });

  describe('Extensible Account Transformations', () => {
    const BGA_CASE_MANAGER_STR_ID = '2Cbzq_yQza-VshprIi6vn';

    const createMockPolicyDatum = (
      overrides?: Partial<PolicyDatum>
    ): PolicyDatum => ({
      sync_id: 'test-sync-id',
      agent_name: 'Test Agent',
      writing_carrier_name: 'Test Carrier',
      customer_name: 'Test Customer',
      effective_date: new Date('2023-01-01'),
      policy_id: 'POL123',
      policy_status: 'Active',
      contacts: ['existing-contact-1'],
      ...overrides,
    });

    const createMockPolicyWithCaseManager = (
      hasCaseManager: boolean = true
    ): Policy => ({
      UniqueID: 'POL123',
      PolicyNumber: 'POL123',
      WriteState: 1,
      InterestParties: {
        InterestParty: hasCaseManager
          ? [
              {
                ContactID: 1,
                PartyRole: Role.PRIMARY_AGENT,
                InterestPercent: 50,
                Name: 'Agent One',
                PolicyID: 123,
                PartyRoleText: 'Agent',
                Contact: {
                  Agent: {
                    CaseManagerID: 123,
                  },
                },
              },
            ]
          : [],
      },
    });

    beforeEach(() => {
      worker.task = undefined;
    });

    describe('applyAccountSpecificTransforms', () => {
      it('should apply BGA transformation when account is BGA', () => {
        const mockDatum = createMockPolicyDatum();
        const mockPolicy = createMockPolicyWithCaseManager(true);

        worker.task = {
          account: { account_id: AccountIds.BGA },
        } as any;

        // Access the private method for testing
        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result.contacts).toEqual([
          'existing-contact-1',
          BGA_CASE_MANAGER_STR_ID,
        ]);
      });

      it('should return datum unchanged for accounts without transformers', () => {
        const mockDatum = createMockPolicyDatum();
        const mockPolicy = createMockPolicyWithCaseManager(true);

        worker.task = {
          account: { account_id: AccountIds.WORLD_CHANGERS },
        } as any;

        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result).toEqual(mockDatum);
        expect(result).toBe(mockDatum);
      });

      it('should return datum unchanged when no account is present', () => {
        const mockDatum = createMockPolicyDatum();
        const mockPolicy = createMockPolicyWithCaseManager(true);

        worker.task = undefined;

        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result).toEqual(mockDatum);
        expect(result).toBe(mockDatum);
      });

      it('should handle unknown account IDs gracefully', () => {
        const mockDatum = createMockPolicyDatum();
        const mockPolicy = createMockPolicyWithCaseManager(true);

        worker.task = {
          account: { account_id: 'unknown-account-id' },
        } as any;

        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result).toEqual(mockDatum);
        expect(result).toBe(mockDatum);
      });
    });
    describe('brokerCentralsPolicyTransform', () => {
      let worker: SmartOfficeWorker;

      beforeEach(() => {
        worker = new SmartOfficeWorker();
      });

      const createPolicyDatum = (
        overrides?: Partial<PolicyDatum>
      ): PolicyDatum => ({
        sync_id: 'test-sync-id',
        policy_id: 'POL123',
        product_name: 'Product Name',
        writing_carrier_name: 'Carrier Name',
        payment_mode: 'Annual',
        commissionable_premium_amount: 1000,
        policy_status: 'Active',
        geo_state: 'CA',
        product_sub_type: 'TypeA',
        issue_age: 45,
        agent_name: 'Agent Name',
        product_type: 'TypeB',
        customer_name: 'Customer Name',
        contacts_split: { contact_001: 50 },
        effective_date: new Date('2023-01-01'),
        contacts: ['contact_001'],
        ...overrides,
      });

      const createPolicy = (
        pendingCase?: Partial<Policy['PendingCase']>
      ): Policy => ({
        UniqueID: 'POL123',
        PolicyNumber: 'POL123',
        WriteState: 1,
        PendingCase: {
          CommPrem: pendingCase?.CommPrem,
          CommAnnPrem: pendingCase?.CommAnnPrem,
          TeamText: pendingCase?.TeamText,
        },
      });
      const dbData = {} as DBData;

      it('Given PendingCase with CommPrem and CommAnnPrem, should override premium fields', () => {
        const datum = createPolicyDatum({
          premium_amount: 500,
          commissionable_premium_amount: 800,
        });
        const policy = createPolicy({ CommPrem: 1500, CommAnnPrem: 2500 });

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(1500);
        expect(result.commissionable_premium_amount).toBe(2500);
      });

      it('Given PendingCase with only CommPrem, should override premium_amount only', () => {
        const datum = createPolicyDatum({
          premium_amount: 500,
          commissionable_premium_amount: 800,
        });
        const policy = createPolicy({ CommPrem: 2000 });

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(2000);
        expect(result.commissionable_premium_amount).toBe(undefined);
      });

      it('Given PendingCase with only CommAnnPrem, should override commissionable_premium_amount only', () => {
        const datum = createPolicyDatum({
          premium_amount: 500,
          commissionable_premium_amount: 800,
        });
        const policy = createPolicy({ CommAnnPrem: 3000 });

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(undefined);
        expect(result.commissionable_premium_amount).toBe(3000);
      });

      it('Given PendingCase with no CommPrem or CommAnnPrem, should not override premium fields', () => {
        const datum = createPolicyDatum({
          premium_amount: 500,
          commissionable_premium_amount: 800,
        });
        const policy = createPolicy();

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(undefined);
        expect(result.commissionable_premium_amount).toBe(undefined);
      });

      it('Given undefined PendingCase, should not override premium fields', () => {
        const datum = createPolicyDatum({
          premium_amount: 500,
          commissionable_premium_amount: 800,
        });
        const policy: Policy = {
          UniqueID: 'POL123',
          PolicyNumber: 'POL123',
          WriteState: 1,
          PendingCase: undefined,
        };

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(undefined);
        expect(result.commissionable_premium_amount).toBe(undefined);
      });

      it('Given datum with no premium fields, should set them if PendingCase exists', () => {
        const datum = createPolicyDatum({});
        delete (datum as any).premium_amount;
        delete (datum as any).commissionable_premium_amount;
        const policy = createPolicy({ CommPrem: 1234, CommAnnPrem: 5678 });

        const result = worker.brokerCentralsPolicyTransform(
          datum,
          policy,
          dbData
        );

        expect(result.premium_amount).toBe(1234);
        expect(result.commissionable_premium_amount).toBe(5678);
      });
    });
    describe('Extensibility Examples', () => {
      it('should demonstrate how to add new account transformers', () => {
        const mockDatum = createMockPolicyDatum({ contacts: [] });
        const mockPolicy: Policy = {
          UniqueID: 'POL123',
          PolicyNumber: 'POL123',
          WriteState: 1,
          PlanType: 'SpecialPlan',
        };

        // Example: Add a custom transformer for testing
        const customTransformer = (
          datum: PolicyDatum,
          data: Policy
        ): PolicyDatum => {
          if (data.PlanType === 'SpecialPlan') {
            return {
              ...datum,
              contacts: [...(datum?.contacts ?? []), 'custom-special-rep'],
            };
          }
          return datum;
        };

        // Simulate adding the transformer to the registry
        (worker as any).accountPolicyTransformers.set(
          'custom-account',
          customTransformer
        );

        worker.task = {
          account: { account_id: 'custom-account' },
        } as any;

        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result.contacts).toEqual(['custom-special-rep']);

        // Clean up
        (worker as any).accountPolicyTransformers.delete('custom-account');
      });

      it('should show how transformers can modify different policy fields', () => {
        const mockDatum = createMockPolicyDatum({
          commissionable_premium_amount: 1000,
          notes: 'Original note',
        });
        const mockPolicy: Policy = {
          UniqueID: 'POL123',
          PolicyNumber: 'POL123',
          WriteState: 5, // California
        };

        // Example: Premium adjustment transformer
        const premiumAdjustmentTransformer = (
          datum: PolicyDatum,
          data: Policy
        ): PolicyDatum => {
          if (data.WriteState === 5 && datum.commissionable_premium_amount) {
            return {
              ...datum,
              commissionable_premium_amount:
                datum.commissionable_premium_amount * 0.95,
              notes: (datum.notes ?? '') + ' [CA Premium Adjusted]',
            };
          }
          return datum;
        };

        (worker as any).accountPolicyTransformers.set(
          'premium-adjust-account',
          premiumAdjustmentTransformer
        );

        worker.task = {
          account: { account_id: 'premium-adjust-account' },
        } as any;

        const result = (worker as any).applyAccountSpecificTransforms(
          mockDatum,
          mockPolicy
        );

        expect(result.commissionable_premium_amount).toBe(950); // 1000 * 0.95
        expect(result.notes).toBe('Original note [CA Premium Adjusted]');

        // Clean up
        (worker as any).accountPolicyTransformers.delete(
          'premium-adjust-account'
        );
      });
    });
  });
});
