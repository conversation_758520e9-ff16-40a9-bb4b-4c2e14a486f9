import { Prisma, PrismaClient } from '@prisma/client';

import { PaginationInput, PrismaPaginationParams } from '@/types';

const prisma = new PrismaClient({ errorFormat: 'pretty' });

type CalculateSkipAndTakeInput = PaginationInput & {
  paginationType?: '0_BASED' | '1_BASED';
};

/**
 * Calculates pagination parameters for database queries.
 *
 * Given a pagination input containing `page` and `limit`, this function
 * returns an object with `skip` and `take` properties suitable for use with Prisma's
 * pagination options. If the arguments are invalid or missing, it defaults to page 1 and omits the limit.
 *
 * @param input - Object containing:
 *   - `page`: The current page number (1-based). Invalid or missing values default to 1.
 *   - `limit`: The number of items per page. Invalid or missing values will be omitted.
 *
 * @returns An object with `skip` (number of items to skip) and `take` (number of items to take).
 *
 * @example
 * ```
 * calculateSkipAndTake({ page: 2, limit: 10 });
 * // returns: { skip: 10, take: 10 }
 * ``
 */
const calculateSkipAndTake = (
  input: CalculateSkipAndTakeInput
): PrismaPaginationParams => {
  const DEFAULT_PAGE = 1;

  // Parse and validate inputs
  const page = Number(input.page);
  const limit = Number(
    Array.isArray(input.limit) ? input.limit[0] : input.limit
  );

  // Use defaults if invalid (e.g., NaN, non-numeric, negative values)
  let sanitizedPage = !isNaN(page) && page > 0 ? page + 1 : DEFAULT_PAGE;
  const sanitizedLimit = !isNaN(limit) && limit > 0 ? limit : undefined;

  if (input.paginationType === '1_BASED') {
    sanitizedPage -= 1;
  }

  const skip =
    sanitizedPage && sanitizedLimit
      ? (sanitizedPage - 1) * sanitizedLimit
      : undefined;

  return { take: sanitizedLimit, skip };
};

export { calculateSkipAndTake, prisma, Prisma };
