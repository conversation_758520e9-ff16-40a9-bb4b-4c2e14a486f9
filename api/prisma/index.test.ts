import { describe, it, expect } from 'vitest';

import { calculateSkipAndTake } from '.';

describe('calculateSkipAndTake', () => {
  it('Given no input, should return skip undefined and take undefined', () => {
    expect(calculateSkipAndTake({})).toEqual({
      skip: undefined,
      take: undefined,
    });
  });

  describe('(0-based) pagination type', () => {
    it.each([
      { page: -1000 },
      { page: -100 },
      { page: -1 },
      { page: -0 },
      { page: 0 },
    ])(
      'Should return skip 0 for page 1 when pass or not the properly pagination type',
      ({ page }) => {
        const nonPaginationTypeResult = calculateSkipAndTake({
          page,
          limit: 10,
          paginationType: undefined,
        });

        const paginationTypeResult = calculateSkipAndTake({
          page,
          limit: 10,
          paginationType: '0_BASED',
        });

        expect(nonPaginationTypeResult).toEqual({
          skip: 0,
          take: 10,
        });
        expect(paginationTypeResult).toEqual({
          skip: 0,
          take: 10,
        });
      }
    );

    it('Given valid page and limit, should calculate skip and take', () => {
      const PAGE_1 = 0;
      const PAGE_2 = 1;
      const PAGE_3 = 2;
      const PAGE_4 = 3;
      // ...n,

      const limit = 10;

      expect(calculateSkipAndTake({ page: PAGE_1, limit }), 'PAGE_1').toEqual({
        skip: 0,
        take: 10,
      });
      expect(calculateSkipAndTake({ page: PAGE_2, limit }), 'PAGE_2').toEqual({
        skip: 10,
        take: 10,
      });
      expect(calculateSkipAndTake({ page: PAGE_3, limit }), 'PAGE_3').toEqual({
        skip: 20,
        take: 10,
      });
      expect(calculateSkipAndTake({ page: PAGE_4, limit }), 'PAGE_4').toEqual({
        skip: 30,
        take: 10,
      });
    });
  });

  describe('(1-based) pagination type', () => {
    const paginationType = '1_BASED';

    it('Should return skip 0 for page 1 when pass or not the properly pagination type', () => {
      const result = calculateSkipAndTake({
        page: 1,
        limit: 10,
        paginationType,
      });

      expect(result).toEqual({
        skip: 0,
        take: 10,
      });
    });

    it.only('Given valid page and limit, should calculate skip and take', () => {
      const PAGE_1_AS_ZERO = 0;
      const PAGE_1 = 1;
      const PAGE_2 = 2;
      const PAGE_3 = 3;
      const PAGE_4 = 4;
      // ...n,

      const limit = 10;

      expect(
        calculateSkipAndTake({ page: PAGE_1_AS_ZERO, limit, paginationType }),
        'PAGE_1_AS_ZERO'
      ).toEqual({ skip: undefined, take: 10 });
      expect(
        calculateSkipAndTake({ page: PAGE_1, limit, paginationType }),
        'PAGE_1'
      ).toEqual({
        skip: 0,
        take: 10,
      });
      expect(
        calculateSkipAndTake({ page: PAGE_2, limit, paginationType }),
        'PAGE_2'
      ).toEqual({
        skip: 10,
        take: 10,
      });
      expect(
        calculateSkipAndTake({ page: PAGE_3, limit, paginationType }),
        'PAGE_3'
      ).toEqual({
        skip: 20,
        take: 10,
      });
      expect(
        calculateSkipAndTake({ page: PAGE_4, limit, paginationType }),
        'PAGE_4'
      ).toEqual({
        skip: 30,
        take: 10,
      });
    });
  });

  it.each([
    { page: 2, limit: 0 },
    { page: 2, limit: -5 },
    { page: 2, limit: undefined as any },
  ])(
    'Given invalid input as page($page) and limit($limit), should return skip 0',
    (input) => {
      expect(calculateSkipAndTake(input)).toEqual({
        skip: undefined,
        take: undefined,
      });
    }
  );
});
