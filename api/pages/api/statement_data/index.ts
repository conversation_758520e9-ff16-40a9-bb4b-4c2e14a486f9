import BigNumber from 'bignumber.js';
import { AccountIds, DEFAULT_FILTER } from 'common/constants';
import { customViewDefault } from 'common/constants/account_role_settings';
import * as dto from 'common/dto/statement_data';
import { TransactionRate, TransactionStatuses } from 'common/globalTypes';
import dayjs from 'dayjs';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  Catch,
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Put,
  Req,
  Res,
} from 'next-api-decorators';
import * as Sentry from '@sentry/node';
import url from 'url';
import { normalizeBooleanQueryParam } from 'common/helpers/normalizeBooleanQueryParam';

import { container } from '@/ioc';
import { ZodQuery } from '@/lib/decorators';
import { exceptionHandler } from '@/lib/exceptionHandler';
import Formatter from '@/lib/Formatter';
import {
  addReceivableValuesToCommissionData,
  filterFieldOptions,
  hasTransactionRateValues,
} from '@/lib/helpers';
import { processContacts } from '@/lib/helpers/processContacts';
import { withAuth } from '@/lib/middlewares';
import prisma, { prismaClient } from '@/lib/prisma';
import {
  getSelectStatement,
  loadFilterOption,
  queryFieldValues,
} from '@/lib/statement';
import { _postHistoryData } from '@/pages/api/history';
import { CompReportDataHelpers } from '@/pages/api/statement_data/helpers/compReportData.helpers';
import { addCompCalcToCommissionData } from '@/pages/api/statement_data/helpers/statementDataCompCalc.helpers';
import { getMappedSubfieldForOrderBy } from '@/pages/api/statement_data/helpers/statementDataFilters.helpers';
import { calculateSkipAndTake, Prisma } from '@/prisma';
import { ContactService, IContactService } from '@/services/contact';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { SavedReportsService } from '@/services/saved-reports';
import { SettingsService } from '@/services/settings';
import { StatementFilterService } from '@/services/statement/filter';
import {
  DataStates,
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
  Roles,
} from '@/types';
import { FieldsModel } from '@/types/enum/fields-model.enum';
import {
  filterZeroCommissionAndPayoutValues,
  hasAgentCommission,
} from './helpers/commissionsHelper';
import { StatementDataService } from '@/services/statement/statement-data';
import { BaseHandler } from '@/lib/baseHandler';

@Catch(exceptionHandler)
class Handler extends BaseHandler {
  private statementDataService: StatementDataService;
  constructor() {
    super();

    this.statementDataService = container.get(StatementDataService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.COMMISSIONS)
  async getStatementData(
    @Req()
    req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @(ZodQuery(dto.StatementDataQueryDtoSchema)())
    query: dto.StatementDataQueryDto
  ) {
    return getStatementData(req, res, query);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.COMMISSIONS)
  async addStatementData(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return this.statementDataService.addStatementData({
      body: req.body,
      uid: req.uid,
      account_id: req.account_id,
      ouid: req.ouid,
    });
  }

  @Put()
  @Guard(CrudAction.UPDATE, EntityType.COMMISSIONS)
  async updateStatementData(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return this.statementDataService.updateStatementData({
      body: req.body,
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    });
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.COMMISSIONS)
  async patchStatementData(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return this.statementDataService.updateStatementData({
      body: req.body,
      account_id: req.account_id,
      uid: req.uid,
      ouid: req.ouid,
    });
  }

  @Delete()
  @Guard(CrudAction.DELETE, 'Commissions')
  async deleteStatementData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return deleteStatementData(req, res);
  }
}

const warnOnStatusMismatch = (commissionData: any[]) => {
  commissionData.forEach((item, idx) => {
    const details = item.accounting_transaction_details;
    if (!details || !Array.isArray(details)) return;

    details.forEach((detail, detailIdx) => {
      if (
        detail.status !== undefined &&
        item.agent_commissions_status !== undefined &&
        detail.status !== item.agent_commissions_status
      ) {
        Sentry.captureMessage(
          `Status mismatch: accounting_transaction_details[${detailIdx}].status (${detail.status}) != agent_commissions_status (${item.agent_commissions_status}) for data[0][${idx}]`,
          'warning'
        );
      }
    });
  });
};

export default withAuth(createHandler(Handler));

export const _getStatementData = async (
  req: ExtNextApiRequest,
  query?: dto.StatementDataQueryDto,
  isExportData = false
) => {
  const queryData = query || req.query;
  const {
    // Parameter only sent when exporting producer view csv
    agent_commissions = '',
    tags,
    contacts,
    disableFilter,
    hide_no_payout_calc_commissions = '',
    id,
    incl_dupes = false,
    incl_linked = false,
    incl_zero_commissions = '',
    is_commission_report = false,
    limit,
    orderBy = 'created_at',
    page,
    producer_view = false,
    q = '',
    sort = 'desc',
    is_saved_report,
    flags,
    show_allocated_commissions = false,
  } = queryData;

  if (producer_view) req.role_id = Roles.PRODUCER.toString();

  const filterService = container.get<StatementFilterService>(
    StatementFilterService
  );
  const settingsService = container.get<SettingsService>(SettingsService);
  const contactService = container.get<IContactService>(ContactService);

  // If user is producer, we get the user and contact data and limit results to their data
  const userData = await filterService.getProducerUserData(
    req as ExtAccountInfo,
    !!producer_view,
    contacts
  );

  const contact_str_id = userData?.user_contact[0]?.str_id;

  // Validation/error handling of page, limit, orderBy, sort
  const { take, skip } = calculateSkipAndTake({ page, limit });

  let commissionsAccountSettings;
  if (is_commission_report || normalizeBooleanQueryParam(is_saved_report)) {
    const producerSettings = await settingsService.getSettingsByContact({
      uid: req.uid,
      accountId: req.account_id,
      roleId: Roles.PRODUCER,
      contactStrId: contact_str_id,
    });

    // Get all the fields for the statements model
    // To save the snapshot, so that when the view changes, there will be data available for display
    const fields = await prismaClient.fields.findMany({
      where: {
        model: FieldsModel.STATEMENTS,
        state: DataStates.ACTIVE,
      },
      select: {
        key: true,
      },
    });

    commissionsAccountSettings = {
      pages_settings: {
        ...producerSettings?.pages_settings,
        commissions: fields.map((field) => field.key),
      },
      agent_settings: producerSettings?.agent_settings,
    };
  } else {
    commissionsAccountSettings = await prisma.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: String(req.account_id),
          role_id: parseInt(req.role_id),
          custom_view_name: customViewDefault,
        },
      },
      select: {
        pages_settings: true,
        agent_settings: true,
      },
    });
  }

  // Get the list of contacts that the producer user has access to based on the account agent settings
  let contactAndChildrenStrIds: string[] = [];
  const { agent_settings } = commissionsAccountSettings ?? {};
  const directDownlineDataAccess =
    agent_settings?.directDownlineDataAccess?.commissionsConfig;
  const extendedDownlineDataAccess =
    agent_settings?.extendedDownlineDataAccess?.commissionsConfig;
  const payoutLevelsDataAccess =
    agent_settings?.extendedDownlineDataAccess?.payoutLevels;

  if (contact_str_id) {
    contactAndChildrenStrIds = await filterService.getContactAndChildrenStrIds(
      extendedDownlineDataAccess,
      directDownlineDataAccess,
      contact_str_id
    );

    if (payoutLevelsDataAccess && Array.isArray(payoutLevelsDataAccess)) {
      const childrenGridLevelsStrIds =
        await contactService.getChildenContactStrIdListByGridLevel(
          contact_str_id,
          payoutLevelsDataAccess
        );

      // The payoutLevelsDataAccess setting can be combined with the extendedDownlineDataAccess/directDownlineDataAccess settings, so we need to merge the two results
      contactAndChildrenStrIds = Array.from(
        new Set([...contactAndChildrenStrIds, ...childrenGridLevelsStrIds])
      );
    }
  }

  let currentRecord = null;
  if (id && incl_linked) {
    currentRecord = await prisma.statement_data.findUnique({
      where: { str_id: id },
      select: { parent_id: true, id: true },
    });
  }

  const globalWhere: { AND: any[] } = filterService.getGlobalWhere(
    req.account_id,
    contactAndChildrenStrIds,
    contact_str_id,
    q,
    id,
    !!incl_dupes,
    !!incl_linked,
    currentRecord,
    tags,
    flags,
    !!show_allocated_commissions
  );

  // Get the query params from the url and filter out the ones we want to use for filtering
  const rawUrl = req.url;
  const parsedUrl = url.parse(rawUrl);
  const queryParamNames =
    parsedUrl.query?.split('&')?.map((param) => param.split('=')[0]) ?? [];

  const where = JSON.parse(JSON.stringify(globalWhere));
  const filtersWhere = JSON.parse(JSON.stringify(globalWhere));

  const isProducer = req.role_id.toString() === Roles.PRODUCER.toString();

  const { additionalFilterFields, filterList } =
    await filterService.applyStatementFilter({
      query: queryData,
      where,
      account: req as ExtAccountInfo,
      queryParams: queryParamNames,
      userData,
      contactAndChildrenStrIds,
      isProducer,
    });

  console.time('statement_data');

  // Define the fields to be selected based on the user's account settings
  const selectStatement = await getSelectStatement(commissionsAccountSettings);

  const accountingTransactionDetailsWhere: Prisma.accounting_transaction_detailsWhereInput =
    {
      state: DataStates.ACTIVE,
      ...(is_commission_report && {
        status: {
          equals: TransactionStatuses.DRAFT,
          mode: 'insensitive',
        },
      }),
    };

  const getStatementData = async () => {
    return await prismaClient.statement_data.findManyWithPagination({
      where,
      skip,
      take,
      select: {
        id: true,
        ...selectStatement,
        details: {
          where: {
            state: { not: DataStates.DELETED },
          },
        },
        report: {
          where: { state: { in: ['active', 'grouped'] } },
          include: {
            accounting_transaction_details: {
              select: {
                id: true,
                str_id: true,
                amount: true,
                date: true,
                report_id: true,
                saved_report_id: true,
                statement_id: true,
                status: true,
                transaction_id: true,
                type: true,
                rate: true,
                party: true,
                company_id: true,
                contact: {
                  select: {
                    id: true,
                    str_id: true,
                    first_name: true,
                    last_name: true,
                    email: true,
                  },
                },
                tags: true,
              },
            },
          },
        },
        children_data: { where: { state: { not: DataStates.DELETED } } },
        report_data_id: true,
        accounting_transaction_details: {
          where: accountingTransactionDetailsWhere,
        },
        document: {
          select: {
            filename: true,
          },
        },
      },
      orderBy: [
        getMappedSubfieldForOrderBy({
          field: orderBy,
          sort,
        }),
        { id: 'asc' },
      ],
    });
  };

  const promises = [
    getStatementData(),
    prisma.statement_data.count({
      where,
    }),
    prisma.statement_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        payment_date: true,
      },
      orderBy: [{ ['payment_date']: 'asc' }],
    }),
    prisma.statement_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        payment_date: true,
      },
      orderBy: [{ ['payment_date']: 'desc' }],
    }),
    disableFilter ? {} : queryFieldValues(req.account_id, filterList, where),
  ];

  if (additionalFilterFields.length > 0) {
    promises.push(
      disableFilter
        ? {}
        : queryFieldValues(req.account_id, additionalFilterFields, filtersWhere)
    );
  }
  promises.push(
    prisma.statement_data.aggregate({
      where,
      _sum: {
        commissionable_premium_amount: true,
        commission_amount: true,
        commission_paid_amount: true,
        fees: true,
        customer_paid_premium_amount: true,
        premium_amount: true,
      },
    })
  );

  const getAgentCommissionsData = async () => {
    const agentCommissionsWhere = {
      ...where,
      agent_commissions: { not: null },
    };

    return await prisma.statement_data.findManyWithPagination({
      where: agentCommissionsWhere,
      select: { id: true, agent_commissions: true },
      orderBy: { id: 'asc' },
    });
  };

  promises.push(
    // TODO: Figure out how to do this for a subset of agents
    // prisma.$queryRaw`${agentCommissionsQuery}`,
    getAgentCommissionsData()
  );

  const data: any[] = await Promise.all(promises);

  if (additionalFilterFields.length > 0) {
    data[4] = Object.assign({}, data[4], data[5]);
    data.splice(5, 1);
  }

  const { pages_settings } = commissionsAccountSettings ?? {};
  const commissionFields = pages_settings?.commissions?.fields ?? [];

  const hasCompCalcFields =
    commissionFields.includes('comp_calc') ||
    commissionFields.includes('comp_calc_log') ||
    commissionFields.includes('comp_calc_status');

  for (const item of data[0]) {
    if (hasCompCalcFields) {
      addCompCalcToCommissionData({
        commissionDataItem: item,
        includeCompCalc: commissionFields.includes('comp_calc'),
        includeCompCalcLog: commissionFields.includes('comp_calc_log'),
        includeCompCalcStatus: commissionFields.includes('comp_calc_status'),
      });
    }

    addReceivableValuesToCommissionData(item);

    if (!hasTransactionRateValues(item)) {
      addReceivableValuesToCommissionData(item?.report);
      [
        TransactionRate.AGENT,
        TransactionRate.AGENCY,
        TransactionRate.POLICY,
      ].forEach((rate) => {
        item[rate] = item?.report?.[rate];
      });
    }

    if (!incl_zero_commissions) {
      filterZeroCommissionAndPayoutValues(item);
    }
  }

  // We have to do this here because Prisma doesn't support filtering empty json objects {} in the query
  if (hide_no_payout_calc_commissions) {
    data[0] = data[0].filter((item) =>
      hasAgentCommission(item, !!incl_zero_commissions)
    );
  }

  // Producer can only see their own commissions
  if (parseInt(req.role_id) === Roles.PRODUCER) {
    const effectiveContactStr = determineEffectiveContactStr(
      contact_str_id,
      contactAndChildrenStrIds,
      contacts,
      agent_commissions
    );

    const effectiveContactIds =
      await contactService.getContactIdsByStrIdList(effectiveContactStr);

    data[0].forEach((item) => {
      if (!effectiveContactStr) {
        console.warn(
          'Getting commission data for role, but no contact id found.'
        );
        return;
      }

      processFields(
        item,
        effectiveContactStr,
        effectiveContactIds,
        is_commission_report
      );
    });

    data[data.length - 1].forEach((item) => {
      delete item.agent_commissions.total;
      item.agent_commissions = filterEffectiveContactStr(
        item.agent_commissions,
        effectiveContactStr
      );
    });
  }

  // Totals comes in two queries b/c reasons, combine them into one here
  // TODO: Find a more efficient way to do this
  const totals = data[5]._sum;
  const contactStrId = userData?.user_contact[0]?.str_id;

  const effectiveContactStr = contactStrId
    ? contactAndChildrenStrIds.length > 0
      ? contactAndChildrenStrIds
      : contactStrId
    : contacts && typeof contacts === 'string'
      ? contacts
      : agent_commissions && typeof agent_commissions === 'string'
        ? agent_commissions
        : contactAndChildrenStrIds.length > 0
          ? contactAndChildrenStrIds
          : contactStrId;

  const processAgentCommissions = (acc, item, effectiveContactStr) => {
    Object.entries(item.agent_commissions).forEach(([agent, value]) => {
      if (agent === 'total') return;
      if (agent === effectiveContactStr)
        acc[agent] = new BigNumber(acc[agent] || 0)
          .plus(new BigNumber((value as number) || 0))
          .toNumber();
    });
    return acc;
  };

  const processAllAgentCommissions = (acc, item) => {
    if (!item.agent_commissions) {
      return acc;
    }

    Object.entries(item.agent_commissions).forEach(([k, v]) => {
      if (k === 'total') return;
      acc[k] = new BigNumber(acc[k] || 0)
        .plus(new BigNumber((v as number) || 0))
        .toNumber();
    });
    return acc;
  };

  const calculateAgentCommissionTotals = (
    data,
    effectiveContactStr,
    roleId
  ) => {
    const isAdmin = parseInt(roleId) === Roles.ACCOUNT_ADMIN;

    if (isAdmin) {
      return data[6].reduce(processAllAgentCommissions, {});
    }

    if (Array.isArray(effectiveContactStr)) {
      return data[6].reduce(processAllAgentCommissions, {});
    }

    return data[6].reduce(
      (acc, item) => processAgentCommissions(acc, item, effectiveContactStr),
      {}
    );
  };
  const agentCommissionTotals = calculateAgentCommissionTotals(
    data,
    effectiveContactStr,
    req.role_id
  );
  totals.agent_commissions = agentCommissionTotals;
  data[5] = totals;

  if (is_commission_report && req.account_id === AccountIds.TRANSGLOBAL) {
    data[0] = CompReportDataHelpers.addCustomTagsToStatementData(
      data[0],
      contactStrId
    );
  }

  // TODO: Find a better way to do this.
  // Remove from Prisma extension
  const commissionData = data[0];
  const hasAgentCommissionPayoutRate = commissionData.some(
    (item) => Object.keys(item.agent_commission_payout_rate ?? {}).length > 0
  );
  if (hasAgentCommissionPayoutRate) {
    const agents = commissionData.reduce((acc, item) => {
      if (item.agent_commissions) {
        Object.keys(item.agent_commissions)
          .filter((k) => k !== 'total')
          .forEach((k) => {
            if (!acc.includes(k)) {
              acc.push(k);
            }
          });
      }
      return acc;
    }, []);
    const salesReps = await prisma.contacts.findMany({
      where: {
        account_id: req.account_id,
        str_id: { in: agents },
        type: { equals: 'Sales rep' },
      },
      select: { str_id: true },
    });
    const salesRepStrIds = salesReps.map((sr) => sr.str_id);
    if (salesRepStrIds.length > 0)
      data[0] = commissionData.map((item) => {
        if (item.agent_commission_payout_rate) {
          const nonSalesRepCommissions = Object.entries(
            item.agent_commissions ?? {}
          ).reduce((acc, [k, v]) => {
            acc = acc.plus(
              new BigNumber(
                !salesRepStrIds.includes(k) && k !== 'total' ? +v : 0
              )
            );
            return acc;
          }, new BigNumber(0));
          salesRepStrIds.forEach((sr) => {
            if (typeof item.agent_commission_payout_rate === 'object') {
              item.agent_commission_payout_rate[sr] =
                item.agent_commission_payout_rate?.[sr] !== undefined &&
                item.agent_commissions?.[sr] !== undefined
                  ? BigNumber(item.agent_commissions[sr])
                      .div(
                        BigNumber(item.commission_amount).minus(
                          nonSalesRepCommissions
                        )
                      )
                      .multipliedBy(100)
                      .dp(3)
                      .toNumber()
                  : undefined;
            }
          });
        }
        return item;
      });
  }

  // If exporting data, we need to get all the contacts for the agent commissions
  if (isExportData) {
    await processContacts(data[0]);
  }

  console.timeEnd('statement_data');

  return data;
};

const getStatementData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  query: dto.StatementDataQueryDto
) => {
  query.disableFilter = true;
  req.logger.profile('Getting statement data');

  let data;
  // If comp_report_id is present, get the statement ids from saved report snapshot data and filter based on that
  if (query.comp_report_id) {
    const savedReportsService =
      container.get<SavedReportsService>(SavedReportsService);

    const repdataIds = await savedReportsService.getReportSnapshotDataIds(
      query.comp_report_id,
      req.account_id
    );

    query.statement_data_ids = repdataIds;
    data = await _getStatementData(req, query);
  } else {
    data = await _getStatementData(req, query);
  }

  const _fieldOptions = data[4];
  let fieldOptions = {
    document_id: undefined,
    contacts: undefined,
    payment_date_start: data[2]?.payment_date,
    payment_date_end: data[3]?.payment_date,
    ..._fieldOptions,
  };

  // Can be removed in the future, just in case that we need to revert back to the old way of getting field options
  if (!query.disableFilter) {
    fieldOptions.contacts = Array.from(
      new Set(fieldOptions.contacts?.flat() ?? [])
    );

    // Add contact names to filters
    const contactIds = fieldOptions.contacts;
    const contacts = await prisma.contacts.findMany({
      where: { str_id: { in: contactIds } },
      select: {
        str_id: true,
        first_name: true,
        last_name: true,
        email: true,
      },
    });
    const contactsMap = Object.fromEntries(
      contacts.map((contact) => [
        contact.str_id,
        Formatter.contact(contact, { account_id: req.account_id }),
      ])
    );
    fieldOptions.contacts = [
      { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
      ...contactIds.map((id) => ({
        id,
        name: contactsMap[id] || id,
      })),
    ];
    fieldOptions.contacts.sort((a, b) => a.name.localeCompare(b.name));

    // Add document name to filters
    const documentIds = fieldOptions.document_id;
    const documents = await prisma.documents.findMany({
      where: { str_id: { in: documentIds } },
      select: {
        str_id: true,
        file_path: true,
        filename: true,
      },
    });
    const documentsMap = Object.fromEntries(
      documents.map((document) => [
        document.str_id,
        `${document?.file_path?.endsWith(document?.filename) ? document?.filename : `${document?.file_path?.split('/')?.pop()?.substring(22)}`}`,
      ])
    );
    fieldOptions.document_id =
      documentIds?.map((id) => ({
        id,
        name: documentsMap[id] || id,
      })) ?? [];
    fieldOptions.document_id.sort((a, b) => a.name.localeCompare(b.name));

    const excludedKeys = ['payment_date_start', 'payment_date_end'];
    const dataObject = data[0] ? data[0][0] : null;
    fieldOptions = filterFieldOptions(dataObject, fieldOptions, excludedKeys);
  }
  const contactStrIds = data[0].reduce((acc, item) => {
    if (!item.contacts || item.contacts.length === 0) return acc;
    return [...acc, ...item.contacts];
  }, []);
  const agents = contactStrIds
    ? await prismaClient.contacts.findMany({
        accountInject: false,
        where: {
          str_id: {
            in: contactStrIds,
          },
        },
        select: {
          id: true,
          str_id: true,
          first_name: true,
          last_name: true,
          middle_name: true,
          company_name: true,
          type: true,
        },
      })
    : [];
  const agentsMap = new Map(agents.map((r) => [r.id, r]));
  // Get commission value from tmp_payout_report, if it exists, add to statement_data, this function is available for fintary admin only
  let commissionData = data[0].map((r) => {
    if (r.report?.accounting_transaction_details) {
      r.report.accounting_transaction_details.forEach((d) => {
        d.agent = agentsMap.get(d.contact_id);
      });
    }
    r.accounting_transaction_details = r.report?.accounting_transaction_details;
    return r;
  });
  // TODO: Need a better way of determining FINTARY_ADMIN. Temporarly inferring from ouid since this is temporary.
  if (
    (parseInt(req.role_id) === Roles.ACCOUNT_ADMIN && req.ouid) ||
    req.account_id === AccountIds.TRANSGLOBAL
  ) {
    req.logger.profile('Profiling: get expected commission data');
    const expectedResults = await prisma.tmp_payout_report.findMany({
      where: {
        policy_number: {
          in: data[0]
            .map((item) =>
              item.payment_date && item.policy_id ? item.policy_id : null
            )
            .filter((item) => item),
        },
      },
    });
    const expectedResultsMap = expectedResults.reduce((acc, ecd) => {
      if (!acc[ecd.policy_number]) {
        acc[ecd.policy_number] = [];
      }
      acc[ecd.policy_number].push(ecd);
      return acc;
    }, {});
    commissionData = data[0].map((item) => {
      const dataFromPayoutReport =
        item.payment_date && item.policy_id
          ? expectedResultsMap[item.policy_id]
              ?.filter(
                (ecd) =>
                  (!ecd?.date_from ||
                    dayjs(item.payment_date)
                      .add(14, 'days')
                      .isAfter(dayjs(ecd.date_from))) &&
                  (!ecd?.date_to ||
                    dayjs(item.payment_date)
                      .add(-14, 'days')
                      .isBefore(dayjs(ecd.date_to)))
              )
              ?.map((ecd) => {
                const status = Object.values(item.agent_commissions ?? {}).find(
                  (commission) =>
                    (+commission)?.toFixed(2) === ecd.payment.toFixed(2)
                );
                return `${status ? '✅' : '⚠️'} ${Formatter.contact(ecd, { account_id: req.account_id })}: $${ecd.payment.toFixed(2)} @ ${ecd.percentage}%, ${ecd.date_from.toLocaleDateString('en-US')}`;
              })
          : undefined;
      return { ...item, expected_result: dataFromPayoutReport };
    });
    req.logger.profile('Profiling: get expected commission data');
  }

  warnOnStatusMismatch(commissionData);

  req.logger.profile('Getting statement data');
  res.json({
    data: commissionData,
    count: data[1],
    fieldOptions,
    totals: data[5],
  });
};

/**
 * Delete statement_data
 */
const deleteStatementData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    await prisma.statement_data.updateMany({
      where: {
        AND: [{ id: { in: ids } }, { account_id: String(req.account_id) }],
      },
      data: {
        state: 'deleted',
      },
    });
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const fieldsToProcess = [
  'contacts',
  'agent_commissions',
  'agent_commission_payout_rate',
  'agent_payout_rate',
  'agent_commissions_status2',
  'agent_payout_rate_override',
  'comp_calc_status',
  'agent_commissions_log',
  'comp_calc_log',
  'comp_calc',
];

/**
 * Processes specific fields of an item based on the effective contact string or IDs.
 */
// TODO: Extract blocks of code into functions for each field for better readability
const processFields = (
  item: any,
  effectiveContactStr: string | string[],
  effectiveContactIds: number[],
  isCommissionReport: string | boolean
) => {
  const isArray = Array.isArray(effectiveContactStr);

  fieldsToProcess.forEach((field) => {
    // The agent_payout_rate_override field can be getted from the report if null
    if (field === 'agent_payout_rate_override' && !item[field]) {
      item[field] = item.report?.agent_payout_rate_override || null;
    }

    if (item[field]) {
      if (field === 'contacts') {
        // Skip filtering for commission reports - keep all contacts
        if (!isCommissionReport && item[field]) {
          const contactList = item[field];

          if (Array.isArray(effectiveContactStr)) {
            item[field] = contactList.filter((contact) =>
              effectiveContactStr.includes(contact)
            );
          } else if (effectiveContactStr) {
            item[field] = contactList.includes(effectiveContactStr)
              ? [effectiveContactStr]
              : [];
          } else {
            item[field] = [];
          }
        }
      } else if (
        // The comp_calc and comp_calc_log fields are objects with contact ids as keys instead of str_ids
        ['comp_calc', 'comp_calc_log', 'comp_calc_status'].includes(field)
      ) {
        item[field] = effectiveContactIds.reduce((acc, id) => {
          if (item[field]?.[id]) {
            acc[id] = item[field][id];
          }
          return acc;
        }, {});
      } else {
        item[field] = (
          isArray
            ? (effectiveContactStr as string[]).reduce((acc, str) => {
                if (item[field]?.[str]) {
                  acc[str] =
                    field === 'agent_commissions'
                      ? new BigNumber(item[field][str]).toNumber()
                      : item[field][str];
                }
                return acc;
              }, {})
            : effectiveContactStr &&
                item[field]?.[effectiveContactStr] !== undefined
              ? { [effectiveContactStr]: item[field][effectiveContactStr] }
              : {}
        ) as any;
      }
    }
  });
};

/**
 * Filters the agent commissions to include only the effective contact string.
 */
const filterEffectiveContactStr = (
  agentCommissions: Record<string, any>,
  effectiveContactStr: string | string[]
): Record<string, any> => {
  if (typeof effectiveContactStr === 'string') {
    return Object.fromEntries(
      Object.entries(agentCommissions).filter(
        ([key]) => key === effectiveContactStr
      )
    );
  } else if (Array.isArray(effectiveContactStr)) {
    return Object.fromEntries(
      Object.entries(agentCommissions).filter(([key]) =>
        effectiveContactStr.includes(key)
      )
    );
  }
  return agentCommissions;
};

/**
 * Determines the effective contact string based on the provided parameters.
 */
const determineEffectiveContactStr = (
  contact_str_id: string | undefined,
  contactAndChildrenStrIds: string[],
  contacts: string | string[] | undefined,
  agent_commissions: string | undefined
): string | string[] | undefined => {
  if (contact_str_id) {
    return contactAndChildrenStrIds.length > 0
      ? contactAndChildrenStrIds
      : contact_str_id;
  }
  if (contacts && typeof contacts === 'string') {
    return contacts;
  }
  if (agent_commissions && typeof agent_commissions === 'string') {
    return agent_commissions;
  }
  return contactAndChildrenStrIds.length > 0
    ? contactAndChildrenStrIds
    : contact_str_id;
};
