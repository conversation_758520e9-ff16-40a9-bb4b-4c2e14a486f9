import * as Sentry from '@sentry/nextjs';

import { withAuth } from '@/lib/middlewares';
import { ExtAccountInfo, ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import agent_production from './agent_production';
import companies from './companies';
import contacts from './contacts';
import mappings from './mappings';
import processors from './processors';
import documents from './documents';
import reconciliation_data from './reconciliation_data';
import report_data from './report_data';
import statement_data from './statement_data';
import extractions from './extractions';
import saved_reports_groups from './saved_reports_groups';
import saved_reports from './saved_reports';
import custom_download from './custom_download';
import { container } from '@/ioc';
import { CommonAction, EntityType } from '@/services/permission/interface';
import { convertModelToVO } from '@/lib/decorators';
import { PermissionService } from '@/services/permission';
import company_products from './company_products';
import customers from './customers';
import data_processing from './data_processing';
import companies_products_options from './companies_products_options';
import contact_groups from './contact_groups';
import calculation from './calculation';
import comp_grids from './comp_grids';
import comp_grids_products from './comp_grids_products';
import comp_grids_levels from './comp_grids_levels';
import comp_grids_criteria from './comp_grids_criteria';
import comp_grids_rates from './comp_grids_rates';
import reconciler_flows from './reconciler_flows';
import accounts_configs from './accounts_configs';

const tables = {
  'statement_data/by_agents': agent_production,
  companies,
  contacts,
  mappings,
  processors,
  documents,
  reconciliation_data,
  report_data,
  statement_data,
  extractions,
  'saved_reports/groups/details': saved_reports_groups,
  saved_reports,
  custom_download,
  'companies/products': company_products,
  customers,
  'v2/data_processing': data_processing,
  'companies/products/options': companies_products_options,
  'contacts/groups': contact_groups,
  calculation,
  'comp-grids': comp_grids,
  'comp-grids/products': comp_grids_products,
  'comp-grids/levels': comp_grids_levels,
  'comp-grids/criteria': comp_grids_criteria,
  'comp-grids/rates': comp_grids_rates,
  reconciler_flows: reconciler_flows,
  'accounts/configs': accounts_configs,
};

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb',
    },
  },
};

const getData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  let { endpoint } = req.query;
  // Remove page & limit to export full data
  delete req.query?.page;
  delete req.query?.limit;

  const permissionService = container.get<PermissionService>(PermissionService);

  const endpointEntityMap = {
    statement_data: EntityType.COMMISSIONS,
    reconciliation_data: EntityType.RECONCILIATION,
    report_data: EntityType.POLICIES,
    contacts: EntityType.AGENTS,
    // There's no data source for contacts/groups right now, so export will be failed for this endpoint
    'contacts/groups': EntityType.AGENTS_GROUPS,
    'statement_data/by_agents': EntityType.AGENTS_PRODUCTION,
    documents: EntityType.DOCUMENTS,
  };
  try {
    endpoint = endpoint.split('?')[0];
    const subject = endpointEntityMap[endpoint];

    if (subject) {
      const account = convertModelToVO(ExtAccountInfo, req);
      await permissionService.getAbilities(account);
      permissionService.canWithError(CommonAction.EXPORT, subject);
    }

    if (!Object.keys(tables).includes(endpoint) || !tables[endpoint]) {
      throw new Error(`Data source not found: ${endpoint}`);
    }

    tables[endpoint](req, res);
  } catch (error) {
    console.error('Error: ', error);
    Sentry.captureException(error);
    res.status(error.statusCode || 500).json({ error: error.message });
  }
};

export default withAuth(handler);
