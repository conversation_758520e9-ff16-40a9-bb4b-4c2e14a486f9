import { Prisma } from '@prisma/client';
import * as Sen<PERSON> from '@sentry/nextjs';
import { dateOrDefault, setNextDay } from 'common/helpers';
import url from 'url';
import { customViewDefault } from 'common/constants/account_role_settings';
import { DEFAULT_FILTER } from 'common/constants';
import { RECONCILIATION_V2_ONLY_FIELDS } from 'common/constants/reconciliation';

import { container } from '@/ioc';
import { exceptionHandler } from '@/lib/exceptionHandler';
import FieldValuesCache from '@/lib/field-values/FieldValuesCache';
import normalizeFieldValues from '@/lib/field-values/normalizeFieldValues';
import {
  filterFieldOptions,
  limitConcurrency,
  loadFieldFilters,
} from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import prisma, { prismaClient } from '@/lib/prisma';
import { PermissionService } from '@/services/permission';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { ExtNextApiRequest, ExtNextApiResponse, Roles } from '@/types';
import { calculateSkipAndTake } from '@/prisma';
import { shouldGetSelectedFields } from '@/lib/helpers/shouldGetSelectedFields';
import {
  checkReconciliationVersion,
  getReconciliationsDataV2,
} from '@/pages/api/reconciliation_data2/getReconciliationData';
import { RECONCILIATION_VERSION } from '@/constants/reconciliation-data';
import { processContacts } from '@/lib/helpers/processContacts';

const filterFieldsCache = new FieldValuesCache('reconciliation_data');

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const permissionService = container.get<PermissionService>(PermissionService);

  await permissionService.getAbilities({
    account_id: req.account_id,
    role_id: req.role_id,
    ouid: req.ouid,
    uid: req.uid,
  });
  try {
    switch (req.method) {
      case 'GET': {
        permissionService.canWithError(
          CrudAction.READ,
          EntityType.RECONCILIATION
        );

        const version = await checkReconciliationVersion(req.account_id);

        if (version === RECONCILIATION_VERSION.V2) {
          const data = await getReconciliationsDataV2(req);
          res.json(data);
        } else {
          await getReconciliations(req, res);
        }
        break;
      }
      // These should generally be generated rather than manually added
      // case 'POST':
      //   await addReconciliations(req, res);
      //   break;
      case 'PUT':
        permissionService.canWithError(
          CrudAction.UPDATE,
          EntityType.RECONCILIATION
        );
        await updateReconciliations(req, res);
        break;
      case 'PATCH':
        permissionService.canWithError(
          CrudAction.UPDATE,
          EntityType.RECONCILIATION
        );
        await bulkUpdateReconciliations(req, res);
        break;
      case 'DELETE':
        permissionService.canWithError(
          CrudAction.DELETE,
          EntityType.RECONCILIATION
        );
        await deleteReconciliations(req, res);
        break;
      default:
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    exceptionHandler(error, req, res);
  }
};

export default withAuth(handler);

/**
 * Base filter options
 */
function loadFilterOption(
  req: ExtNextApiRequest,
  addition: any = null,
  userData: any = null
) {
  const {
    query: { q = '', id = '', reconciliation_id = '' },
  } = req;

  const globalWhere: Prisma.reconciliation_dataWhereInput = {
    AND: [
      { account_id: req.account_id },
      // if userData exists add a new filter based on the user's contact id
      // Also if the user exists, we know that the user is a producer and only want to show the reconciliated data
      ...(userData
        ? [
            {
              contacts: {
                has: userData.user_contact[0].str_id,
              },
              reconciliation_status: 'matched',
            },
          ]
        : []),
      {
        OR: req.query.incl_linked
          ? [
              { agent_name: { contains: q, mode: 'insensitive' } },
              { agent_id: { contains: q, mode: 'insensitive' } },
              { carrier_name: { contains: q, mode: 'insensitive' } },
              { customer_name: { contains: q, mode: 'insensitive' } },
              { group_id: { contains: q, mode: 'insensitive' } },
              { policy_id: { contains: q, mode: 'insensitive' } },
              { policy_status: { contains: q, mode: 'insensitive' } },
              { product_name: { contains: q, mode: 'insensitive' } },
              { product_type: { contains: q, mode: 'insensitive' } },
              { reconciled: { contains: q, mode: 'insensitive' } },
              { transaction_type: { contains: q, mode: 'insensitive' } },
              {
                parent_data: {
                  writing_carrier_name: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  agent_name: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: { agent_id: { contains: q, mode: 'insensitive' } },
              },
              {
                parent_data: {
                  carrier_name: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  customer_name: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: { group_id: { contains: q, mode: 'insensitive' } },
              },
              {
                parent_data: {
                  policy_id: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  policy_status: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  product_name: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  product_type: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  reconciled: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  transaction_type: { contains: q, mode: 'insensitive' },
                },
              },
              {
                parent_data: {
                  writing_carrier_name: { contains: q, mode: 'insensitive' },
                },
              },
            ]
          : [
              { agent_name: { contains: q, mode: 'insensitive' } },
              { agent_id: { contains: q, mode: 'insensitive' } },
              { carrier_name: { contains: q, mode: 'insensitive' } },
              { customer_name: { contains: q, mode: 'insensitive' } },
              { group_id: { contains: q, mode: 'insensitive' } },
              { policy_id: { contains: q, mode: 'insensitive' } },
              { policy_status: { contains: q, mode: 'insensitive' } },
              { product_name: { contains: q, mode: 'insensitive' } },
              { product_type: { contains: q, mode: 'insensitive' } },
              { reconciled: { contains: q, mode: 'insensitive' } },
              { transaction_type: { contains: q, mode: 'insensitive' } },
              { writing_carrier_name: { contains: q, mode: 'insensitive' } },
            ],
      },
    ],
  };
  if (addition) {
    (globalWhere.AND as any).push(addition);
  }
  if (id) {
    (globalWhere.AND as any).push({ str_id: id });
    (globalWhere.AND as any).push({
      OR: [{ state: 'active' }, { state: 'grouped' }],
    });
  } else if (reconciliation_id) {
    (globalWhere.AND as any).push({
      reconciliation_str_id: reconciliation_id,
    });
  } else {
    (globalWhere.AND as any).push({
      OR: req.query.incl_linked
        ? [{ state: 'active' }, { state: 'grouped' }]
        : [{ state: 'active' }],
    });
  }

  return structuredClone(globalWhere);
}

export const _getReconciliationsData = async (
  req: ExtNextApiRequest,
  isExportData = false
) => {
  try {
    const {
      query: {
        page,
        limit,
        orderBy = 'reconciled',
        sort = 'asc',
        q = '',
        carrier_name = '',
        writing_carrier_name = '',
        effective_date_start = '',
        effective_date_end = '',
        reconciled = '',
        // Reconciliation_id = '',
        // id = '',
        policy_status = '',
        product_name = '',
        product_type = '',
        agent_name = '',
        // Parameter only sent when exporting producer view csv
        producer_view = false,
        report_data_id = '',
        statements = '',
        compensation_type = '',
        is_saved_report,
      },
    } = req;
    if (producer_view) req.role_id = Roles.PRODUCER.toString();

    // Validation/error handling of page, limit, orderBy, sort
    const { take, skip } = calculateSkipAndTake({ page, limit });
    const effectiveDateStart = dateOrDefault(effective_date_start, undefined);
    const effectiveDateEnd = setNextDay(
      dateOrDefault(effective_date_end, undefined)
    );

    // If the user making the request is a producer, we get the user and contact data.
    let userData;
    if (parseInt(req.role_id) === Roles.PRODUCER) {
      userData = await prisma.users.findUnique({
        where: { uid: req.uid },
        select: {
          user_contact: {
            select: {
              str_id: true,
            },
          },
        },
      });
    }
    if (producer_view && parseInt(req.role_id) === Roles.PRODUCER) {
      userData = await prisma.users.findFirst({
        where: {
          account_user_roles: {
            some: {
              role_id: Roles.PRODUCER,
              account_id: req.account_id,
              state: 'active',
            },
          },
        },
        select: {
          user_contact: {
            select: {
              str_id: true,
            },
          },
        },
      });
    }

    const globalWhere = loadFilterOption(req, null, userData);
    // if (id) {
    //   (globalWhere.AND as any).push({ str_id: id });
    // } else if (reconciliation_id) {
    //   (globalWhere.AND as any).push({
    //     reconciliation_str_id: reconciliation_id,
    //   });
    // } else {
    //   (globalWhere.AND as any).push({ state: 'active' });
    // }

    // Get the query params from the url and filter out the ones we want to use for filtering
    const rawUrl = req.url;
    const parsedUrl = url.parse(rawUrl);
    const queryParamNames = parsedUrl.query
      ?.split('&')
      .map((param) => param?.split('=')[0]);

    const where = structuredClone<any>(globalWhere);
    const filtersWhere = structuredClone(globalWhere);

    let filterList = [
      'writing_carrier_name',
      'carrier_name',
      'policy_status',
      'product_name',
      'product_type',
      'agent_name',
    ];

    const filteredQueryParamNames = queryParamNames?.filter((param) =>
      filterList.includes(param)
    );
    const additionalFilterFields = [];

    if (carrier_name) {
      const filters = loadFieldFilters(carrier_name, 'carrier_name');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('carrier_name');
          filterList = filterList.filter((item) => item !== 'carrier_name');
        }
      }
    }
    if (writing_carrier_name) {
      const filters = loadFieldFilters(
        writing_carrier_name,
        'writing_carrier_name'
      );
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('writing_carrier_name');
          filterList = filterList.filter(
            (item) => item !== 'writing_carrier_name'
          );
        }
      }
    }
    if (reconciled) {
      const filters = loadFieldFilters(reconciled, 'reconciled');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('reconciled');
          filterList = filterList.filter((item) => item !== 'reconciled');
        }
      }
    }
    if (policy_status) {
      const filters = loadFieldFilters(policy_status, 'policy_status');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('policy_status');
          filterList = filterList.filter((item) => item !== 'policy_status');
        }
      }
    }
    if (product_name) {
      const filters = loadFieldFilters(product_name, 'product_name');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('product_name');
          filterList = filterList.filter((item) => item !== 'product_name');
        }
      }
    }
    if (product_type) {
      const filters = loadFieldFilters(product_type, 'product_type');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('product_type');
          filterList = filterList.filter((item) => item !== 'product_type');
        }
      }
    }
    if (agent_name) {
      const filters = loadFieldFilters(agent_name, 'agent_name');
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('agent_name');
          filterList = filterList.filter((item) => item !== 'agent_name');
        }
      }
    }
    if (report_data_id) {
      let filters: Prisma.reconciliation_dataWhereInput[] = loadFieldFilters(
        report_data_id,
        'report_data_id'
      );
      filters = filters.map((filter: any) => {
        if (filter.report_data_id && Array.isArray(filter.report_data_id.in)) {
          return {
            report_data: {
              document: { filename: { in: filter.report_data_id.in } },
            },
          };
        } else {
          return {
            OR: [
              { report_data_id: { equals: null } },
              { report_data: { document_id: { equals: null } } },
            ],
          };
        }
      });
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
      }
    }

    if (statements) {
      let statementConditions;
      if (statements === '-1') {
        statementConditions = [
          {
            statement_ids: { equals: Prisma.DbNull },
          },
        ];
      } else {
        statementConditions = (
          Array.isArray(statements) ? statements : [statements]
        ).map((statement) => ({
          statement_ids: {
            array_contains: parseInt(statement),
          },
        }));
      }
      where.AND.push({
        OR: statementConditions,
      });
    }

    if (compensation_type) {
      let conditions;
      if (compensation_type === DEFAULT_FILTER.BLANK_OPTION) {
        conditions = [
          {
            OR: [
              {
                statements: {
                  some: {
                    compensation_type: null,
                  },
                },
              },
              {
                statements: {
                  none: {},
                },
              },
            ],
          },
        ];
      } else {
        let compensationTypes: string[] = [];

        if (Array.isArray(compensation_type)) {
          compensationTypes = compensation_type.flatMap((compType) =>
            compType.split(',')
          );
        } else {
          compensationTypes = compensation_type.split(',');
        }

        const reconciliationIdsResult: any[] = await prisma.$queryRaw`
      SELECT
        DISTINCT rd.id AS reconciliation_id
      FROM
        reconciliation_data rd
      JOIN
        LATERAL jsonb_array_elements_text(rd.statement_ids) AS statement_id ON true
      JOIN
        statement_data s ON s.id = statement_id.value::int
      WHERE
        rd.account_id = ${req.account_id}
        AND rd.state IN ('active', 'grouped')
        AND s.compensation_type IN (${Prisma.join(compensationTypes)});
      `;

        const reconciliationIds = reconciliationIdsResult.map<number>(
          ({ reconciliation_id }) => reconciliation_id
        );

        conditions = {
          // TODO(toby): Find a way to avoid binding an unbounded set of IDs. This will break when the number of results exceeds Prisma's parameter binding limit.
          id: { in: reconciliationIds },
        };
      }
      where.AND.push(conditions);
    }
    where.AND = [
      ...where.AND,
      {
        AND: [
          {
            effective_date: effectiveDateStart
              ? { gte: effectiveDateStart }
              : undefined,
          },
          {
            effective_date: effectiveDateEnd
              ? { lt: effectiveDateEnd }
              : undefined,
          },
        ],
      },
    ];

    // Define the fields to be selected based on the user's account settings
    const reconAccountSettings = await prisma.account_role_settings.findUnique({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: String(req.account_id),
          role_id: parseInt(req.role_id),
          custom_view_name: customViewDefault,
        },
      },
      select: {
        pages_settings: true,
      },
    });

    let selectStatement = {};
    const selectedFields: Record<string, any> = {
      str_id: true,
      created_at: true,
      created_by: true,
      updated_at: true,
      updated_by: true,
      state: true,
      statement_ids: true,
      children_reconciliation_data: true,
    };
    let includeCompTypeField = false;

    // Custom fields are fields that have custom logic in BE or FE
    // and they are not in the reconciliation_data table.
    // We have to filter them out from the settingFields which is used to select fields from the reconciliation_data table.
    const customFields = [
      'profit',
      'profit_percentage',
      ...RECONCILIATION_V2_ONLY_FIELDS,
    ];
    const settingFields =
      (
        reconAccountSettings?.pages_settings?.reconciliation?.fields || []
      ).filter((f) => !customFields.includes(f)) || [];

    if (
      shouldGetSelectedFields(
        is_saved_report,
        reconAccountSettings?.pages_settings?.reconciliation?.fields
      )
    ) {
      settingFields.forEach((item: string) => {
        if (item === 'report_data' && +req.role_id === Roles.PRODUCER) {
          selectedFields[item] = {
            select: {
              id: true,
              str_id: true,
              // Contacts_split may contain multiple agents, but seems no way to filter here, so filter in code post retrieval.
              contacts_split: true,
            },
          };
        } else if (item === 'report_data_id') {
          selectedFields['report_data_id'] = true;
          selectedFields['report_data'] = {
            include: {
              document: {
                select: {
                  filename: true,
                  str_id: true,
                },
              },
            },
          };
        } else if (item === 'statements') {
          selectedFields['statements'] = {
            include: {
              document: {
                select: {
                  filename: true,
                  str_id: true,
                },
              },
            },
          };
        } else if (
          ['agent_commission_amount', 'agent_commission_amount_pct'].includes(
            item
          )
        ) {
          // Do nothing - this isn't a real db field and is calculated below
        } else if (item === 'compensation_type') {
          includeCompTypeField = true;
          selectedFields['statements'] = {
            select: {
              id: true,
              compensation_type: true,
            },
          };
        } else {
          selectedFields[item] = true;
        }
      });
      if (selectedFields['commission_amount_monthly']) {
        selectedFields['commission_expected_monthly'] = true;
        selectedFields['commission_balance_monthly'] = true;
      }
      selectStatement = { ...selectedFields };
    } else {
      // TODO: Get reconciliation data from the fields table (First we need to populate that table with reconciliation fields)
      selectStatement = {
        contacts: true,
        agent_name: true,
        balance: true,
        writing_carrier_name: true,
        amount_paid: true,
        cancellation_date: true,
        commission_amount_monthly: true,
        commission_expected_monthly: true,
        commission_balance_monthly: true,
        commissions_expected: true,
        customer_paid_premium_amount: true,
        customer_name: true,
        effective_date: true,
        group_id: true,
        issue_age: true,
        log: true,
        notes: true,
        carrier_name: true,
        policy_id: true,
        internal_id: true,
        premium_amount: true,
        product_name: true,
        product_type: true,
        reconciled: true,
        reinstatement_date: true,
        policy_status: true,
        statement_ids: true,
        statement_str_ids: true,
        transaction_type: true,
        commissionable_premium_amount: true,
        report_str_id: true,
        children_reconciliation_data: true,
        report_data_id: true,
        report_data: {
          include: {
            document: {
              select: {
                filename: true,
                str_id: true,
              },
            },
          },
        },
        statements: {
          include: {
            document: {
              select: {
                filename: true,
                str_id: true,
              },
            },
          },
        },
      };
      includeCompTypeField = true;
    }

    const getReconciliationData = async () => {
      return await prisma.reconciliation_data.findManyWithPagination({
        where,
        skip,
        take,
        select: {
          id: true,
          str_id: true,
          created_at: true,
          created_by: true,
          updated_at: true,
          updated_by: true,
          state: true,
          ...selectStatement,
        },
        orderBy: [{ [orderBy]: sort }, { ['id']: 'asc' }],
      });
    };

    const promises = [
      getReconciliationData(),
      prisma.reconciliation_data.count({
        where,
      }),
      prisma.reconciliation_data.findFirst({
        where: loadFilterOption(req, { effective_date: { not: null } }),
        select: {
          effective_date: true,
        },
        orderBy: [{ ['effective_date']: 'asc' }],
      }),
      prisma.reconciliation_data.findFirst({
        where: loadFilterOption(req, { effective_date: { not: null } }),
        select: {
          effective_date: true,
        },
        orderBy: [{ ['effective_date']: 'desc' }],
      }),
      prisma.reconciliation_data.findFirst({
        where: loadFilterOption(req, { payment_date_first: { not: null } }),
        select: {
          payment_date_first: true,
        },
        orderBy: [{ ['payment_date_first']: 'asc' }],
      }),
      prisma.reconciliation_data.findFirst({
        where: loadFilterOption(req, { payment_date_last: { not: null } }),
        select: {
          payment_date_last: true,
        },
        orderBy: [{ ['payment_date_last']: 'desc' }],
      }),
      filterFieldValues(req.account_id, filterList, where),
    ];

    if (additionalFilterFields.length > 0) {
      promises.push(
        filterFieldValues(req.account_id, additionalFilterFields, filtersWhere)
      );
    }

    promises.push(
      prisma.reconciliation_data.aggregate({
        where,
        _sum: {
          commissionable_premium_amount: true,
          balance: true,
          commissions_expected: true,
          premium_amount: true,
          customer_paid_premium_amount: true,
        },
      })
    );

    const data: any[] = await Promise.all(promises);

    const getCompensationTypeById = async (
      id: string
    ): Promise<string | null> => {
      const result = await prisma.statement_data.findUnique({
        where: { id: id },
        select: { compensation_type: true },
      });
      return result?.compensation_type || null;
    };

    // Add compensation_type field to response
    if (includeCompTypeField)
      data[0] = await Promise.all(
        data[0].map(async (item) => {
          if (!Array.isArray(item.statements) || item.statements.length === 0) {
            if (
              !Array.isArray(item.statement_ids) ||
              item.statement_ids.length === 0
            ) {
              item.compensation_type = null;
            } else {
              const compensation_types = await Promise.all(
                item.statement_ids.map(
                  async (id) => await getCompensationTypeById(id)
                )
              );
              item.compensation_type = [...new Set(compensation_types)];
            }
          } else {
            const compensation_types = item.statements
              .map((statement) => statement.compensation_type)
              .filter(Boolean);
            item.compensation_type = [...new Set(compensation_types)];
          }
          return item;
        })
      );

    // Agent split to only include value for this agent
    if (+req.role_id === Roles.PRODUCER) {
      data[0] = await Promise.all(
        data[0].map(async (item) => {
          if (item.report_data?.contacts_split) {
            item.report_data.contacts_split = Object.fromEntries(
              Object.entries(item.report_data.contacts_split).filter(
                ([k, v]) => k === userData.user_contact[0].str_id
              )
            );
          }
          // TODO: Calculate commission amount using joins with statement relations and/or separate commissions table when avaialble.
          if (
            Array.isArray(item.statement_ids) &&
            item.statement_ids.length > 0
          ) {
            const statementData = await prisma.statement_data.findMany({
              where: {
                id: { in: item.statement_ids },
                account_id: req.account_id,
                state: { in: ['active', 'grouped'] },
              },
            });
            const commissionAmount = statementData.reduce((acc, cur) => {
              return (
                acc +
                +(
                  cur?.agent_commissions?.[userData.user_contact[0].str_id] ?? 0
                )
              );
            }, 0);
            item.agent_commission_amount = commissionAmount;
            item.agent_commission_amount_pct =
              item.commissionable_premium_amount
                ? +commissionAmount.toFixed(2) /
                  item.commissionable_premium_amount
                : null;
          }
          return item;
        })
      );
    } else {
      const processItem = async (item) => {
        if (
          Array.isArray(item.statement_ids) &&
          item.statement_ids.length > 0
        ) {
          const statementData = await prisma.statement_data.findMany({
            where: {
              id: { in: item.statement_ids },
              account_id: req.account_id,
              state: { in: ['active', 'grouped'] },
            },
          });

          const commissionAmount = statementData.reduce((acc, cur) => {
            return Object.entries(cur?.agent_commissions ?? {}).reduce(
              (acc, [k, v]) => (+acc + k === 'total' ? 0 : +v),
              0
            );
          }, 0);

          item.agent_commission_amount = commissionAmount;
          item.agent_commission_amount_pct = item.commissionable_premium_amount
            ? +commissionAmount.toFixed(2) / item.commissionable_premium_amount
            : null;
        }
        return item;
      };

      data[0] = await limitConcurrency(processItem, data[0], 30, {
        onFail: (error) => {
          console.error('Failed to process item:', error);
        },
      });
    }

    // Add contact names and contact split info to the response for export data
    if (isExportData) {
      await processContacts(data[0]);
    }

    if (additionalFilterFields.length > 0) {
      data[6] = Object.assign({}, data[6], data[7]);
      data.splice(7, 1);
    }

    // Most results come in at top level, but aggregate query comes in under _sum.
    // Moving to top level to align with other items.
    data[7] = data[7]?._sum;

    return { data: data, availableFields: reconAccountSettings };
  } catch (error) {
    Sentry.captureException(error);
    console.error('Error fetching reconciliation data', error);
    throw error;
  }
};

/**
 * Get reconciliations
 */
const getReconciliations = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const availableFields = await prismaClient.account_role_settings.findUnique(
      {
        where: {
          account_id_role_id_custom_view_name: {
            account_id: String(req.account_id),
            role_id: parseInt(req.role_id),
            custom_view_name: customViewDefault,
          },
        },
        select: {
          pages_settings: true,
          reconciliation_view: true,
        },
      }
    );

    // Check if the account has the policy_document and statements fields enabled
    let policyDocumentFilter = true;
    let statementDocumentFilter = true;
    let compensationTypeFilter = true;
    if (
      Array.isArray(availableFields?.reconciliation_view) &&
      availableFields.reconciliation_view.length > 0
    ) {
      policyDocumentFilter =
        availableFields.reconciliation_view.includes('report_data_id');
      statementDocumentFilter =
        availableFields.reconciliation_view.includes('statements');
      compensationTypeFilter =
        availableFields.reconciliation_view.includes('compensation_type');
    }

    const filterWhere = {
      account_id: req.account_id,
      AND: [],
    };
    let filterWhereSql: any = Prisma.sql`reconciliation_data.account_id = ${req.account_id}`;
    if (req.query.id) {
      (filterWhere.AND as any).push({ str_id: req.query.id });
      filterWhereSql = Prisma.sql`${filterWhereSql} AND reconciliation_data.str_id = ${req.query.id}`;
    } else if (req.query.reconciliation_id) {
      (filterWhere.AND as any).push({
        reconciliation_str_id: req.query.reconciliation_id,
      });
      filterWhereSql = Prisma.sql`${filterWhereSql} AND reconciliation_data.reconciliation_str_id = ${req.query.reconciliation_id}`;
    } else {
      (filterWhere.AND as any).push({ state: { in: ['active', 'grouped'] } });
      filterWhereSql = Prisma.sql`${filterWhereSql} AND reconciliation_data.state IN ('active', 'grouped')`;
    }

    // Add items to compensation_type filter only if the statements field exists in the response
    let getCompensationTypeFieldValuesPromise: Promise<any> | undefined;
    if (compensationTypeFilter) {
      const filterWhereStatementDocs = structuredClone(filterWhere);
      (filterWhereStatementDocs.AND as any).push({
        statement_ids: { not: null },
      });
      const getCompensationTypeFieldValues = async () => {
        const compTypeResults: any[] = await prisma.$queryRaw`
        SELECT
          DISTINCT s.compensation_type AS "compensation_type"
        FROM
          reconciliation_data
        JOIN
          LATERAL jsonb_array_elements_text(reconciliation_data.statement_ids) AS statement_id ON true
        JOIN
          statement_data s ON s.id = statement_id.value::int
        WHERE
          ${filterWhereSql}
          AND s.state IN ('active', 'grouped')
          AND s.compensation_type IS NOT NULL
          AND s.compensation_type <> ''
        `;

        const compensation_type = compTypeResults
          .map<string>(({ compensation_type }) => compensation_type)
          .filter((compType) => !!compType);
        compensation_type.sort((a, b) => a.localeCompare(b));

        return compensation_type;
      };

      getCompensationTypeFieldValuesPromise = getCompensationTypeFieldValues();
    }

    const { data } = await _getReconciliationsData(req);

    const _fieldOptions = data[6];
    let fieldOptions = {
      report_data_id: undefined,
      statements: undefined,
      compensation_type: undefined,
      effective_date_start: data[2]?.effective_date,
      effective_date_end: data[3]?.effective_date,
      payment_date_first: data[4]?.payment_date_first,
      payment_date_last: data[5]?.payment_date_last,
      ..._fieldOptions,
    };

    if (!policyDocumentFilter) {
      delete fieldOptions.report_data_id;
    }
    if (!statementDocumentFilter) {
      delete fieldOptions.statements;
    }
    if (!compensationTypeFilter) {
      delete fieldOptions.compensation_type;
    }

    const augmentRequestRecords = async () => {
      // Check if the account has the policies_show_grouped setting enabled
      const accountData = await prisma.accounts.findUnique({
        where: { str_id: req.account_id, state: 'active' },
        select: {
          policies_show_grouped: true,
        },
      });

      // If the account has the policies_show_grouped setting enabled, we need to get the parent report data id for each reconciled policy
      if (accountData.policies_show_grouped) {
        const reportStrIds = data[0]
          .map((item) => item.report_str_id)
          .filter(Boolean);
        const policyData = await prisma.report_data.findMany({
          where: { str_id: { in: reportStrIds } },
          select: {
            str_id: true,
            parent_id: true,
            children_report_data: {
              select: {
                str_id: true,
                policy_id: true,
              },
            },
          },
        });

        const parentReportDataIds = policyData
          .map((item) => item.parent_id)
          .filter(Boolean);
        const responsePolicyData = await prisma.report_data.findMany({
          where: { id: { in: parentReportDataIds } },
          select: {
            id: true,
            str_id: true,
            policy_id: true,
          },
        });
        const responsePolicyDataMap = responsePolicyData.reduce((map, item) => {
          map[item.id] = { str_id: item.str_id, policy_id: item.policy_id };
          return map;
        }, {});

        for (const item of data[0]) {
          if (item.report_str_id) {
            const policy = policyData.find(
              (policy) => policy.str_id === item.report_str_id
            );
            // If the policy has a parent report data id, we add a new field to the response with the parent report data id
            if (policy && policy.parent_id) {
              item.report_data_parent = responsePolicyDataMap[policy.parent_id];
            }
            // If the policy has children report data, we add a new field to the response with the children report data ids
            if (
              policy &&
              policy.children_report_data &&
              policy.children_report_data.length > 0
            ) {
              item.children_report_data_ids = policy.children_report_data.map(
                (child) => ({
                  str_id: child.str_id,
                  policy_id: child.policy_id,
                })
              );
              item.children_report_data = policy.children_report_data;
            }
          }
        }
      }
    };

    const augmentRequestRecordsPromise = augmentRequestRecords();

    // Add items to Policy document filter only if the report_data_id field exists in the response
    let getReportDataIdPromise: Promise<any> | undefined;
    if (policyDocumentFilter) {
      const getReportDataId = async () => {
        let rawQuery = () =>
          prisma.$queryRaw`select d.filename from reconciliation_data rcd inner join report_data rpd on rpd.id = rcd.report_data_id inner join documents d on d.str_id = rpd.document_id where rcd.account_id = ${req.account_id} and rcd.state in ('active', 'grouped') and rpd.state in ('active', 'grouped') and d.state = 'active' group by 1`;
        if (req.query.id) {
          rawQuery = () =>
            prisma.$queryRaw`select d.filename from reconciliation_data rcd inner join report_data rpd on rpd.id = rcd.report_data_id inner join documents d on d.str_id = rpd.document_id where rcd.account_id = ${req.account_id} and rcd.str_id = ${req.query.id} group by 1`;
        } else if (req.query.reconciliation_id) {
          rawQuery = () =>
            prisma.$queryRaw`select d.filename from reconciliation_data rcd inner join report_data rpd on rpd.id = rcd.report_data_id inner join documents d on d.str_id = rpd.document_id where rcd.account_id = ${req.account_id} and reconciliation_str_id = ${req.query.reconciliation_id} group by 1`;
        }

        const filterWherePolicyDocs = structuredClone(filterWhere);
        (filterWherePolicyDocs.AND as any).push({
          report_data: {
            document: { isNot: null },
          },
        });

        const policyDocuments = await rawQuery();
        const documents = Array.isArray(policyDocuments)
          ? policyDocuments.map((doc) => doc.filename)
          : [];

        const report_data_id = [DEFAULT_FILTER.BLANK_OPTION, ...documents];
        report_data_id.sort((a, b) => a.localeCompare(b));

        return report_data_id;
      };

      getReportDataIdPromise = getReportDataId();
    }

    // Add items to statement documents filter only if the statements field exists in the response
    let getStatementFieldValuesPromise: Promise<any> | undefined;
    if (statementDocumentFilter) {
      const getStatementFieldValues = async () => {
        const findSql = Prisma.sql`select statement_data.id as "statement_id", documents.filename as "filename" from reconciliation_data join "_reconciliation_dataTostatement_data" on reconciliation_data.id="_reconciliation_dataTostatement_data"."A" join statement_data on "_reconciliation_dataTostatement_data"."B"=statement_data.id join documents on statement_data.document_id=documents.str_id where ${filterWhereSql}`;
        const results: any[] = await prisma.$queryRaw(findSql);

        let documents = results.map(({ statement_id, filename }) => ({
          id: statement_id,
          name: filename,
        }));

        // Remove duplicates
        documents = Array.from(
          new Set(documents.map((doc) => JSON.stringify(doc)))
        ).map((doc) => JSON.parse(doc as string));
        const statements = [
          { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
          ...documents,
        ];
        statements.sort((a, b) => a.name.localeCompare(b.name));

        return statements;
      };

      getStatementFieldValuesPromise = getStatementFieldValues();
    }

    await augmentRequestRecordsPromise;

    if (getReportDataIdPromise !== undefined) {
      fieldOptions.report_data_id = await getReportDataIdPromise;
    }

    if (getStatementFieldValuesPromise !== undefined) {
      fieldOptions.statements = await getStatementFieldValuesPromise;
    }

    if (getCompensationTypeFieldValuesPromise !== undefined) {
      fieldOptions.compensation_type =
        await getCompensationTypeFieldValuesPromise;
    }

    if (parseInt(req.role_id) !== Roles.ACCOUNT_ADMIN) {
      const excludedKeys = [
        'effective_date_start',
        'effective_date_end',
        'payment_date_first',
        'payment_date_last',
      ];
      const dataObject = data[0] ? data[0][0] : null;
      fieldOptions = filterFieldOptions(dataObject, fieldOptions, excludedKeys);
    } else {
      const excludedKeys = [
        'effective_date_start',
        'effective_date_end',
        'payment_date_first',
        'payment_date_last',
        'report_data_id',
        'statement_documents',
      ];
      const dataObject = data[0] ? data[0][0] : null;
      fieldOptions = filterFieldOptions(dataObject, fieldOptions, excludedKeys);
    }

    res.json({
      data: data[0],
      count: data[1],
      fieldOptions,
      totals: data[7],
    });
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

// /**
//  * Add reconciliations
//  */
// const addReconciliations = async (
//   req: ExtNextApiRequest,
//   res: ExtNextApiResponse
// ) => {
//   const body = req.body as ReconciliationsModel;
//   const { uid, account_id } = req;

//   // pick up the fields which are included in the table
//   try {
//     const list = Array.isArray(body) ? body : [body];
//     const promises = list.map((post) => {
//       const data = getTableFields(post);
//       return prisma.reconciliation_data.create({
//         data: {
//           ...data,
//           account_id,
//           uid,
//         },
//       });
//     });
//     const data = await prisma.$transaction(promises);
//     res.status(200).json({ status: 'OK', data: data.length });
//   } catch (error) {
//     console.error(error);
//     Sentry.captureException(error);
//     res.status(500).json({ error: error.message || error.toString() });
//   }
// };

/**
 * Update reconciliations
 * /api/reconciliations
 * @returns
 */
const updateReconciliations = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body as ReconciliationsModel;
  const { uid, account_id } = req;
  const post = getTableFields(body);
  try {
    const data = await prisma.reconciliation_data.update({
      where: {
        id: Number((post as any).id),
        account_id: req.account_id,
      },
      data: {
        ...post,
        account_id,
        uid,
      },
    });
    res.status(200).json({ data });
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

// Bulk update reconciliations
const bulkUpdateReconciliations = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const bodys = req.body as ReconciliationsModel[];
  const { uid, account_id } = req;

  // If given single record, put in array
  const records = Array.isArray(bodys) ? bodys : [bodys];

  const postList = records.map((body) => getTableFields(body));
  try {
    const promises = postList.map((post: any) => {
      return prisma.reconciliation_data.update({
        where: { id: Number(post.id), account_id: req.account_id },
        data: {
          ...post,
          account_id,
          uid,
        },
      });
    });
    const data = await prisma.$transaction(promises);
    res.status(200).json({ data });
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

const deleteReconciliations = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    await prisma.reconciliation_data.updateMany({
      where: {
        AND: [{ id: { in: ids } }, { account_id: String(req.account_id) }],
      },
      data: {
        state: 'deleted',
      },
    });
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    console.error(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

interface ReconciliationsModel {
  id: number;
  str_id: string;
  uid: string;
  state: string;
  created_at: string | number;
  updated_at: string | number;

  aggregate_premiums: number;
  amount_paid: number;
  balance: number;
  cancellation_date: string | number;
  carrier_name: string;
  commission_amount_monthly: number;
  commissions_expected: number;
  customer_name: string;
  effective_date: string | number;
  internal_id: string | null;
  issue_age: number;
  log: string;
  normalized_id: string;
  policy_id: string;
  policy_status: string;
  premium_amount: number;
  product_type: string;
  reconciled: string;
  reinstatement_date: string | number;
  report_str_id: string;
  statement_id: string;
  statement_ids: number[];
  statement_str_ids: string[];
  writing_carrier_name: string;
}

const getTableFields = (post: ReconciliationsModel) => {
  // Pick up all the fields form the post
  const {
    id,
    str_id,
    uid,
    state,
    created_at,
    updated_at,
    aggregate_premiums,
    amount_paid,
    balance,
    cancellation_date,
    carrier_name,
    commission_amount_monthly,
    commissions_expected,
    customer_name,
    effective_date,
    internal_id,
    issue_age,
    log,
    normalized_id,
    policy_id,
    policy_status,
    premium_amount,
    product_type,
    reconciled,
    reinstatement_date,
    report_str_id,
    statement_id,
    statement_ids,
    statement_str_ids,
    writing_carrier_name,
  } = post;

  const tableFields = {
    id,
    str_id,
    uid,
    state,
    created_at,
    updated_at,
    aggregate_premiums,
    amount_paid,
    balance,
    cancellation_date,
    carrier_name,
    commission_amount_monthly,
    commissions_expected,
    customer_name,
    effective_date,
    internal_id,
    issue_age,
    log,
    normalized_id,
    policy_id,
    policy_status,
    premium_amount,
    product_type,
    reconciled,
    reinstatement_date,
    report_str_id,
    statement_id,
    statement_ids,
    statement_str_ids,
    writing_carrier_name,
  };
  // Pick up valid fileds
  const validFields = Object.keys(tableFields).reduce((acc, key) => {
    if (tableFields[key]) {
      acc[key] = tableFields[key];
    }
    return acc;
  }, {});
  return validFields;
};

const filterFieldValues = async (
  account_id: string,
  filterFields: string[],
  where: Prisma.reconciliation_dataWhereInput
): Promise<Record<string, any[]>> => {
  const queries = filterFields.map<Promise<[string, any[]]>>(
    async (fieldName) => {
      const values = await filterFieldsCache.get(
        account_id,
        fieldName,
        where,
        async () => {
          const records: any[] = await prisma.reconciliation_data.findMany({
            select: { [fieldName]: true },
            where,
          });

          return normalizeFieldValues(
            records.map((record) => record[fieldName])
          );
        }
      );

      return [fieldName, values];
    }
  );

  const resultEntries = await Promise.all(queries);

  return Object.fromEntries(resultEntries);
};
