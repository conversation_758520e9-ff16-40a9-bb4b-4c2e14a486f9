import {
  history_state,
  history_status,
  history_type,
  Prisma,
} from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import currency from 'currency.js';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Put,
  Req,
  Res,
} from 'next-api-decorators';
import { ReportDataQueryDto } from 'common/dto/report_data/dto';
import { customViewDefault } from 'common/constants/account_role_settings';

import { container } from '@/ioc';
import FieldValuesCache from '@/lib/field-values/FieldValuesCache';
import normalizeFieldValues from '@/lib/field-values/normalizeFieldValues';
import { withAuth } from '@/lib/middlewares';
import prisma, { prismaClient } from '@/lib/prisma';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { _postHistoryData } from '@/pages/api/history';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import {
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
  Roles,
} from '@/types';
import ReportDataFilterService from '@/services/report/filter';
import { EnhancedQuery } from '@/lib/decorators';
import { calculateSkipAndTake } from '@/prisma';
import {
  chunkArray,
  limitConcurrency,
  isAllowedFieldToSelectInDatabase,
  addReceivableValuesToCommissionData,
} from '@/lib/helpers';
import { shouldGetSelectedFields } from '@/lib/helpers/shouldGetSelectedFields';

const filterFieldsCache = new FieldValuesCache('report_data');

class Handler {
  @Get()
  @Guard(CrudAction.READ, EntityType.POLICIES)
  async getReportData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @EnhancedQuery() query: ReportDataQueryDto
  ) {
    query.disableFilter = true;
    await getReportData(req, res, query);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.POLICIES)
  async createReportData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await addReportData(req, res);
  }

  @Put()
  @Patch()
  @Guard(CrudAction.CREATE, EntityType.POLICIES)
  async updateReportData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await updateReportData(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.POLICIES)
  async deleteReportData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await deleteReportData(req, res);
  }
}

export default withAuth(createHandler(Handler));

/**
 * Base filter options
 */
function loadFilterOption(req: ExtNextApiRequest, excludeNullDates = false) {
  const {
    query: { q = '', id = '' },
  } = req;

  const globalWhere = {
    AND: [
      { account_id: String(req.account_id) },
      { OR: [{ state: 'active' }] },
      {
        OR: [
          { agent_name: { contains: q, mode: 'insensitive' } },
          { aggregation_id: { contains: q, mode: 'insensitive' } },
          { customer_name: { contains: q, mode: 'insensitive' } },
          { document_id: { contains: q, mode: 'insensitive' } },
          { notes: { contains: q, mode: 'insensitive' } },
          { policy_id: { contains: q, mode: 'insensitive' } },
          { policy_status: { contains: q, mode: 'insensitive' } },
          { product_name: { contains: q, mode: 'insensitive' } },
          { product_sub_type: { contains: q, mode: 'insensitive' } },
          { product_type: { contains: q, mode: 'insensitive' } },
          { writing_carrier_name: { contains: q, mode: 'insensitive' } },
          { account_type: { contains: q, mode: 'insensitive' } },
          { import_id: { contains: q, mode: 'insensitive' } },
        ],
      },
      { effective_date: undefined },
      { str_id: undefined },
    ],
  };

  if (excludeNullDates) {
    globalWhere.AND[3].effective_date = { not: null };
  }

  if (id) {
    globalWhere.AND[4].str_id = id;
    globalWhere.AND[1].OR = [
      { state: 'active' },
      { state: 'duplicate' },
      { state: 'grouped' },
    ];
  }

  return JSON.parse(JSON.stringify(globalWhere));
}

export const _getReportData = async (
  req: ExtNextApiRequest,
  query?: ReportDataQueryDto,
  isExportData = false
) => {
  const queryData = query || req.query;
  const {
    disableFilter,
    limit,
    orderBy = 'created_at',
    page,
    // Parameter only sent when exporting producer view csv
    producer_view = false,
    sort = 'desc',
    is_saved_report,
  } = queryData;

  const account = {
    account_id: req.account_id,
    role_id: req.role_id,
    uid: req.uid,
    ouid: req.ouid,
  } as ExtAccountInfo;
  if (producer_view) account.role_id = Roles.PRODUCER.toString();

  const reportFilterService = container.get(ReportDataFilterService);

  // Validation/error handling of page, limit, orderBy, sort
  const { take, skip } = calculateSkipAndTake({ page, limit });
  const { id, incl_linked } = queryData;
  let currentRecord = null;
  if (id && incl_linked) {
    currentRecord = await prisma.report_data.findUnique({
      where: { str_id: id },
      select: { parent_id: true, id: true },
    });
  }

  const { globalWhere, where, filterList, additionalFilterFields } =
    await reportFilterService.getFilters(
      req,
      queryData,
      account,
      currentRecord
    );

  const filtersWhere = JSON.parse(JSON.stringify(globalWhere));

  const accounts = await prisma.accounts.findUnique({
    where: {
      str_id: String(req.account_id),
    },
    select: {
      policies_show_grouped: true,
    },
  });

  // Define the fields to be selected based on the user's account settings
  const reportAccountSettings = await prisma.account_role_settings.findUnique({
    where: {
      account_id_role_id_custom_view_name: {
        account_id: String(req.account_id),
        role_id: parseInt(req.role_id),
        custom_view_name: customViewDefault,
      },
    },
    select: {
      pages_settings: true,
    },
  });

  let selectStatement = {};
  const selectedFields = {
    str_id: true,
    sync_id: true,
    sync_worker: true,
    account_id: true,
    created_at: true,
    created_by: true,
    updated_at: true,
    updated_by: true,
    state: true,
    document_id: true,
    document: {
      select: {
        filename: true,
      },
    },
    config: true,
    import_id: true,
    accounting_transaction_details: {
      select: {
        id: true,
        str_id: true,
        amount: true,
        date: true,
        logs: true,
        report_id: true,
        saved_report_id: true,
        statement_id: true,
        status: true,
        transaction_id: true,
        type: true,
        rate: true,
        party: true,
        company_id: true,
        contact: {
          select: {
            id: true,
            str_id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        tags: true,
      },
    },
    customer_id: true,
    customer: {
      select: {
        id: true,
        first_name: true,
        last_name: true,
        company_name: true,
        type: true,
      },
    },
    company_product_id: true,
    company_id: true,
    agent_id: true,
  };

  if (
    shouldGetSelectedFields(
      is_saved_report,
      reportAccountSettings?.pages_settings?.policies?.fields
    )
  ) {
    reportAccountSettings.pages_settings?.policies?.fields.forEach(
      (item: string) => {
        if (isAllowedFieldToSelectInDatabase(item)) {
          selectedFields[item] = selectedFields[item] ?? true;
        }
      }
    );
    selectStatement = { ...selectedFields };
  } else {
    const policy_fields = await prisma.fields.findMany({
      where: {
        model: 'reports',
        state: 'active',
      },
      select: {
        key: true,
      },
    });
    policy_fields.forEach((field: { key: string }) => {
      if (isAllowedFieldToSelectInDatabase(field.key)) {
        selectedFields[field.key] = true;
      }
    });
    selectStatement = { ...selectedFields };
  }

  const getPoliciesData = async () => {
    return await prisma.report_data.findManyWithPagination({
      where,
      skip,
      take,
      select: {
        id: true,
        ...selectStatement,
        report_contacts: {
          select: {
            id: true,
            str_id_report_data: true,
            str_id_contact: true,
          },
        },
        statement_data: true,
        children_report_data: accounts.policies_show_grouped
          ? {
              where: {
                state: { in: ['active', 'grouped'] },
              },
            }
          : undefined,
      },
      orderBy: [{ [orderBy]: sort }, { id: 'asc' }],
    });
  };

  const promises = [
    // Index 0
    getPoliciesData(),
    // Index 1
    prisma.report_data.count({
      where,
    }),
    // Index 2
    prisma.report_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        effective_date: true,
      },
      orderBy: [{ ['effective_date']: 'asc' }],
    }),
    // Index 3
    prisma.report_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        effective_date: true,
      },
      orderBy: [{ ['effective_date']: 'desc' }],
    }),
    // Index 4
    disableFilter ? {} : queryFieldValues(req.account_id, filterList, where),
  ];

  if (additionalFilterFields.length > 0) {
    // Index 5
    promises.push(
      disableFilter
        ? {}
        : queryFieldValues(req.account_id, additionalFilterFields, filtersWhere)
    );
  }

  promises.push(
    prisma.report_data.aggregate({
      where,
      _sum: {
        commissionable_premium_amount: true,
        commissions_expected: true,
        premium_amount: true,
        customer_paid_premium_amount: true,
      },
    })
  );

  const data: any[] = await Promise.all(promises);
  if (additionalFilterFields.length > 0) {
    data[4] = Object.assign({}, data[4], data[5]);
    data.splice(5, 1);
  }

  // Most results come in at top level, but aggregate query comes in under _sum.
  // Moving to top level to align with other items.
  data[5] = data[5]?._sum;

  // Query all contacts
  if (isExportData) {
    const allContactIds = [];
    data[0].forEach((item) => {
      if (item.contacts) {
        allContactIds.push(...item.contacts);
      }

      if (item.contacts_split) {
        allContactIds.push(...Object.keys(item.contacts_split));
      }

      const allAgentCommissionContactIds = [
        ...Object.keys(item.agent_payout_rate_override || {}),
      ].filter((id) => id !== 'total');

      item.agentCommissionContacts = {};
      allAgentCommissionContactIds.forEach((contactId) => {
        item.agentCommissionContacts[contactId] = '';
      });

      allContactIds.push(...allAgentCommissionContactIds);
    });

    const allUniqueContacts = [...new Set(allContactIds)].filter(Boolean);
    const chunkContacts = chunkArray(allUniqueContacts, 1000);

    const result = await limitConcurrency(
      async (contacts) => {
        return await prisma.contacts.findMany({
          where: {
            str_id: {
              in: contacts,
            },
          },
          select: {
            str_id: true,
            first_name: true,
            last_name: true,
          },
          accountInject: false,
        });
      },
      chunkContacts,
      10,
      {
        onFail: (error) => {
          console.error('Failed to process item:', error);
        },
      }
    );

    // Flatten the result array
    const flattenedResult = result.flat();
    const contactNameMap = new Map(
      flattenedResult.map((contact) => [
        contact.str_id,
        `${contact.first_name} ${contact.last_name}`,
      ])
    );

    data[0]?.forEach((item) => {
      const contactNames = [];
      const contactsSplitNames = [];
      if (item.contacts) {
        item.contacts.forEach((contact) => {
          const contactName = contactNameMap.get(contact);
          if (contactName) {
            contactNames.push(contactName);
          }
        });
      }

      if (item.contacts_split) {
        Object.keys(item.contacts_split).forEach((key) => {
          const contactName = contactNameMap.get(key);
          if (contactName) {
            contactsSplitNames.push(contactName);
          }
        });
      }

      Object.keys(item.agentCommissionContacts).forEach((contactId) => {
        const contactName = contactNameMap.get(contactId);
        item.agentCommissionContacts[contactId] = contactName;
      });

      // Add contact names to the item
      item.contactNames = contactNames;
      item.documentName = item.document?.filename;
      item.contactsSplitNames = contactsSplitNames.join(', ');
    });
  }

  data[0]?.forEach(addReceivableValuesToCommissionData);

  return req.query.is_dynamic_select ? data[0] : data;
};

export const getReportData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  query: ReportDataQueryDto
) => {
  const data = await _getReportData(req, query);
  const contactStrIds = data[0].reduce((acc, item) => {
    if (!item.contacts || item.contacts.length === 0) return acc;
    return [...acc, ...item.contacts];
  }, []);
  const agents = contactStrIds
    ? await prismaClient.contacts.findMany({
        where: {
          str_id: {
            in: contactStrIds,
          },
        },
        select: {
          id: true,
          str_id: true,
          first_name: true,
          last_name: true,
          middle_name: true,
          company_name: true,
          type: true,
        },
      })
    : [];
  const agentsMap = new Map(agents.map((r) => [r.id, r]));
  data[0].forEach((item) => {
    item.accounting_transaction_details?.forEach((detail) => {
      if (detail.party !== 'agent') {
        return;
      }
      detail.agent = agentsMap.get(detail.contact_id);
    });
  });
  const _fieldOptions = data[4];
  const fieldOptions = {
    document_id: undefined,
    import_id: undefined,
    contacts: undefined,
    effective_date_start: data[2]?.effective_date,
    effective_date_end: data[3]?.effective_date,
    ..._fieldOptions,
  };

  res.json({
    data: data[0],
    count: data[1],
    fieldOptions,
    totals: data[5],
  });
};

/**
 * Add report_data
 */
export const addReportData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body as ReportDataModel;
  const list = Array.isArray(body) ? body : [body];
  try {
    const importId = nanoid();
    const newData = list.map((item) => {
      const post = getTableFields(item);
      return {
        ...post,
        account_id: req.account_id,
        uid: String(req.uid),
        str_id: nanoid(),
        import_id: importId,
        document_id: item.document_id ?? null,
      };
    });
    const data = await prisma.report_data.createMany({
      data: newData,
    });
    // CreateMany doesn't return the created data, so using import_id to re-query it
    // https://github.com/prisma/prisma/issues/8131#issuecomment-**********
    const newCreated = await prisma.report_data.findMany({
      where: {
        import_id: importId,
      },
    });
    asyncReportHistoryData(newCreated);
    if (newData.length !== newCreated.length || newData.length !== data.count) {
      console.error(
        `Warning: Potential issue in creating new data. Expected new: ${newData.length}, Created new: ${data.count}, Queried new: ${newCreated.length}`
      );
    }
    res.json({ status: 'OK', data: newData.length });
  } catch (error) {
    console.error(`Error adding data: ${error}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

const asyncReportHistoryData = (newRows: any[]) => {
  const historyData = newRows.map((item) => ({
    json_data: JSON.stringify(item),
    uid: item.uid,
    table_type: history_type.report,
    state: history_state.new,
    status: history_status.active,
    statement_data_id: null, // ??
    report_data_id: item.id,
    account_id: item.account_id,
  }));
  _postHistoryData(historyData)
    .then((res) => {
      console.log('history data', res);
    })
    .catch((err) => {
      console.log('history data error', err);
    });
};

/**
 * Update report_data
 */
export const updateReportData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body as ReportDataModel;
  const { uid, account_id } = req;
  const post: Partial<ReportDataModel> = getTableFields(body);
  try {
    // TODO: Do we want to align splits with agents on policy or can they diverge?
    // Allow divergence for now since contacts_split may be synced, and we use
    // contacts_commission_split for override
    // If agent is not saved with the policy, remove them from split config.
    // (post as any).contacts_split = Object.fromEntries(
    //   Object.entries((post as any).contacts_split ?? {}).filter(
    //     ([agentId, v]) => ((post as any).contacts ?? []).includes(agentId)
    //   )
    // );
    // (post as any).contacts_commission_split = Object.fromEntries(
    //   Object.entries((post as any).contacts_commission_split ?? {}).filter(
    //     ([agentId, v]) => ((post as any).contacts ?? []).includes(agentId)
    //   )
    // );
    const split_percentage =
      Number.isNaN(
        +((body.split_percentage as unknown as string) ?? '').replace('%', '')
      ) || (body.split_percentage as unknown as string) === ''
        ? null
        : +((body.split_percentage as unknown as string) ?? '').replace(
            '%',
            ''
          );
    await container.get<SyncFieldService>(SyncFieldService).canUpdateIfChanged({
      newData: post,
      tableName: 'report_data',
      id: Number((post as any).id),
      account: { account_id } as ExtAccountInfo,
      config: body.config || {},
    });

    const data = await prisma.report_data.update({
      where: { id: Number((post as any).id), account_id },
      data: {
        ...post,
        split_percentage,
        account_id,
        uid,
        updated_at: new Date(),
        updated_by: uid,
      },
    });
    asyncReportHistoryData([data]);
    res.status(200).json({ data });
  } catch (error) {
    console.log(error);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

/**
 * Bulk update report_data
 */
export const bulkUpdateReportData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const bodys = req.body as ReportDataModel[];
  const { uid, account_id } = req;
  const postList = bodys.map((body) => getTableFields(body));
  try {
    const promises = postList.map((post: any) => {
      return prisma.report_data.update({
        where: { id: Number(post.id), account_id: String(account_id) },
        data: {
          ...post,
          account_id,
          uid,
        },
      });
    });
    const data = await prisma.$transaction(promises);
    res.status(200).json({ data });
  } catch (error) {
    Sentry.captureException(error);
    res.status(500).json({ error: error.message || error.toString() });
  }
};

/**
 * Delete report_data
 */
const deleteReportData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid ids');
    await prisma.report_data.updateMany({
      where: {
        AND: [{ id: { in: ids } }, { account_id: String(req.account_id) }],
      },
      data: {
        state: 'deleted',
      },
    });
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};

interface ReportDataModel {
  id: number;
  str_id: string | null;
  import_id: string | null;
  state: string | null;
  processing_status: string | null;
  created_at: Date | null;
  created_by: string | null;
  updated_at: Date | null;
  updated_by: string | null;
  account_type: string | null;
  agent_name: string | null;
  aggregation_id: string | null;
  aggregation_primary: boolean | null;
  cancellation_date: Date | null;
  commissionable_premium_amount: number | null;
  excess_amount: number | null;
  commissions_expected: number | null;
  contacts: any[];
  contacts_split: any;
  contacts_commission_split: any;
  customer_first_name: string | null;
  customer_last_name: string | null;
  customer_name: string | null;
  group_id: string | null;
  internal_id: string | null;
  dba: string | null;
  effective_date: Date | null;
  issue_age: number | null;
  notes: string | null;
  policy_id: string | null;
  policy_status: string | null;
  policy_term_months: number | null;
  policy_date: Date | null;
  premium_amount: number | null;
  product_type: string | null;
  product_sub_type: string | null;
  product_name: string | null;
  product_option_name: string | null;
  reinstatement_date: Date | null;
  signed_date: Date | null;
  transaction_type: string | null;
  type: string | null;
  uid: string | null;
  document_id: string | null;
  writing_carrier_name: string | null;
  split_percentage: number | null;
  group_name: string | null;
  payment_mode: string | null;
  geo_state: string | null;
  first_payment_date: Date | null;
  first_processed_date: Date | null;
  receivable_schedule: unknown | null;
  commission_profile_id: string | null;
  config?: { overrideFields: string[] };
  agent_payout_rate_override?: any | null;
  tags: string[] | null;
  customer_id?: number;
}

const parseFieldValue = <K extends keyof ReportDataModel>(
  key: K,
  value: any
) => {
  // TODO: Make this more structured by data types.
  if (key.endsWith('_date')) {
    if (value) {
      return dayjs(value);
    }
  } else if (key.endsWith('_amount') || key === 'commissions_expected') {
    return value === null || value === undefined || value === ''
      ? value
      : currency(value).value;
  } else {
    return value;
  }

  return undefined;
};

const getTableFields = (post: Partial<ReportDataModel>) => {
  const fields: (keyof ReportDataModel)[] = [
    'id',
    'str_id',
    'state',
    'processing_status',
    'account_type',
    'agent_name',
    'aggregation_id',
    'aggregation_primary',
    'cancellation_date',
    'commissionable_premium_amount',
    'excess_amount',
    'commissions_expected',
    'contacts',
    'contacts_split',
    'contacts_commission_split',
    'customer_first_name',
    'customer_last_name',
    'customer_name',
    'customer_id',
    'dba',
    'effective_date',
    'group_id',
    'internal_id',
    'issue_age',
    'notes',
    'policy_id',
    'policy_status',
    'policy_term_months',
    'premium_amount',
    'product_name',
    'product_option_name',
    'product_type',
    'product_sub_type',
    'reinstatement_date',
    'signed_date',
    'transaction_type',
    'type',
    'document_id',
    'import_id',
    'writing_carrier_name',
    'geo_state',
    'split_percentage',
    'payment_mode',
    'group_name',
    'policy_date',
    'first_payment_date',
    'first_processed_date',
    'receivable_schedule',
    'commission_profile_id',
    'agent_payout_rate_override',
    'tags',
  ];

  return fields.reduce((acc, key) => {
    acc[key] = parseFieldValue(key, post[key]);
    return acc;
  }, {});
};

const queryFieldValues = async (
  account_id: string,
  filterList: string[],
  where: Prisma.report_dataWhereInput
) => {
  const queries = filterList.map<Promise<[string, any[]]>>(
    async (fieldName) => {
      const values = await filterFieldsCache.get(
        account_id,
        fieldName,
        where,
        async () => {
          const records: any[] = await prisma.report_data.findMany({
            select: { [fieldName]: true },
            where,
          });

          return normalizeFieldValues(
            records.map((record) => record[fieldName])
          );
        }
      );

      return [fieldName, values];
    }
  );

  const resultEntries = await Promise.all(queries);

  return Object.fromEntries(resultEntries);
};
