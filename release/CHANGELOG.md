# release

## 4.27.7 (2025-07-17 18:58:00)

<details>
<summary>Changes</summary>

### Patch Changes

- d304fee: Zipped compensation report exports now include date suffix in individual files as well.
</details>

<details>
<summary>Previous Versions</summary>

## 4.27.6 (2025-07-17 05:07:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 6152d2b: Fix calculation issue caused by lifting the previousRate to the top of stetement processing
- a27b1ac: Populate accounting transaction details tag fields on comp calc for referral calc method.
</details>

## 4.27.5 (2025-07-16 08:33:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 355a19a: Fix sales rep payment value sign issue for DMI
</details>

## 4.27.4 (2025-07-16 05:31:25)

<details>
<summary>Changes</summary>

### Patch Changes

- cb7f978: Add timer stats for comp calc.
- 22c8690: Fix the endless re-render issue caused by dependency.
- dc4b5bd: Get the accurate sRate for referral calculation using the statement's effective date.
- 13db96c: Add sorting for processors and document profiles in the Admin Companies and Companies page, and disable unused sorting options in the data import page.
- a1be4d5: Use policy effective date if commission effective date is missing for seeing comp grid rates.
- 98cfb4f: Add an incremental dedupe setting that will not re-activate duplicated state records for grouping data processing.
- 9356559: Fix commission_remaining calculation issues of sales vps
- 47da833: Add calc method multiplier to agent commission log.
- 1c0daa0: Fix grouping failure caused by null premium_amount & split_percent
- 58ad7c5: Add keepRate and overrideSplit as calcMethods not needing compGridCriteria
</details>

## 4.27.3 (2025-07-15 19:44:51)

<details>
<summary>Changes</summary>

### Patch Changes

- 76863ad: Added ID (str_id) to commissions "Export with IDs" report export.
- f9d2910: Search bar in report group summary page now looks for report name in case insensitive mode.
- 75ce00f: Support cross entity actions for data actions tool.
</details>

## 4.27.2 (2025-07-15 17:28:12)

<details>
<summary>Changes</summary>

### Patch Changes

- f8bfc25: Introduced a “Discard Changes” button in Agent Transactions, allowing users to revert transaction updates
</details>

## 4.27.1 (2025-07-15 16:42:35)

<details>
<summary>Changes</summary>

### Patch Changes

- 3d2e81d: Fix allied retro payment allocation issues
</details>

## 4.27.0 (2025-07-14 21:58:47)

<details>
<summary>Changes</summary>

### Minor Changes

- cef0ab0: Added support for retro payment allocation
### Patch Changes

- 48371a9: Updated the statement amount extractor to exclude date-like values such as 06.16 or 6.16, also enable extracting the negative amounts.
- 987bc4b: Added pagination functionality to the agents transactions
</details>

## 4.26.3 (2025-07-14 15:34:20)

<details>
<summary>Changes</summary>

### Patch Changes

- 5547d60: Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
- 2ea98a7: Added tooltips on document status fields to provide more detailed information about document processing results and errors.
</details>

## 4.26.2 (2025-07-14 09:00:53)

<details>
<summary>Changes</summary>

### Patch Changes

- f53d05c: Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
</details>

## 4.26.1 (2025-07-11 22:36:31)

<details>
<summary>Changes</summary>

### Patch Changes

- ef7f01e: Producers will now only see commissions in paid or approved state.
- 35de74e: Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
- 9b35c90: Only sync policies with state = inforce, and sync commPrem & commAnnPrem
- 6fc06b2: Added locks to Views and Fields which are not editable.
- 2e3790a: Allied payment allocation improvements including UI & allocation adjustment
</details>

## 4.26.0 (2025-07-11 00:55:30)

<details>
<summary>Changes</summary>

### Minor Changes

- 6f082ac: Fix sorting params are not added to the url query when user sort any column.
### Patch Changes

- 720496e: Remove users with deleted access from Admin > Accounts
</details>

## 4.25.1 (2025-07-10 18:35:18)

<details>
<summary>Changes</summary>

### Patch Changes

- 5e15322: Sync transaction type , group name and sales vps for DMI account
</details>

## 4.25.0 (2025-07-10 05:23:58)

<details>
<summary>Changes</summary>

### Minor Changes

- d41d64a: On policy edit form, add search capability to geo state field.
</details>

## 4.24.1 (2025-07-09 22:08:06)

<details>
<summary>Changes</summary>

### Patch Changes

- deff3bc: - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
- a3980f0: Allow users to reset payment allocations
- a3980f0: UI adjustment for grouped commissions & payment allocations
- 534f1dc: Disable member count syncing feature for risk tag
- 6898cf2: Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

## 4.24.0 (2025-07-09 00:17:30)

<details>
<summary>Changes</summary>

### Minor Changes

- b4121db: Fix the pagination not working on many pages.
- 40dd850: Introduced bulk-add and batch-update functionalities to the commissions page.
### Patch Changes

- 89fe5b5: Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
- 54ec1df: Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
- 031d8f1: Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
- 3f63d9b: Fix the commission calculation failure with different calc basis
- a32cf9b: Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
- c5c689a: Fixes for comp reports not being approved
- c3bd7d7: Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
- 07887a5: Fix grouping failure caused by null premium_amount & split_percent
</details>

## 4.23.3 (2025-07-07 10:37:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 62df1bd: Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

## 4.23.2 (2025-07-07 09:57:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 5b43915: Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
</details>

## 4.23.1 (2025-07-07 02:54:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 401322d: Only calculate renewal profiles if the statement’s compensation type is Renewal Commission.
- 9722a24: Improves performance by minimizing database load for all required data in comp calc.
</details>

## 4.23.0 (2025-07-06 06:51:15)

<details>
<summary>Changes</summary>

### Minor Changes

- 04d824e: Support payment allocation which can be enabled by setting FeatureFlags {paymentAllocation: true} in account configs
### Patch Changes

- 8b49c3a: Fix for auto populate house rate and rate when creating new lines in comp grid viewer for grids with only carrier rate.
- ebd2e55: Fixed bug for data actions tool where a deleted action still takes effect on data preview
</details>

## 4.22.7 (2025-07-06 04:07:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 884fab0: Fix issue where created_by is missing when uploading a document.
- e93c5d8: Fix companies unable to update document profile mappings due to unique constraint violation in "companies_document_profiles".
- 7b3e302: Fix file upload API failing due to missing service configuration
</details>

## 4.22.6 (2025-07-04 22:50:27)

<details>
<summary>Changes</summary>

### Patch Changes

- ab5b67b: Fixed agent transaction save failure when all transactions had been deleted
</details>

## 4.22.5 (2025-07-04 17:56:46)

<details>
<summary>Changes</summary>

### Patch Changes

- b6aca2a: Reduce classification confidence threshold to 0.8 to allow more files result.
- 6a7dee2: In admin accounts, sort users by active first. Add user state formatter.
- c925d4a: `Total premium` now available in saved report group views and exports.
</details>

## 4.22.4 (2025-07-04 07:57:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 4bd2560: Not calc receivables for sale reps.
</details>

## 4.22.3 (2025-07-04 06:54:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 86432e1: Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
- 02a0e63: Hotfix the document processing page crash issue
- d023076: Show virtual & virtual type in commission data view
</details>

## 4.22.2 (2025-07-03 18:09:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 78c6ede: Fix for search bar not working on multiple app pages.
</details>

## 4.22.1 (2025-07-03 06:25:20)

<details>
<summary>Changes</summary>

### Patch Changes

- daa48af: Allow user to select manual grouping calculation method
- fe07088: Fixed "Hide commissions with payout" filter on Commissions page that was not returning expected results
- 54c1f53: Fix the issue where the page crashes when selecting an existing mapping.
</details>

## 4.22.0 (2025-07-02 08:45:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 034e734: Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.
### Patch Changes

- c75dbd1: Fixed an error that occurred when updating data for the 'state' field
- 438f1cb: Correctly populate fields value into generated grouped statement, including new_commission_rate, contacts, split_percentage, agent_commission_payout_rate and agent_payout_rate
- 31a6bcd: Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
- c6b8ebd: Support running groupings with rules event with no default grouping settings
- f2b8da2: Trigger multi-company mode when multiple documents are uploaded and no classification results are available. And include company and document type details in the upload preview page. Also align the right of type selector.
- 320204a: Fix for comp calc not getting results for Hitchings account
- 9c7a7bf: Allow user to override policy splits
- e63ac1e: Fix the bulk edit issue that when we update the any field the date field(like deposit date) will be auto updated
- 129de78: Fix the bug of paying current agent if hierarchy processing is none
  Fix the bug of associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
  Fetch all comp profiles for comp profile matching service
- e971a40: In the past, importing a file would automatically create a new mapping, often resulting in many similar or duplicate mappings. Now, users must now click the ‘Save’ button to confirm whether they want to keep the mapping.
</details>

## 4.21.5 (2025-07-01 05:15:06)

<details>
<summary>Changes</summary>

### Patch Changes

- 82abbf2: Enhance the data update tool to support executing queries independently of custom code logic.
- 3f1966a: Fixed grouping failures due to foreign key constraint violations
</details>

## 4.21.4 (2025-06-30 19:27:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 5015a9a: Update front-end and API to support one-to-many carrier relationships.
- a8c926c: Add bulk receivable calc for policy.
</details>

## 4.21.3 (2025-06-28 01:04:43)

<details>
<summary>Changes</summary>

### Patch Changes

- b690e48: Set policy_year_end to start year when end year is `Not applicable'
- f0e0e59: Updated transaction management on the Agents page
</details>

## 4.21.2 (2025-06-27 16:51:32)

<details>
<summary>Changes</summary>

### Patch Changes

- e3e8c66: Enhanced performance for transaction management on the Agents page
</details>

## 4.21.1 (2025-06-27 16:03:26)

<details>
<summary>Changes</summary>

### Patch Changes

- ca19604: Added a new display-only Excess field to the Policies page, consistent with fields like Target Premium and Annualized Revenue.
</details>

## 4.21.0 (2025-06-27 04:25:17)

<details>
<summary>Changes</summary>

### Minor Changes

- 65a04c6: Add validation of date range when bulk-updating agent carrier grid levels.
### Patch Changes

- ced82c3: Optimize the Document Profile page:
  1. Add account company selection and display.
  2. Add document sync functionality to sync documents with related companies.
  3. Add sorting functionality to the Document Profile view.
  4. Add pagination to the Document Profile view.
- 05a6ccf: Hotfix the validation issue where clicking ‘Confirm’ causes some fields to be missing.
- deee7cf: Enhanced SO Worker to support configuration of custom API endpoints
</details>

## 4.20.2 (2025-06-26 19:28:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 4fb97e7: Add hierarchy tag for comp report creation if the comp calc method used is not referral. Only for Transglobal account.
</details>

## 4.20.1 (2025-06-26 04:21:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 7470e72: Delete orphan virtual records after the grouping/deduping process
- 8974884: The generated virtual records will carry over the document_id from the grouped items, and the commission rate will retain two decimal places.
</details>

## 4.20.0 (2025-06-25 16:02:09)

<details>
<summary>Changes</summary>

### Minor Changes

- 10d636e: Fix an issue that statement amount is null after uploading statement successfully.
</details>

## 4.19.3 (2025-06-25 03:00:44)

<details>
<summary>Changes</summary>

### Patch Changes

- b913331: Fix TWC agent payout rate calc missing data for comp profile sets
- dc0162f: Do not calculate for the uplines of agents whose compensation profile has hierarchy processing set to none.
- 04101ec: Display an alert message in the agent compensation calculation log if both payer and payee rates are null.
- ad51d3c: Improve reconciliation performance
- 89aaadc: Fix agent receivables not showing up on commissions page
- 115b0f1: Upgrade the data update tool to handle code execution with Prisma integration and support for Promises.
- 5d06718: Update TWC agent payout rate formula and support multi-carrier comp profiles
</details>

## 4.19.2 (2025-06-24 08:09:30)

<details>
<summary>Changes</summary>

### Patch Changes

- ecfacd9: Fix comp grid query performance
- 3624939: Fix the issue where commission rate was stored in decimal format instead of percentage
- 8669195: Added auto header removal for spreadsheet processors to handle inconsistent mappings.
- 134f822: Added support for select statements based on processing date range when grouping
- 5ed59a1: Sales representatives should pay their uplines when the "Agents' Hierarchy Upline" option is selected in compensation profiles.
- 88f2bf0: Fixed the issue where manual grouping did not respect the useVirtualOption flag.
- fab9042: The Agent Receivable and Override Receivable values should not be multiplied by the split percentage.
</details>

## 4.19.1 (2025-06-23 02:31:10)

<details>
<summary>Changes</summary>

### Patch Changes

- 88da7c8: Add `Case Manage Pool` to the agents for BGA account
</details>

## 4.19.0 (2025-06-21 05:55:46)

<details>
<summary>Changes</summary>

### Minor Changes

- 9c0e9f9: SO integration now syncs policy splits when syncing policies
### Patch Changes

- 216d9c6: Fix the issue where the commission amount's sign does not align with the payout rate's sign.
- 535bb48: Only updates the newly updated carrier data for MAG worker
- 6ff9e59: Allow uploads up to 25mb for data submitted through the data update api.
- 7eab225: Refactor getAgentHierarchy method to accept date range parameters
</details>

## 4.18.0 (2025-06-19 20:18:24)

<details>
<summary>Changes</summary>

### Minor Changes

- 370baa2: Implemented BA MOO-specific grouping calculation method
- 827e408: Fix issue that user can't edit agents.
- dff81b3: The whole row of linked commissions should be grayed out.
### Patch Changes

- 6e1e7f0: Fix comp profile set not saving with effective date
</details>

## 4.17.1 (2025-06-19 02:50:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 31fe4fa: Add relation field Commission → Commission amount to dashboard for Policies
  Remove scrollbar from widget
  Automatically select appropriate formatter based on the selected field
  Update UI: instead of using Policies.customer_name, display as Policies → Customer name
- 11c1020: Fix the issue where reading undefined data when login
</details>

## 4.17.0 (2025-06-19 01:20:20)

<details>
<summary>Changes</summary>

### Minor Changes

- 1ec6573: Fix issue users can't bulk update documents
- 2c36d77: RiskTag now can sync member count back to the BenefitPoint
### Patch Changes

- 36bd470: Add flags filter for commissions.
</details>

## 4.16.1 (2025-06-18 23:17:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 91e6f3c: Align agent_commission_payout_rate between web and export
Fix per policy scope calc not accounting for previously used commissions (Trawick)
Fix receivable calc not including company filter when looking up agent level
</details>

## 4.16.0 (2025-06-18 06:00:21)

<details>
<summary>Changes</summary>

### Minor Changes

- bcf5620: Consolidate comp grid products when syncing with MAG worker
- 3f30a1b: The new grouping process generates virtual records based on the grouped items and infers the correct premium amount and commission rate according to the calculation method specified in the grouping rules.
### Patch Changes

- d2d710e: Fix the issue where the writing agent's split incorrectly shows 100% instead of 0% when the effective split for the downline agent is 0 or NaN.
- 3f30a1b: This change allows clients to set up grouping rules by specifying on how to filter the data and how the grouping keys are transformed for specific rule
- 65521c1: Optimise the Document import trends and Company documents count charts to show "manual" vs "auto" import methods.
- b81f801: Fix the issue where the global company cannot be updated.
</details>

## 4.15.0 (2025-06-17 07:40:08)

<details>
<summary>Changes</summary>

### Minor Changes

- e54ba48: Add new data tool for bulk updating agent carrier grid levels.
- 7859287: Fixed issue of missing agent names of agent commission total row on commissions page.
### Patch Changes

- ace26b2: Fix the issue where the new processor resets to default on every action and cannot be created. Also remove the old document profiles logic from the processor page.
- 71030fc: Update the data update preview API and frontend to display commission flags and their log.
- f7af2f8: Add POLICY_STATE as geo_state for Transglobal data sync.
- 1147431: Add agency receivable for commission from the policy when the commission's agency receivable is missing or null.
- e7d871c: Add 'All' sheet option for multi-sheet spreadsheets to combine all worksheets with source identification, also apply the logic the related part auto/manual document processing.
- c6549fd: Now MyAdvisorGridsWorker supports syncing agent hierarchy and agent level
- 7aac4f8: Associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
</details>

## 4.14.5 (2025-06-13 06:46:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 0b545a3: Hotfix the issue where the page crashes when selecting a new extraction on the “Create new processor” page. Also fix the extraction structure issue, where the result includes unnecessary data and cannot be properly split into Tables and Lines.
- 4eba4f8: Add support for WorldChanger's custom agent payout rate calc.
</details>

## 4.14.4 (2025-06-12 23:32:27)

<details>
<summary>Changes</summary>

### Patch Changes

- bb757e1: Add total amount in the bottom of comp report excel export
- 5607e58: Resolve the issue where the writing agent's split is calculated incorrectly based on the downline agent's data.
- 1d887fd: Report processors - replaced codemirror with fully featured processor playground
- 62fa847: Fix the issue where "data_imports" can't be created due to relational field conflicts, and add "processor_str_id" and "mapping_str_id" fields to "data_imports" during both manual and auto import processes, then display these fields in the data imports view.
- fa3c735: Include the old comp_grid_levels relation when doing the receivable calculation
</details>

## 4.14.3 (2025-06-12 06:08:12)

<details>
<summary>Changes</summary>

### Patch Changes

- b076570: Ensure the payout rate is accurately adjusted based on the payee rate of the current agent and the payee rate of their downline agents.
</details>

## 4.14.2 (2025-06-12 05:20:21)

<details>
<summary>Changes</summary>

### Patch Changes

- aadec40: Allow to update accounting transaction dates from agents view.
- ef4b827: Ensure the payout rate is accurately adjusted based on the payee rate of the current agent and the payee rate of their downline agents.
- db4dd35: Fixed the Issue: In certain scenarios, reconciled statements were not assigned the agent from their corresponding reconciled policy.
- a36e031: Showing the account name instead of the account ID on the Global Companies page, let user easier to identify each account. Also add account filter for Global Companies page.
</details>

## 4.14.1 (2025-06-11 09:39:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 9b0b1ca: Fix issue where clearing 'statement amount' or 'bank amount' caused save to fail. Previously, when users cleared the values (set them from a number to empty), the fields became undefined, which led to validation or parsing issues that prevented document saving.
- 7bdae93: Allow selecting more options beyond ‘Z’ (e.g., ‘AA’, ‘AB’) in the column dropdown for mapping in document processing, and sort all options alphabetically.
- 536c4cb: Integration tests now run in docker container and local db
</details>

## 4.14.0 (2025-06-10 19:59:47)

<details>
<summary>Changes</summary>

### Minor Changes

- 3bbaa23: Fix the issue of showing "(not found in Fintary)" for the columns carrier/mga and paying entity on commissions page.
### Patch Changes

- 6caef09: Add profit and % profit to reconciliation
- b30fba2: Fix newly created date range not showing.
</details>

## 4.13.1 (2025-06-10 09:35:04)

<details>
<summary>Changes</summary>

### Patch Changes

- d920336: Add support for importing Fintary format comp grids
</details>

## 4.13.0 (2025-06-10 05:21:34)

<details>
<summary>Changes</summary>

### Minor Changes

- 49139d1: On Documents page, update Commission totals validation to include bank amount in (in addition to commission records and statement amount).
### Patch Changes

- c80cd7e: Fixed agent compensation values when exports CSV from comissions page.
- 048751e: Fix for accounting transactions in agent page not showing the total amount correctly.
</details>

## 4.12.0 (2025-06-06 16:02:22)

<details>
<summary>Changes</summary>

### Minor Changes

- 566851e: On commissions page, add clickable link to columns that have agent tag names.
- 0c7e4c0: Added mapping support for e2e document processing. Now users can select a mapping under the document profile within the company page. If the uploaded file is a spreadsheet and no appropriate processor is available, the system will run the selected mapping and automatically import the data provided all conditions are met.
### Patch Changes

- 9e184d5: Enhance both the front-end and back-end of the data update configurations to display data update actions and criteria clearly.
- 464a508: Add multiplier check for no useCompGrid in comp calc.
</details>

## 4.11.5 (2025-06-05 19:50:15)

<details>
<summary>Changes</summary>

### Patch Changes

- e7d1191: Fix the issue where classification couldn’t run during email upload.
- 8f8fb64: Add customer name to reconciled lines in commission data
</details>

## 4.11.4 (2025-06-05 15:59:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 0733e5f: Fix sorting on the commissions page for commission rate, commission amount, and premium amount
</details>

## 4.11.3 (2025-06-05 05:10:30)

<details>
<summary>Changes</summary>

### Patch Changes

- a948b3c: Add a “Custom” criteria method for data actions criteria.
- 16c9861: Fix the issue where receivable calc is not working due to the relation change of comp_grids and comp_grid_levels
- 3c9c274: Fix the issue where a global company couldn’t be saved
- 70b9f4e: Add payer grid rates to agent commission calc log.
- 237ef12: When a statement has a period date that falls within the effective range of the policies, or exactly matches a policy’s effective_date, it is always reconciled to the newest policy.
</details>

## 4.11.2 (2025-06-04 23:15:56)

<details>
<summary>Changes</summary>

### Patch Changes

- 42a55c8: Add log formatter field in data update actions.
- 9a43b2d: Add missing grid levels column to agent exported csv
</details>

## 4.11.1 (2025-06-04 07:06:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 2bd0b17: Fix bug of comparing the agent's and downline's multipliers to calculate the agent's current multiplier and payout rate.
</details>

## 4.11.0 (2025-06-04 04:02:21)

<details>
<summary>Changes</summary>

### Minor Changes

- c1fdfb5: Implement the new database model for DocumentProfile and its relationships. Updated the related codebase across multiple modules and built a new document profile management view.
  Key changes include:
  1. New DocumentProfile view
  - Allows creating, editing, viewing, and deleting individual document profiles.
  - Supports linking profiles to documents, global companies, mappings, prompts, and various configuration settings.
  - Shows the total document count under each profile and allows accessing special files via links.
  2. Document view updates
  - Enables document selection of an associated DocumentProfile.
  3. Company / Global company view updates
  - Allows associating companies with specific document profiles.
  4. Processor, Processor selector, E2e document processing, Data import, and Mapping page updates
  - Updated relationship logic to support the new DocumentProfile structure.
- 05a7abf: Replaced the 'Expected Receivables' column with 'Agency Receivable', 'Agent Receivable', and 'Override Receivable' columns for commissions and policies.
### Patch Changes

- 721040a: Add support for comma-separated values in CONTAINS and NCONTAINS operators in field matcher when 'Treat as array' option is true.
- e9350e1: Fixed bug where users could not save global processors (which are associated with a different account) in the companies page
- 9693592: Added reverse comp report approval script to code base
</details>

## 4.10.4 (2025-06-03 06:52:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 55f50c6: Compare the agent's and downline's multipliers to calculate the agent's current multiplier and payout rate in comp calc.
- 78c8c83: Hotfix the issue where the document edit page cannot be saved.
- 8b66187: Add support for setting json fields (like agent_commissions) in data actions tool
- b021d94: Fix currency format for premium amount and agent commission data in excel report file
- 52bcefe: Fetching comp grid level when comp_grid_id is null in comp calc.
- d0ea4bf: Switch the order of the carrier grid level name and the house payout grid level name in comp profile view/edit.
- a6e36b3: Ensure that the payee and payer compensation grids are filtered correctly.
  Ensure comp profile matcher running correctly.
- 140026b: 🚀 Allow users to enable data syncing feature by adding integration configurations
</details>

## 4.10.3 (2025-05-29 18:53:31)

<details>
<summary>Changes</summary>

### Patch Changes

- d291b3a: Introduced integration tests covering companies, comp grids, users, and reports (more to come)
</details>

## 4.10.2 (2025-05-29 10:07:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 00d3fcc: Enable search for Company selector in Agent arrier/agency levels
- 6ff4e82: Fix the issue where agents syncing will fail when no crm agents are returned
- e1cf732: Addressing the issue of multi-carrier comp profiles not retrieving the correct grid levels.
- c4397fa: When running bonus calculations, take the split_percentage into account
- 00d3fcc: Reduce web app update notification from 12 hrs to 4 hrs after an update is available
</details>

## 4.10.1 (2025-05-29 05:39:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 370f0c9: Enhance the compensation profile matching logic to support multi-carrier compensation profiles.
</details>

## 4.10.0 (2025-05-29 04:41:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 35d57af: Replaced the 'Expected Receivables' column with 'Agency Receivable', 'Agent Receivable', and 'Override Receivable' columns for commissions and policies.
### Patch Changes

- d8d9098: Ensure large value of currency is displayed in decimal format, not scientific notation
- 8ed0dde: Add additional metrics:
  • Pie chart for document upload sources (Web, API, Email)
  • Pie chart for document import methods (Manual, Auto)
  • Pie chart for E2E document processing statuses (Processing, Pending Review, Processed - Auto)
  • Line chart for E2E document imports over time
- e044dbf: Display the agent name with the amount in agent commisssions when agent settings are configured to show agent downline commissions.
  Updated logic for calculating report amounts to prioritize specific agent commissions based on contact ID.
- d01075d: Tweak the document edit dialog by adding highlight to ‘statement_amount’ when it requires verification, and prevent auto-focus on the 'notes' field to avoid unintended focus during save or validation errors.
</details>

## 4.9.3 (2025-05-28 07:46:41)

<details>
<summary>Changes</summary>

### Patch Changes

- bf55b98: Update compensation calculation logic to handle profiles with hierarchy processing set to 'none'.
- 508ba59: Fix ungroup issue caused by build issue of losing original file path info
- a1090ba: Support matching all for multi-carrier comp profiles without specific carriers in comp calc.
</details>

## 4.9.2 (2025-05-27 08:44:04)

<details>
<summary>Changes</summary>

### Patch Changes

- c37b6c7: Fix splits syncing failure caused by not coercing policy number to string
</details>

## 4.9.1 (2025-05-27 06:47:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 575a017: Fix popoulating missing agents
</details>

## 4.9.0 (2025-05-27 02:51:45)

<details>
<summary>Changes</summary>

### Minor Changes

- d0b9459: Enable syncing agent splits for AgencyIntegratorWorker & syncing & linking all agents for policies
### Patch Changes

- 99a8bbd: Fetch correct comp grid for multi-carrier comp profile for comp calc
- 00ffb53: Removed the task locking to allow concurrent document processing for the same account.
- ce76de8: Add a “required” message to the company and type inputs and tweak the UI to prevent users from needing to click “Save” twice.
- 384209d: Added agents agency levels and carrier levels
</details>

## 4.8.2 (2025-05-26 22:54:22)

<details>
<summary>Changes</summary>

### Patch Changes

- dce7789: Fix date filter at document page not working properly and show error when end date is before start date
- 1fbbef2: Update commissions filters order
</details>

## 4.8.1 (2025-05-26 22:33:37)

<details>
<summary>Changes</summary>

### Patch Changes

- d57ee38: Fix: target premium value is not formatted as currency in reconciliation and policy export file
</details>

## 4.8.0 (2025-05-26 08:04:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 26d347a: Improve ui/ux in Comp grid viewer (collapse columns, date ranges, improve btns)
### Patch Changes

- 8468e71: Fix the document upload api failure caused by service binding
- 67dd206: Changed multiline-text input to codemirror for report processors. Added a few rendering UI fixes as well for the report processor views.
- 19f79a6: Filter the testing fields 'receivable_value_agency_rate', 'receivable_value_agent_rate', 'receivable_value_override_rate' to solve the commission page can't be loaded issue.
- 83bac27: Fix for comp grid rates not saving on comp grid viewer for new ordered grid levels
</details>

## 4.7.3 (2025-05-23 05:24:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 4bdaf92: Update "DocumentProcessing" worker to use existing data processing record through "dataProcessingService" instead of creating new record, fixing the issue of missing account information in processing logs.
- ec09247: Comp reports fix for agents not showing up and filtering out sales reps
- e4b9ae8: Add option for include grouped commissions in the comp reports
</details>

## 4.7.2 (2025-05-22 07:15:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 9cd0c0b: Add support for company, document type, and statement classification in email and API document uploads. If conditions are met, include them in the auto document processing flow. Also, add a verification system for this field.
- 0c130ca: Fix wrong date parameter names when opening document url at document group page that has group by value upload, deposit, or processing date
- fdd3f25: Fix the issue that some files may fail to be extracted by ExtractTable in e2e document processing when uploading a large number of files.
- 2715ab5: Fix crash on document processing when processors have null names.
- 0a0b174: Fixed bug where the generate comp payout report pane would crash on invalid processing start and end dates.
</details>

## 4.7.1 (2025-05-21 06:56:06)

<details>
<summary>Changes</summary>

### Patch Changes

- c2c85c7: Added support for displaying rates in the comp grid viewer based on ordered comp grid levels. This feature respects agent settings (ExtendedDownlineDataAccess.compGridRates) and applies to all accounts.
- 90662d0: Allow users to update synced agents
- adf3323: Return compensation type = Renew if policy start year is empty or gte 1
- 0d88d3f: Tweak the Document page UI:
  1. Add more space to the "Status" column to ensure the ✨ label is always visible.
  2. Update the delete restriction for non-admin accounts: previously, only documents with status "New" could be deleted, now allow deletion unless the status is "Processed".
  3. Restrict editing of the Status field to Fintary admins only, to prevent non-admin users from using it to delete processed documents.
- f4f3256: Re-add the upload source icon to both the Document page and the Admin Document page.
- d00b60f: Integration between ui and api for comp profile matcher tools.
- 51c4e1c: Resolve bugs in update and add functionalities for multi-carrier mode compensation profiles.
- 9c68ba6: Data actions tool: Extended the within operator to handle dates within a range that starts before or after the reference date
</details>

## 4.7.0 (2025-05-19 07:04:29)

<details>
<summary>Changes</summary>

### Minor Changes

- 7952175: Implemented global ordered comp grid levels
</details>

## 4.6.3 (2025-05-19 05:59:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 727609f: Remove reporting of invalid account identifier errors to Sentry.
- 326ba40: Enhance commission calculation to support retrieving applicable compensation profiles for both single-carrier and multi-carrier modes.
- 819e6db: Add a 'Blank' option to the carrier select dropdown in the comp profile view.
</details>

## 4.6.2 (2025-05-19 03:33:36)

<details>
<summary>Changes</summary>

### Patch Changes

- 24d046d: Improve policy and commissions database indexes to improve filter and filter option performance
- 442592b: Only show "Show duplicates" option for Fintary admins
- fabeb4b: Dashboard: Fix producer selection, enable search, refactor/clean up ui
</details>

## 4.6.1 (2025-05-17 00:10:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 8339cdd: Add comp profile matcher ui
</details>

## 4.6.0 (2025-05-16 02:02:45)

<details>
<summary>Changes</summary>

### Minor Changes

- f4256c2: Added report processor capability for custom policies and commissions exports.
### Patch Changes

- b26d504: Fix data actions criteria for payment_date
</details>

## 4.5.5 (2025-05-15 21:02:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 90805d1: Optimise BA agents syncing and the logic of linking agents to policies
- be6c467: Update DMI integration mappings, agents linking and support incremental policy syncing
</details>

## 4.5.4 (2025-05-14 09:05:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 6776e5a: Support comp calc in accounts with > 32k agents
</details>

## 4.5.3 (2025-05-14 01:45:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 7cb1280: Resolve limitation of 32k agents in dynamic-select endpoint
</details>

## 4.5.2 (2025-05-13 21:08:17)

<details>
<summary>Changes</summary>

### Patch Changes

- 61456e7: Fix an issue where individual exports in the compensation report were missing most of the expected files.
</details>

## 4.5.1 (2025-05-13 18:51:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 7ed2e81: Limit file name width in document page, merge file and override file columns, show override file below in same row, move (+) icon to end of File column.
- 5311e4f: Update the compensation profile view and form to support both single-carrier and multi-carrier modes.
- b82b615: Link comp grid products with company_products to enable full functionality
- 96483a5: Add E2E document processing with log and improve filtering by "processors" and "import status" in the workflow.
- 2bdf235: Fix an issue where producers could view policies belonging to other producers.
</details>

## 4.5.0 (2025-05-12 07:33:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 86323c3: Fix issue of document column not showing name at policies page. Improve policy edit form by using new dropdown select.
### Patch Changes

- 9b4c1fd: Fix: allow synced data update after integration is disabled
- d1c05cb: Fix bug related to adding commission profile sets
- 1a03924: Consider policy_start_year when inferring compensation type and default to "Renew" if over 1 year
</details>

## 4.4.0 (2025-05-12 00:30:02)

<details>
<summary>Changes</summary>

### Minor Changes

- 31e8ca9: Agent settings, allow upline to view downlines commissions and policies based on hierachy and based on grid payout levels
### Patch Changes

- 419ac0b: Added custom error toasts for SignIn errors (firebase)
- 5b3177a: Fix issue with BA compensation grid syncing where product names were not populated correctly.
</details>

## 4.3.1 (2025-05-08 16:58:20)

<details>
<summary>Changes</summary>

### Patch Changes

- d0be894: Fix the issue where the AgencyIntegrator fails to sync entities for large dataset accounts due to excessive data being queried in a single request
</details>

## 4.3.0 (2025-05-08 07:54:09)

<details>
<summary>Changes</summary>

### Minor Changes

- 11e4118: Fixed table action -> more menu in wrong position and improve its ux. Fixed label and value of input overlap at edit form.
- 5537b78: Add new filters to commissions and policies: transaction type and group name
### Patch Changes

- fdfca8e: Fix the issue where companies can’t be selected in multi-company mode, and make the "payment_method" field to "RiskTag" accounts only.
- 53f5de5: Make the link icon for grouped commissions clickable, navigating to the associated commission line item where incl_linked=true.
- 630335f: Add payer_grid_level_name, payee_grid_level_name, and single_carrier_mode to the agent_commission_schedule_profiles table
- 0136db2: Fix issue where search box is difficult to focus on in Document Admin page.
- 5ed9a3a: Add an "upload_source" field and label to differentiate upload sources: Web, API, and Email.
</details>

## 4.2.0 (2025-05-07 18:07:03)

<details>
<summary>Changes</summary>

### Minor Changes

- f5a02e5: AgencyIntegrator now syncs products via the plan search endpoint
</details>

## 4.1.3 (2025-05-07 16:31:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 5bca96d: Enhanced commission calculation logging by including `Scope` and `policyCommissionsUse` details when calc method is `policyScopeCalcsNew`.
- 2f035ad: Fix the issue where "agent_commission" cannot be imported when the account admin is also an agent.
</details>

## 4.1.2 (2025-05-07 05:49:44)

<details>
<summary>Changes</summary>

### Patch Changes

- ee412cc: Default to `ACH` if payment_type is empty string in in BenefitPoint statement sync
</details>

## 4.1.1 (2025-05-07 04:42:53)

<details>
<summary>Changes</summary>

### Patch Changes

- ad25c3b: Fix bug where default pages were not visible under "View" and "Fields" for non-admin roles
</details>

## 4.1.0 (2025-05-06 19:04:25)

<details>
<summary>Changes</summary>

### Minor Changes

- cb28d7d: Add processing date and deposit date to filter of documents
### Patch Changes

- 9be6b1d: Exclude records in non-editable states by default when running grouping/deduping and reconciliation with grouping
- 024efbd: Fix issue at report page: Agent column should show name instead of id
- ba5a548: Implemented resend user invite feature
- 37c5975: Implemented new date operators (Within, before, after, n days/months/years) for the Data Actions tool.
</details>

## 4.0.4 (2025-05-05 08:33:39)

<details>
<summary>Changes</summary>

### Patch Changes

- d84ce2e: Fix the issue that when processor update the import statuses will change to 'none'
</details>

## 4.0.3 (2025-05-05 07:59:52)

<details>
<summary>Changes</summary>

### Patch Changes

- fae8cc1: Fix the document upload issue and enhance the UI by adjusting the select box.
- c00c194: Tweak the document processing workflow and resolve the issue where ExtractTable cannot be run during the workflow. Also fix the "split_percentage" validation issue. Implement an "process_method" field to distinguish between automatic and manual processing.
</details>

## 4.0.2 (2025-05-01 17:57:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 8629666: Fix for commissions agents field not being exported correctly in comp reports
</details>

## 4.0.1 (2025-05-01 17:10:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 922760a: Update BA/MAG sync to populate compensation_type from product name/sub
</details>

## 4.0.0 (2025-05-01 04:22:55)

<details>
<summary>Changes</summary>

### Major Changes
- c92df54: Can't select multiple value of agent select when doing search in dropdown box
### Patch Changes

- 8618daf: Allow using Shift key to select multiple rows
- dea47e4: Resolved mismatch between total agent commissions in comp reports and commissions page.
- 9a4e222: When syncing comp grid products using MAG integration, use the product name that synced from AI integration instead
</details>

## 3.15.2 (2025-04-30 04:36:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 7f20a58: Fix account validation error & passing account_id correctly
- ddf81b2: Updated cloud build scripts to use npm ci instead of npm i，downgraded nanoid version to ^3, and upgraded formidable verstion to 3.5.4
</details>

## 3.15.1 (2025-04-29 05:33:57)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f23b8e: Allow users to specify payment method when uploading documents and will set to corresponding payment_method when syncing to BenefitPoint
</details>

## 3.15.0 (2025-04-29 02:41:04)

<details>
<summary>Changes</summary>

### Minor Changes

- 6d21eb5: Pay to first active upline agent if current agent is in archived state
</details>

## 3.14.4 (2025-04-28 08:35:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 3ee249e: AI correctly populate the company_product_id
</details>

## 3.14.3 (2025-04-27 10:12:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 1632bba: Fix document edit failure caused by not correctly connecting account
</details>

## 3.14.2 (2025-04-26 02:13:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 1951576: Fix export agents function to export all agents
</details>

## 3.14.1 (2025-04-25 18:20:00)

<details>
<summary>Changes</summary>

### Patch Changes

- ae86be4: Fix deleting and inviting again an already invited user bug
</details>

## 3.14.0 (2025-04-25 16:24:04)

<details>
<summary>Changes</summary>

### Minor Changes

- f5ccea9: Add regression test feature in admin tools to validate new commission calc updates on provided use cases
### Patch Changes

- ba1f4cd: Filter agent options based on data from selected date ranges when creating comp reports.
</details>

## 3.13.3 (2025-04-24 17:48:54)

<details>
<summary>Changes</summary>

### Patch Changes

- 3e85e99: Remove the unused "web/common/tools" module and update related dependencies. Add new date types for "findAllDate" and "dateProcessor". Add a new utility function "splitAtPositions", and update the default processor code.
</details>

## 3.13.2 (2025-04-24 06:40:32)

<details>
<summary>Changes</summary>

### Patch Changes

- fa0c936: Refactor comp calc by adding all comp calc logics into a seperate modules and add more comp calc unit tests
- 2fbcea0: Update view & fields behaviour for no setting
- 23a2303: Fix bug of activity log export
- f01567a: Invited users onboarding workflow asking new users to edit account info fix.
</details>

## 3.13.1 (2025-04-23 05:06:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 6cd6444: Fix the bug of including invalid date for search params
- 945d223: Sync carrier rate to total rate & fix comp grid product display issue caused by no product name
- d759036: Optimize the export process for report_data and statement_data.
</details>

## 3.13.0 (2025-04-22 20:47:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 978d9b1: Improve performace of table and edit form, use server pagination for dropdown select
- 43c7b76: Allow to input statement month for documents
</details>

## 3.12.1 (2025-04-22 07:14:28)

<details>
<summary>Changes</summary>

### Patch Changes

- 857ba2d: Add a new Source to Dashboard: accounting_transaction_details
Add a company_id and company relation
</details>

## 3.12.0 (2025-04-22 05:40:44)

<details>
<summary>Changes</summary>

### Minor Changes

- c06c609: Extend logic for function alignSign to take the sign of rate
### Patch Changes

- 728a312: Fix the issue of sso logged user cannot view Reconciliation / Commissions / Policy data
</details>

## 3.11.4 (2025-04-21 21:17:05)

<details>
<summary>Changes</summary>

### Patch Changes

- 6ab2b77: Sync rates for product without returning carrier_product_criteria data
- 94488e3: Fix comp grid criteria syncing failure caused by not find comp grid product
- cf09c82: - Make sure notes and agent code are strings when syncing agents using SmartOffice worker
</details>

## 3.11.3 (2025-04-17 18:56:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 4b3826a: Add bulk edit to documents page
</details>

## 3.11.2 (2025-04-16 19:13:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 9f68a9c: Fixed policies agent payout rate override times 100 issues
</details>

## 3.11.1 (2025-04-16 08:46:24)

<details>
<summary>Changes</summary>

### Patch Changes

- a268f04: Fix Agent comp reports PDF exports resulting in "Error exporting data"
- c50236d: Add support for multiple actions per criteria meet in data actions tool
- 9c90f17: Update AgencyIntegorator carrier whitelist for Brokers Alliance
</details>

## 3.11.0 (2025-04-16 04:33:18)

<details>
<summary>Changes</summary>

### Minor Changes

- da7210c: Refactor agent.ts and separate all commission process logic to commissionProcessor.ts
- 5f801b7: Add sorting feature for widget builder to be able to sort and select N items based on field
</details>

## 3.10.1 (2025-04-15 19:29:17)

<details>
<summary>Changes</summary>

### Patch Changes

- afab314: Move gmail sync from cron job to cloud scheduler
</details>

## 3.10.0 (2025-04-15 06:51:42)

<details>
<summary>Changes</summary>

### Minor Changes

- 74127bc: Refactor logic of fetch profiles for agents that are not in hiearchy, only fetch profiles for those agents once
- a3d62ae: MAG worker now supports syncing comp grid data
### Patch Changes

- 90aa6d0: Add a "See comp grid rates" context menu to commission line items
</details>

## 3.9.0 (2025-04-14 08:34:07)

<details>
<summary>Changes</summary>

### Minor Changes

- 2b965fe: Fix statementAmountExtraction for document process, retrieve the amount by adding the unit test cases
- 752b383: Fixed the issue where retrying a computation after an error led to duplicate computations.
### Patch Changes

- 5187ae4: Fix error page caused by missing render function when rendering flags
</details>

## 3.8.0 (2025-04-12 00:25:16)

<details>
<summary>Changes</summary>

### Minor Changes

- fd2bc0d: Extract statement amount from filenames when uploading files.
- 887b478: Skip manually reconciled statements in reconciliation process and show Manual reconciler for manually reconciled data
### Patch Changes

- d843e1d: Include account shortname in email to upload documents.
  Make query chips for document states of Pending upload and Pending review Fintary admin only.
  For end users, show documents in Pending review state as Processing.
  Update uploaded at and imported at to be local time instead of UTC.
- b8e51db: Added clear field operation for data actions tool
</details>

## 3.7.1 (2025-04-11 05:51:30)

<details>
<summary>Changes</summary>

### Patch Changes

- d6298df: Update the document import process to allow importing the "agent_commissions", "agent_payout_rate", "agent_commission_payout_rate" and "agent_commission_status" fields. ("agent_commission_payout_rate" and "agent_commission_status" have ambiguous type issues, so please consult with the engineering team before using them.)
  Also, add normalization for them to ensure the values are either "currency" or "percentages". Additionally, implement a checker to verify that the key belongs to the corresponding "agent ID".
- 74d7473: PR template check improvement
- a53b76d: Fixed the issue in production that the widget grouped by Agents will result concurrent database calls, which throws exception
</details>

## 3.7.0 (2025-04-11 04:50:35)

<details>
<summary>Changes</summary>

### Minor Changes

- 366a5cb: When agent payout rate override is specified and the agents are not in the hierarchy of the writing agent of the statement, extra profiles of these agents will be fetch for further calculation
</details>

## 3.6.1 (2025-04-10 23:08:02)

<details>
<summary>Changes</summary>

### Patch Changes

- e8068b9: Fix duplicate applications of multiplier in comp calc
- 1bb9275: Improve comp grid load time and remove duplicate date ranges (take newest record)
</details>

## 3.6.0 (2025-04-10 17:36:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 9c93924: Add flags column for commissions in ui
- d83c6df: Add a "Pending upload" status for documents created in the "document" table but not yet uploaded to storage, to clarify the specific file status, and add a "Pending upload" status chip and query option in both the document and admin document views.
- d01793d: Allow users to ungroup grouped commission line items
### Patch Changes

- f66031b: Normalize agent_payout_rate and agent_commission_payout_rate data
- 18faa4f: Change activity log filters to a dropdown instead of chips, and add Gmail sync option
- e60b88f: Improve widget x-axis labels to be more likely to show the whole label.
- e5bff8b: Fix the issue on the processor page where the document field only links to documents within the current account, rather than globally. Now it can display all related documents.
</details>

## 3.5.2 (2025-04-10 03:17:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 7080ead: Remove "alignSign" application to custom methods Fixed Override and OverrideBonus, allowing negative rates to generate negative values.
</details>

## 3.5.1 (2025-04-09 19:22:36)

<details>
<summary>Changes</summary>

### Patch Changes

- c36e9e3: Add commission group alerts for commission that linked with others
</details>

## 3.5.0 (2025-04-09 18:58:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 2a41cc9: Allow to search whole text by using double quotes in dropdown select
</details>

## 3.4.2 (2025-04-09 16:55:43)

<details>
<summary>Changes</summary>

### Patch Changes

- cbc3d12: Correct the file name from the Gmail response
</details>

## 3.4.1 (2025-04-09 16:32:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 5c69112: Update export xlsx column width
Add nested report field when exporting comp report
</details>

## 3.4.0 (2025-04-09 05:04:14)

<details>
<summary>Changes</summary>

### Minor Changes

- 8d5da17: Add drag to scroll back to the table
### Patch Changes

- 2c1056c: Report status approval fix for reports created/approved before new approval workflow
</details>

## 3.3.0 (2025-04-09 04:43:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 1f93f5d: Replace the implicit '\_companies_processors' table with an intermediate table, and add an 'import_status' field to it. And update both the UI for companies and the admin companies UI to allow users to select the 'import_status'.
</details>

## 3.2.6 (2025-04-09 02:07:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 86ee2ec: In document exports, use formatted date, simplify override filename, and add statement_amount and notes fields.
- 10d451a: Updated the document processing page workflow
- a945e00: Use sorting table for widget table
</details>

## 3.2.5 (2025-04-08 16:39:43)

<details>
<summary>Changes</summary>

### Patch Changes

- 31729ec: Fix the issue where saving document edit requires clicking twice.
- 3405452: Optimize the feedback of syncing statements error for RiskTag, including showing statement IDs grouped by company, storing alerts and correctly saving stats.
- 8155963: Missing support for uploading some file extensions
- 6b07603: Two decimals formatter fix for BuddyIns account for agent payout rate field.
</details>

## 3.2.4 (2025-04-07 19:45:20)

<details>
<summary>Changes</summary>

### Patch Changes

- e1e4676: Fix exports failing when resolving too much relational data
</details>

## 3.2.3 (2025-04-07 04:03:59)

<details>
<summary>Changes</summary>

### Patch Changes

- 54593fc: Ignore 404 error when syncing statement back to BeneiftPoint service
</details>

## 3.2.2 (2025-04-05 01:45:13)

<details>
<summary>Changes</summary>

### Patch Changes

- aac0788: Fix document processing page crash when processor doesn't return valid results.
</details>

## 3.2.1 (2025-04-05 01:26:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 6274be7: Set target premium and annual revenue per BrokerAlliance's requirements for their AgencyIntegrator integration.
- 12293e7: Use localized Loader for widget preview instead of global (which blocks whole page)
</details>

## 3.2.0 (2025-04-05 01:05:09)

<details>
<summary>Changes</summary>

### Minor Changes

- b67f6b1: Remove dynamically generated agent_commission_payout_rate and agent_payout_rate columns and surface underlying fields (allows editing values).
- e61d1a5: Minor fixes and improvements to new table
</details>

## 3.1.3 (2025-04-04 23:21:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 739fc03: Fix multiplier ignoring 0 (defaulting to 100).
</details>

## 3.1.2 (2025-04-03 20:02:13)

<details>
<summary>Changes</summary>

### Patch Changes

- ac77d0d: Optimize manual grouping, improve wording/feedback, include additional fields.
</details>

## 3.1.1 (2025-04-03 19:42:11)

<details>
<summary>Changes</summary>

### Patch Changes

- 9ffb9c0: Handle the case that multiple criteria are matched, and filter out the ones not valid by checking effective date
- 2b89071: Use period_date for BenefitPoint applyToDate
</details>

## 3.1.0 (2025-04-03 19:16:59)

<details>
<summary>Changes</summary>

### Minor Changes

- 907e7e0: Add support to data actions tool for Array fields and operators (e.g. tags field)
</details>

## 3.0.0 (2025-04-03 07:12:14)

<details>
<summary>Changes</summary>

### Major Changes
- d5d99b4: Add index column to table for TransGlobal
### Patch Changes

- 2786f93: Add a "Bank total" field and include it in the export. Also replace the file path, optimise the upload date type in the export with the filename.
- a327c5f: In referral calc, use 0 if it's set as a rate (previously would ignore 0 if specified).
- 06bbcfe: Only enable `Bulk sync statements to BenefitPoint` button for RiskTag
- ff85e35: Retrieve payment mode from reconciled policies when exporting commissions. Refactor/consolidate config for which keys pull from policy data.
- b313d18: Move Gmail sync to Admin > Documents. Remove logout function.
- 6c37761: When opening link to a document's records in commissions/policies pages, use 'document_id' query param instead of 'q', so search is still available.
- 798ed11: Generate and save agentCommissionPayoutRate & agentPayoutRate in commission calc and store in real, editable columns.
</details>

## 2.0.1 (2025-04-02 05:34:40)

<details>
<summary>Changes</summary>

### Patch Changes

- 1b94387: Add index column to table for TransGlobal
- bd6c262: Update the default field on the statement uploading modal to be “Statement amount”
- af56926: In agent commissions log, rename "Agent split" to "Writing agent split" and remove code that updated this in each iteration, so we keep the original split.
- 83ba92e: Show product type in manual reconcile modal
- 59bc916: Allow user to manually group commission line items
</details>

## 2.0.0 (2025-03-31 16:35:33)

<details>
<summary>Changes</summary>

### Major Changes
- 95e51a5: Add an auto-import workflow for document processing.
### Minor Changes

- d933fe1: Add document type classification on document uploads
</details>

## 1.11.2 (2025-03-31 05:42:36)

<details>
<summary>Changes</summary>

### Patch Changes

- 45ad4bd: Fix receivable-reconciliation agent/agency rates amplified issue
</details>

## 1.11.1 (2025-03-31 01:27:28)

<details>
<summary>Changes</summary>

### Patch Changes

- 6aff761: Fix receivable mappings not searching all mapped company products
</details>

## 1.11.0 (2025-03-28 21:42:13)

<details>
<summary>Changes</summary>

### Minor Changes

- b1d37fd: Add support for bulk comp calculation from commissions page
### Patch Changes

- ab14b7a: Update reconciliation and comp calc to handle new `Offset` and `No payment` payout statuses (ignore)
- 1838f59: On policy split import, properly show error message.
  Also add loading indicator when request is processing.
- 6ef9772: Surface grouped policies in manual reconciliation
- 9fa449b: Surface policy's geo state into commissions when null for comp report and csv exports
- 95b51a0: Apply multiplier to commission calc
- 40ac26f: Added all fields to bulk edit, except for commission amount, commission rate and premium
</details>

## 1.10.6 (2025-03-27 15:12:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 9bed6ec: Support new referral calc for product Carematters
- bcae28c: Improve commission calc performance by separate grid rates query from the main query
</details>

## 1.10.5 (2025-03-26 15:57:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 23da6a6: Fix commission calcs error caused by relation query of passing too many variables in querying contacts_agent_commission_schedule_profiles_sets
</details>

## 1.10.4 (2025-03-26 15:51:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 2070a45: Force to re-initialize the OAuth2Client and set the refresh token
</details>

## 1.10.3 (2025-03-21 18:32:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 1af5603: Dashboard UX improvements
</details>

## 1.10.2 (2025-03-21 17:19:54)

<details>
<summary>Changes</summary>

### Patch Changes

- e6b12a1: Adding retry and remove unneeded try/catch
</details>

## 1.10.1 (2025-03-21 02:40:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 78895cc: Add missing apis google configs
- c620c6d: Implement feature for multiple data field calculation
</details>

## 1.10.0 (2025-03-20 20:06:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 7b3775d: Document group page: added new options for group by, added total row and few small ui/ux improvements
### Patch Changes

- 2ba163e: Fix payment date range query error in commission data view caused by passing Invalid Date string to server
</details>

## 1.9.0 (2025-03-20 09:54:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 95a6cf0: Support Agent production and commissions
  Data field aggregator update
- 4dfd92c: Add auto documents extracting workflow
</details>

## 1.8.3 (2025-03-18 15:34:10)

<details>
<summary>Changes</summary>

### Patch Changes

- b04aa7c: Send releases notifications
- 61b3d9c: Log out user only session expires, 401 errors will check if there'are logout action included
- f395517: Send releases to google chat channel
</details>

## 1.8.2 (2025-03-13 18:53:44)

<details>
<summary>Changes</summary>

### Patch Changes

- e433ffe: Grouping releases by date and only keep the latest 30 releases
</details>

## 1.8.1 (2025-03-10 06:20:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 0cd1cb9: Auto assign contacts with policies while syncing policies
</details>

## 1.8.0 (2025-03-06 18:02:35)

<details>
<summary>Changes</summary>

### Minor Changes

- cea4b59: Support session management for each account
</details>

## 1.7.14 (2025-03-05 03:03:11)

<details>
<summary>Changes</summary>

### Patch Changes

- f6eeeb0: fix bulk manual reconcile of only updating the contacts of the last statement
</details>

## 1.7.13 (2025-02-27 20:08:18)

<details>
<summary>Changes</summary>

### Patch Changes

- fa8f40d: Fix BGA commission calc failure Commission caused by no comp grid criteria
</details>

## 1.7.12 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- b8c3aa5: Feature: enable bulk syncing statements back to BenefitPoint
</details>

## 1.7.11 (2025-02-13 08:12:42)

<details>
<summary>Changes</summary>

### Patch Changes

- b405005: Fix syncing agents for onehq worker caused by schema changing & disable agentLevel syncing for now
</details>

## 1.7.10 (2025-02-13 06:45:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 6d9a483: BenefitPoint Worker now supports syncing acounts with type of prospect & client
</details>

## 1.7.9 (2025-02-13 01:29:52)

<details>
<summary>Changes</summary>

### Patch Changes

- fa56906: Supports bulk reconciling selected statements
</details>

## 1.7.8 (2025-02-11 21:19:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 089f3fb: Enable manual reconciliation on editable commissions
- ea72ed9: Fix the issue that manual reconcile doesn't populate contacts
</details>

## 1.7.7 (2025-02-10 08:38:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 93cd627: AgencyIntegrator assigns agents to policies based on agent_code
</details>

## 1.7.6 (2025-02-08 19:30:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 5ee03d5: Fixed scheduled syncing failure caused by connecting undefined user
</details>

## 1.7.5 (2025-01-30 05:32:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 6e5f9a5: Implement new logic for document processor selection and display suggested selectors to users.
</details>

## 1.7.4 (2025-01-29 01:02:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 587b554: Implement new logic for document processor selection and display suggested selectors to users.
</details>

## 1.7.3 (2025-01-28 21:25:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 3ba428c: Add master company, policy type, and terms to Customers.policies
</details>

## 1.7.2 (2025-01-27 03:27:44)

<details>
<summary>Changes</summary>

### Patch Changes

- b024bdd: Fix the issue of not displaying customer name caused by missing type & company name
</details>

## 1.7.1 (2025-01-24 05:23:00)

<details>
<summary>Changes</summary>

### Patch Changes

- 8bc2baf: For Transglobal syncing documents from AWS, attach documents to North American
</details>

## 1.7.0 (2025-01-23 18:44:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 299a16f: Feature: allow manually reconcile in the commission data view
</details>

## 1.6.9 (2025-01-15 14:13:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 3370cb6: Fix comp calc failure caused by undefined product
</details>

## 1.6.8 (2025-01-08 14:34:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 75369d9: AgencyIntegrator now populates PlacedDate to policy_date
</details>

## 1.6.7 (2025-01-07 08:06:27)

<details>
<summary>Changes</summary>

### Patch Changes

- a7e9a95: RiskTag data syncing now populates policy type to product sub type field base on product type
</details>

## 1.6.6 (2025-01-04 08:24:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 55edd31: Integrate with react compiler
</details>

## 1.6.5 (2024-12-28 09:07:51)

<details>
<summary>Changes</summary>

### Patch Changes

- 9e039c6: AgencyIntegator worker now populates personKey to agent_code
</details>

## 1.6.4 (2024-12-23 04:08:56)

<details>
<summary>Changes</summary>

### Patch Changes

- d26945a: AgencyIntegrator worker now supports syncing specified policies
</details>

## 1.6.3 (2024-12-22 07:20:38)

<details>
<summary>Changes</summary>

### Patch Changes

- f3bd7e4: Add permission check to documents endpoint and export endpoint
</details>

## 1.6.2 (2024-12-17 15:35:09)

<details>
<summary>Changes</summary>

### Patch Changes

- fb02f72: Add csv export for comp grid viewer
</details>

## 1.6.1 (2024-12-17 10:47:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 9176e5b: Fix sync icon displaying issue for uplines caused by not returning the config fields
</details>

## 1.6.0 (2024-12-16 07:22:50)

<details>
<summary>Changes</summary>

### Minor Changes

- bdef42d: Add openapi to allow user to upload a document via openapi calls
</details>

## 1.5.2 (2024-12-13 20:24:24)

<details>
<summary>Changes</summary>

### Patch Changes

- ae1330f: Fix the issue that the OneHQ worker didn't populate business age to the note field
</details>

## 1.5.1 (2024-12-12 13:08:58)

<details>
<summary>Changes</summary>

### Patch Changes

- 57a4e1c: Add alert in commission log for agents with no matching comp profiles
</details>

## 1.5.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- 1cbc8a5: Feat: support multi workers & integrate MAG syncing service
</details>

## 1.4.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- 667d7c3: Feature: add compensation type condition
</details>

## 1.3.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- 49a8f9f: Add support for stacked charts (carriers only)
### Patch Changes

- 8fed107: Move document type selection to the file level, enable adding/removing files with duplicate detection, collapse Date Picker and Total Amount sections, and optimize the UI.
- 49a8f9f: Add 'Enable updates' toggle for user impersonation (CSMs exempt)
</details>

## 1.2.1 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- c693840: BugFix: correctly populate commissionable_premium_amount when creating policy from commission_data
- 3b9fff9: Feature: SmartOffice integration: support syncing policies, agents, companies and products
</details>

## 1.2.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- a3b04cd: Feature: allow user to reset filters, added clear filter support to EnhancedDataView component
### Patch Changes

- 93b7f31: Feature: [AgencyIntegrator] populate product name into policy
</details>

## 1.1.5 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- c21e1f0: BugFix: Fix various cases around agent accounting transactions and comp report interactions
</details>

## 1.1.4 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 8d2b1f6: BugFix: Fix failure of updating agent when adding new contact_levels & uplines
- 7f60e9b: AgencyIntegrator: Fetch line of business as product_type and fix case where no carrier is found for a product
</details>

## 1.1.3 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 0db0931: Update agent_commission_payout_rate to: agent_commission / (commission_amount - non_sales_agent_commissions)
</details>

## 1.1.2 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- e2ed54d: BugFix: Update policy scope comp calc to only use agent's previous earnings (not sales rep)
</details>

## 1.1.1 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 39ad848: Feature: support overriding synced contact_levels & contact_hierarchy
</details>

## 1.1.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- 9872fd1: Initial integration with accounting transactions and agent balance
- 7cecdc3: Add support for per agent payout status
- 1244d2f: Feature: create policy from commission data
### Patch Changes

- 7cecdc3: Re-enable Tawk (Chat support)
- ddaac06: Fix: agents filter in policy data view
- 1dd5c54: Allow users to select different companies for each file during the document upload process.
- 7cecdc3: BugFix: Fix jitter when drag and dropping fields in Views & fields account config
</details>

## 1.0.1 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 05119a5: fix TransGlobal data sync failure by limiting concurrent queries
- d880d74: feat: add PermissionService to protect system resources from authenticated access
  - Implement PermissionService to control access to protected resources
  - Protects the commission data view related apis using the PermissionService
- e461619: Add Admin > Activity log, to view recent activity across accounts.
- 97b0fd0: chore: add changeset action
</details>

</details>
