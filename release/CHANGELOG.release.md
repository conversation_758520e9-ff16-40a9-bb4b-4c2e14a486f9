# release

## Releases on 2025-07-17

### Version 4.27.7
<details>

### Patch Changes
 - Zipped compensation report exports now include date suffix in individual files as well.
</details>

### Version 4.27.6
<details>

### Patch Changes
 - Fix calculation issue caused by lifting the previousRate to the top of stetement processing
 - Populate accounting transaction details tag fields on comp calc for referral calc method.
</details>

## Releases on 2025-07-16

### Version 4.27.5
<details>

### Patch Changes
 - Fix sales rep payment value sign issue for DMI
</details>

### Version 4.27.4
<details>

### Patch Changes
 - Add timer stats for comp calc.
 - Fix the endless re-render issue caused by dependency.
 - Get the accurate sRate for referral calculation using the statement's effective date.
 - Add sorting for processors and document profiles in the Admin Companies and Companies page, and disable unused sorting options in the data import page.
 - Use policy effective date if commission effective date is missing for seeing comp grid rates.
 - Add an incremental dedupe setting that will not re-activate duplicated state records for grouping data processing.
 - Fix commission_remaining calculation issues of sales vps
 - Add calc method multiplier to agent commission log.
 - Fix grouping failure caused by null premium_amount & split_percent
 - Add keepRate and overrideSplit as calcMethods not needing compGridCriteria
</details>

## Releases on 2025-07-15

### Version 4.27.3
<details>

### Patch Changes
 - Added ID (str_id) to commissions "Export with IDs" report export.
 - Search bar in report group summary page now looks for report name in case insensitive mode.
 - Support cross entity actions for data actions tool.
</details>

### Version 4.27.2
<details>

### Patch Changes
 - Introduced a “Discard Changes” button in Agent Transactions, allowing users to revert transaction updates
</details>

### Version 4.27.1
<details>

### Patch Changes
 - Fix allied retro payment allocation issues
</details>

## Releases on 2025-07-14

### Version 4.27.0
<details>

### Minor Changes
 - Added support for retro payment allocation

### Patch Changes
 - Updated the statement amount extractor to exclude date-like values such as 06.16 or 6.16, also enable extracting the negative amounts.
 - Added pagination functionality to the agents transactions
</details>

### Version 4.26.3
<details>

### Patch Changes
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - Added tooltips on document status fields to provide more detailed information about document processing results and errors.
</details>

### Version 4.26.2
<details>

### Patch Changes
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
</details>

## Releases on 2025-07-11

### Version 4.26.1
<details>

### Patch Changes
 - Producers will now only see commissions in paid or approved state.
 - Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
 - Only sync policies with state = inforce, and sync commPrem & commAnnPrem
 - Added locks to Views and Fields which are not editable.
 - Allied payment allocation improvements including UI & allocation adjustment
</details>

### Version 4.26.0
<details>

### Minor Changes
 - Fix sorting params are not added to the url query when user sort any column.

### Patch Changes
 - Remove users with deleted access from Admin > Accounts
</details>

## Releases on 2025-07-10

### Version 4.25.1
<details>

### Patch Changes
 - Sync transaction type , group name and sales vps for DMI account
</details>

### Version 4.25.0
<details>

### Minor Changes
 - On policy edit form, add search capability to geo state field.
</details>

## Releases on 2025-07-09

### Version 4.24.1
<details>

### Patch Changes
 - - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
 - Allow users to reset payment allocations
 - UI adjustment for grouped commissions & payment allocations
 - Disable member count syncing feature for risk tag
 - Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

### Version 4.24.0
<details>

### Minor Changes
 - Fix the pagination not working on many pages.
 - Introduced bulk-add and batch-update functionalities to the commissions page.

### Patch Changes
 - Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
 - Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
 - Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
 - Fix the commission calculation failure with different calc basis
 - Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
 - Fixes for comp reports not being approved
 - Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
 - Fix grouping failure caused by null premium_amount & split_percent
</details>

## Releases on 2025-07-07

### Version 4.23.3
<details>

### Patch Changes
 - Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

### Version 4.23.2
<details>

### Patch Changes
 - Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
</details>

### Version 4.23.1
<details>

### Patch Changes
 - Only calculate renewal profiles if the statement’s compensation type is Renewal Commission.
 - Improves performance by minimizing database load for all required data in comp calc.
</details>

## Releases on 2025-07-06

### Version 4.23.0
<details>

### Minor Changes
 - Support payment allocation which can be enabled by setting FeatureFlags {paymentAllocation: true} in account configs

### Patch Changes
 - Fix for auto populate house rate and rate when creating new lines in comp grid viewer for grids with only carrier rate.
 - Fixed bug for data actions tool where a deleted action still takes effect on data preview
</details>

### Version 4.22.7
<details>

### Patch Changes
 - Fix issue where created_by is missing when uploading a document.
 - Fix companies unable to update document profile mappings due to unique constraint violation in "companies_document_profiles".
 - Fix file upload API failing due to missing service configuration
</details>

## Releases on 2025-07-04

### Version 4.22.6
<details>

### Patch Changes
 - Fixed agent transaction save failure when all transactions had been deleted
</details>

### Version 4.22.5
<details>

### Patch Changes
 - Reduce classification confidence threshold to 0.8 to allow more files result.
 - In admin accounts, sort users by active first. Add user state formatter.
 - `Total premium` now available in saved report group views and exports.
</details>

### Version 4.22.4
<details>

### Patch Changes
 - Not calc receivables for sale reps.
</details>

### Version 4.22.3
<details>

### Patch Changes
 - Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
 - Hotfix the document processing page crash issue
 - Show virtual & virtual type in commission data view
</details>

