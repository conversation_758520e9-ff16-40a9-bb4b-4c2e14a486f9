# web

## Releases on 2025-07-17

### Version 10.20.1
<details>

### Patch Changes
- Updated dependencies [d304fee]
- Updated dependencies [77011c3]
  - common@0.24.12
</details>

### Version 10.20.0
<details>

### Minor Changes
 - Refactor top toolbar of EnhancedDataView including title, search box, filter list, and list of right actions.

### Patch Changes
 - Add testId support to EnhancedSelect's BaseSelect. If no testId is manually provided, it will now auto-generate one based on the selector’s label.
- Updated dependencies [e1511c5]
  - common@0.24.11
</details>

## Releases on 2025-07-16

### Version 10.19.18
<details>

### Patch Changes
 - Implemented ExportAccountsConfigsHandler to handle CSV export of account configurations
 - Fix the endless re-render issue caused by dependency.
 - Use policy effective date if commission effective date is missing for seeing comp grid rates.
 - Add an incremental dedupe setting that will not re-activate duplicated state records for grouping data processing.
 - - Align export Companies behavior with FE
- Updated dependencies [0666169]
- Updated dependencies [c828be7]
- Updated dependencies [47da833]
- Updated dependencies [1c0daa0]
- Updated dependencies [51ca668]
  - common@0.24.10
</details>

## Releases on 2025-07-15

### Version 10.19.17
<details>

### Patch Changes
 - Fix custom report paging
 - Support cross entity actions for data actions tool.
- Updated dependencies [75ce00f]
  - common@0.24.9
</details>

### Version 10.19.16
<details>

### Patch Changes
 - Introduced a “Discard Changes” button in Agent Transactions, allowing users to revert transaction updates
</details>

### Version 10.19.15
<details>

### Patch Changes
 - Include zero split commissions to comp report generation option
 - - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ContactsHandler` to export based on the new config.
  - Added unit tests for textFormatter.
 - Added an updated login page for a more visually appealing UX
- Updated dependencies [ad8f381]
- Updated dependencies [e38c119]
  - common@0.24.8
</details>

## Releases on 2025-07-14

### Version 10.19.14
<details>

### Patch Changes
 - Added pagination functionality to the agents transactions
- Updated dependencies [48371a9]
- Updated dependencies [cd9fde0]
  - common@0.24.7
</details>

### Version 10.19.13
<details>

### Patch Changes
 - Improved transaction management with pagination support and refactored backend logic
 - Added support for AI extraction(Gemini, ChatGPT, Claude, etc.) and enabled the ability to choose between multiple extraction methods in document processing.
 - - Introduced a new field 'processing_date' in the reconciliation configuration.
  - Enhanced filtering logic to handle fields specific to reconciliation version 2.
  - Added constants for reconciliation versions and fields to improve maintainability.
 - Added tooltips on document status fields to provide more detailed information about document processing results and errors.
- Updated dependencies [d6ee6a7]
- Updated dependencies [5547d60]
- Updated dependencies [fefa9a4]
  - common@0.24.6
</details>

### Version 10.19.12
<details>

### Patch Changes
 - Add custom report feature under the report to enable user upload custom report
 - Skip requirement for comp grid criteria if calc method doesn't require comp grid rates.
 - Customers page:
  - Move config from web to common, and add textFormatter for use in export.
  - Updated the `ExportCustomersHandler` to export based on the new config.
  - Added unit tests for textFormatter.
 - - Updated the useEffect hook in AdminDataSync to include initializeGoogleAuth as a dependency, ensuring proper execution.
  - Refactored initializeGoogleAuth to use useCallback for better performance and to prevent unnecessary re-creations of the function.
- Updated dependencies [f53d05c]
- Updated dependencies [1c9d5ce]
  - common@0.24.5
</details>

## Releases on 2025-07-11

### Version 10.19.11
<details>

### Patch Changes
 - Data actions tool: Add support to 'is not empty' operator for string fields (e.g. Group name field).
 - Added pagination functionality to the agents transactions
 - Added locks to Views and Fields which are not editable.
 - Allied payment allocation improvements including UI & allocation adjustment
</details>

## Releases on 2025-07-09

### Version 10.19.9
<details>

### Patch Changes
 - Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
 - Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
 - Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
 - Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
 - Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
 - Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
 - - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
- Updated dependencies [db2a894]
- Updated dependencies [624c907]
- Updated dependencies [df70eef]
- Updated dependencies [d519450]
- Updated dependencies [07887a5]
- Updated dependencies [c09430e]
  - common@0.24.4
</details>

### Version 10.19.10
<details>

### Patch Changes
 - - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
 - Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

## Releases on 2025-07-07

### Version 10.19.8
<details>

### Patch Changes
 - Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

### Version 10.19.7
<details>

### Patch Changes
 - Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
 - - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
- Updated dependencies [9e548c2]
- Updated dependencies [0a84843]
  - common@0.24.3
</details>

## Releases on 2025-07-06

### Version 10.19.6
<details>

### Patch Changes
 - Fix file upload API failing due to missing service configuration
 - Update metrics view to fix the issue where the table doesn’t separate "auto" and "manual" data, and to resolve the legend overlap in the ‘Company Documents Count’ chart.
</details>

## Releases on 2025-07-04

### Version 10.19.5
<details>

### Patch Changes
 - Fixed agent transaction save failure when all transactions had been deleted
</details>

### Version 10.19.4
<details>

### Patch Changes
 - In admin accounts, sort users by active first. Add user state formatter.
- Updated dependencies [6a7dee2]
  - common@0.24.2
</details>

### Version 10.19.3
<details>

### Patch Changes
 - Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
 - Hotfix the document processing page crash issue
 - Minor improvements to report processor playground and admin view.
 - Show virtual & virtual type in commission data view
</details>

## Releases on 2025-07-03

### Version 10.19.2
<details>

### Patch Changes
 - Fix for search bar not working on multiple app pages.
</details>

### Version 10.19.1
<details>

### Patch Changes
 - Updated report summary labels
 - Allow user to select manual grouping calculation method
 - Fix lint issue on companies page.
 - Fix the issue where the page crashes when selecting an existing mapping.
- Updated dependencies [daa48af]
  - common@0.24.1
</details>

