{"name": "@fintary/web", "version": "10.20.1", "private": true, "scripts": {"start-win": "set PORT=3001&& set REACT_APP_API=http://localhost:3000&& dotenv -e .env.prod react-app-rewired start", "dev-local-win": "set PORT=3001&& set REACT_APP_API=http://localhost:3000&& dotenv -e .env.dev react-app-rewired start", "dev-remote-win": "set PORT=3001&& dotenv -e .env.dev react-app-rewired start", "prod-local-win": "set PORT=3001&& set REACT_APP_API=http://localhost:3000&& dotenv -e .env.prod react-app-rewired start", "prod-remote-win": "set PORT=3001&& dotenv -e .env.prod react-app-rewired start", "start": "PORT=3001 REACT_APP_API=http://localhost:3000 dotenv -e .env.prod react-app-rewired start", "dev-local": "cross-env PORT=3001 REACT_APP_API=http://localhost:3000 dotenv -e .env.dev react-app-rewired start", "dev-remote": "PORT=3001 dotenv -e .env.dev react-app-rewired start", "prod-local": "cross-env PORT=3001 REACT_APP_API=http://localhost:3000 dotenv -e .env.prod react-app-rewired start", "prod-remote": "PORT=3001 dotenv -e .env.prod react-app-rewired start", "release": "npm run release --prefix ../", "build": "sh build.sh", "deep-clean": "rm -rf node_modules", "deploy": "npm run deploy-dev && npm run deploy-prod", "deploy-dev": "npm i && dotenv -e .env.dev sh build.sh && firebase use fintary-dev && firebase target:apply hosting dev fintary-dev && firebase deploy --only hosting:dev", "deploy-preview": "npm i && dotenv -e .env.preview sh build.sh && firebase use fintary-prod && firebase target:apply hosting preview fintary-preview && firebase deploy --only hosting:preview", "deploy-prod": "npm i && dotenv -e .env.prod sh build.sh && firebase use fintary-prod && firebase target:apply hosting prod fintary-prod && firebase deploy --only hosting:prod", "test": "react-app-rewired test --silent --watchAll=false --transformIgnorePatterns 'node_modules/(?!react-dnd)/'", "dev-test": "react-app-rewired test --transformIgnorePatterns 'node_modules/(?!react-dnd)/'", "lint": "eslint . --max-warnings=0", "lint-fix": "eslint . --fix", "eject": "react-app-rewired eject", "analyze": "source-map-explorer 'build/static/js/*.js'", "update-rules": "firebase deploy --only firestore:rules", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "postinstall": "cp ../node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/search": "^6.5.10", "@codemirror/view": "^6.36.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.10.0", "@lezer/highlight": "^1.2.1", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.17.1", "@mui/system": "^6.4.11", "@mui/x-date-pickers": "^7.28.3", "@sentry/react": "^8.55.0", "@sentry/types": "^8.55.0", "@tanstack/react-query": "^5.75.7", "@tawk.to/tawk-messenger-react": "^2.0.2", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.15.34", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-window": "^1.8.8", "@uiw/codemirror-themes": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "ag-grid-community": "^32.3.8", "ag-grid-react": "^32.3.8", "allotment": "^1.20.3", "axios": "^1.9.0", "babel-plugin-import": "^1.13.8", "bignumber.js": "^9.3.0", "caniuse-lite": "^1.0.30001712", "chrono-node": "2.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "common": "0.24.12", "copy-to-clipboard": "^3.3.3", "crypto-hash": "^3.1.0", "currency.js": "^2.0.4", "customize-cra": "^1.0.0", "dayjs": "^1.11.13", "didyoumean2": "^7.0.4", "dompurify": "^3.2.5", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "firebase": "^11.7.1", "hotkeys-js": "^3.13.10", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "loglevel": "^1.9.2", "material-react-table": "^2.13.3", "mathjs": "^13.2.3", "moment": "^2.30.1", "nanoid": "^5.1.5", "plaid": "^36.0.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-app-rewired": "^2.2.1", "react-compiler-runtime": "^19.0.0-beta-55955c9-20241229", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.5.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-hotjar": "^6.3.1", "react-idle-timer": "^5.7.2", "react-markdown": "^9.1.0", "react-pdf": "^9.2.1", "react-plaid-link": "^4.0.1", "react-router-dom": "^6.30.1", "react-scripts": "^5.0.1", "react-use": "^17.6.0", "react-window": "^1.8.11", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "source-map-explorer": "^2.5.3", "statsig-react": "^2.1.0", "text-encoding": "^0.7.0", "typeface-roboto": "^1.1.13", "use-debounce": "^10.0.4", "validator": "^13.15.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@chromatic-com/storybook": "^3.2.6", "@storybook/addon-essentials": "^8.4.4", "@storybook/addon-interactions": "^8.4.4", "@storybook/addon-links": "^8.4.4", "@storybook/addon-onboarding": "^8.4.4", "@storybook/blocks": "^8.4.4", "@storybook/preset-create-react-app": "^8.4.4", "@storybook/react": "^8.4.4", "@storybook/react-webpack5": "^8.4.4", "@storybook/test": "^8.4.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/react-helmet": "^6.1.11", "@types/storybook__react": "^5.2.1", "@vitest/ui": "^3.1.1", "babel-plugin-react-compiler": "^19.1.0-rc.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint-plugin-storybook": "^0.12.0", "prettier": "^3.5.3", "react-compiler-webpack": "^0.2.0", "react-refresh": "^0.17.0", "storybook": "^8.4.4", "tailwindcss": "^3.4.17", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "vitest": "^3.1.1", "webpack": "^5.99.8"}, "jest": {"moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "transformIgnorePatterns": ["node_modules/(?!(camelcase)/)"]}}