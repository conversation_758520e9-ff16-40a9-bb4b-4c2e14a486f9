import { Launch, VerticalSplit } from '@mui/icons-material';
import { Box, Chip, IconButton, Skeleton, Typography } from '@mui/material';
import { CustomerType } from 'common/customer/customer.constants';
import copy from 'copy-to-clipboard';
import { useContext } from 'react';
import { Link } from 'react-router-dom';
import { TransactionParty } from 'common/globalTypes';
import {
  getReportFieldConfig,
  mergeConfigsWithRenderInstance,
  ReportOptions,
} from 'common/field-config/report';

import AgentCommissionsEdit from '@/components/CommissionsDataView/AgentCommissionsEdit';
import DataPreviewTable from '@/components/molecules/DataPreviewTable';
import ExpandableData from '@/components/molecules/ExpandableData';
import AgentCommissionSplitConfig from '@/components/PolicyDataView/AgentCommissionSplitConfig';
import { ROUTES } from '@/constants/routes';
import { ComparisonContext } from '@/contexts/ReconciliationConfirmProvider';
import states from '@/data/states.json';
import DataTransformation from '@/services/DataTransformation';
import Formatter from '@/services/Formatter';
import UILabels from '@/services/UILabels';
import { CustomerField } from './Reports/CustomerField';
import CommissionCalcLog from '@/components/molecules/CommissionCalcLog';
import { CHAR_WIDTH } from '@/components/molecules/EnhancedTable/constants';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { useAccountStore } from '@/store';
import ReceivableApplicationRuleConfig from '@/components/PolicyDataView/ReceivableApplicationRuleConfig';
import { buildReceivableValueFilterRenderOnly } from './helpers/transactions.helpers';
import { Field } from '@/types';

const Normalizer = DataTransformation;

class Reports {
  #mode = 'default';

  #strings = new UILabels(this.#mode);

  outstandingFieldsInMobileView: string[] = [];

  label = 'Transactions';

  table = 'report_data';

  copyable = true;

  options?: ReportOptions = {};

  useNewTable = true;
  // Only work for new table
  dynamicSelectsConfig = [];

  filters: Record<string, Field> = {};

  deDupeFields?: unknown;

  labelSimple?: string;

  stickyColumns: string[] = [];

  fields: Record<string, any> = {};

  actions: any[] = [];

  fieldsCollapsed: string[] = [];

  constructor(mode, options?: ReportOptions) {
    this.options = options;
    this.#mode = mode;
    this.#strings = new UILabels(mode);
    this.deDupeFields = options?.deDupeFields ?? this.deDupeFields;
    this.label = this.#strings.getLabel('transactions', 'title');
    this.labelSimple = this.#strings.getLabel('transactions', 'titleSimple');
    this.copyable = true;
    this.filters = {
      transaction_type: {
        label: 'Transaction type',
      },
      group_name: {
        label: 'Group name',
      },
      agent_name: {
        label: 'Agent name',
      },
      contacts: {
        label: 'Agents',
      },
      document_id: {
        label: 'Document',
      },
      policy_status: {
        label: 'Status',
      },
      product_name: {
        label: 'Product name',
      },
      product_type: {
        label: 'Product type',
      },
      writing_carrier_name: {
        label: 'Master company',
      },
      account_type: {
        label: 'Account type',
      },
      tags: {
        label: 'Tags',
      },
    };
    this.stickyColumns = ['policy_id'];
    this.fields = {
      agent_name: {},
      customer_name: {
        getWidth: ({ estimatedWidth }) => {
          // TODO: find better solution
          const headerWidth = 'Customer name'.length * CHAR_WIDTH + 12 + 24 + 8;
          return Math.max(estimatedWidth, headerWidth);
        },
        formatter: (v, row) => {
          if (row?.customer) {
            const name =
              row?.customer?.type === CustomerType.individual
                ? `${row.customer.first_name} ${row.customer.last_name}`
                : row.customer.company_name;
            return (
              <Chip
                label={name}
                clickable
                component="a"
                href={`${ROUTES.customers.url}?q=${row.customer.first_name}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            );
          }
          return v;
        },
        render: (_, newData, setNewData) => {
          const onChange = (v) => {
            if (v.id === -1) {
              setNewData({
                ...newData,
                customer_name: v.first_name,
                customer_id: null,
                customer: null,
              });
            } else {
              setNewData({
                ...newData,
                customer_name: '',
                customer_id: v.id,
                customer: v,
              });
            }
          };
          return (
            <CustomerField
              value={newData.customer || newData.customer_name}
              onChange={onChange}
            />
          );
        },
      },
      dba: {},
      // CustomerFirstName: {
      //   label: 'Insured first name',
      //   matches: ['customer first name', 'first name'],
      //   required: 'name',
      //   enabled: true,
      // },
      // customerLastName: {
      //   label: 'Insured last name',
      //   matches: ['customer last name', 'last name'],
      //   required: 'name',
      //   enabled: true,
      // },
      policy_id: {
        formatter: (s) =>
          Formatter.policyNumber(s?.toString() ?? '', {
            account_id: this.options?.account_id,
          }),
        normalizer: (s) => s?.toString() ?? '',

        getWidth: ({ estimatedWidth }) => estimatedWidth + 30, // 40px for copy icon
      },
      internal_id: {},
      effective_date: {
        // Date
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      signed_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      policy_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      receivable_value_agency_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.AGENCY
      ),
      receivable_value_agent_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.AGENT
      ),
      receivable_value_override_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.POLICY
      ),
      writing_carrier_name: {
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          const enableSyncBtn = !!newData.sync_id;
          const isOverridden = newData?.config?.overrideFields?.includes(
            field.id
          );

          return (
            <EnhancedSelect
              disabled={enableSyncBtn && !isOverridden}
              label={field.label}
              listContainerSx={{
                minWidth: 500,
              }}
              enableSearch
              options={dynamicSelectData?.['companies'] || []}
              extractValue={(
                /** @type {Array<{ company_name: string }>} */ options
              ) => {
                return options.find(
                  (item) => item.company_name === newData.writing_carrier_name
                );
              }}
              url="/companies"
              labelKey="company_name"
              valueKey="company_name"
              onChange={(val: { company_name: string }) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      writing_carrier_name: val.company_name,
                    };
                  });
                }
              }}
              endAdornment={
                enableSyncBtn && (
                  <Box
                    sx={{ ml: 2, color: !isOverridden ? 'text.disabled' : '' }}
                  >
                    {field.endAdornment(newData, field, setNewData)}
                  </Box>
                )
              }
            />
          );
        },
        formatter: (
          val: string,
          collectionVals: { company_name: string }[] = []
        ) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter(
              (company) => company.company_name === val
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.company_name}`}
                clickable
                component="a"
                href={`/companies?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.company_name,
      },
      premium_amount: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      excess_amount: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      product_type: {},
      product_sub_type: {},
      product_name: {
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          const enableSyncBtn = !!newData.sync_id;
          const isOverridden = newData?.config?.overrideFields?.includes(
            field.id
          );
          return (
            <EnhancedSelect
              disabled={enableSyncBtn && !isOverridden}
              label={field.label}
              listContainerSx={{
                minWidth: 500,
              }}
              enableSearch
              options={dynamicSelectData?.['companies/products'] || []}
              extractValue={(
                /** @type {Array<{ product_name: string }>} */ options
              ) => {
                return options.find(
                  (item) => item.product_name === newData.product_name
                );
              }}
              url="/companies/products"
              labelKey="product_name"
              valueKey="product_name"
              onChange={(val: { product_name: string }) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      product_name: val.product_name,
                    };
                  });
                }
              }}
              endAdornment={
                enableSyncBtn && (
                  <Box
                    sx={{ ml: 2, color: !isOverridden ? 'text.disabled' : '' }}
                  >
                    {field.endAdornment(newData, field, setNewData)}
                  </Box>
                )
              }
            />
          );
        },
        formatter: (
          val: string,
          collectionVals: { product_name: string }[] = []
        ) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter(
              (company) => company.product_name === val
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.product_name}`}
                clickable
                component="a"
                href={`/companies/products?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.product_name,
        optionValuer: (option) =>
          option.product_name ? option.product_name : '',
      },
      product_option_name: {},
      cancellation_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      reinstatement_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      config: {
        label: 'Allied payment rule',
        enabled:
          typeof this.options?.isFeatureFlagEnabled === 'function'
            ? this.options?.isFeatureFlagEnabled('paymentAllocation')
            : false,
        type: 'custom',
        render: (field, row, setter) => (
          <ReceivableApplicationRuleConfig data={row} setter={setter} />
        ),
        tableFormatter: (val, row) => {
          const config = row.config?.allied_payment_rule;
          if (!config || !config.mode) {
            return 'None';
          }
          let output = '';
          if (config.mode === 'dental_only') {
            output = 'Dental only';
          } else if (config.mode === 'dental_vision') {
            output = `Dental + Vision`;
            if (config.priority && config.priority.length > 0) {
              output += ` (Priority: ${config.priority.join(', ')})`;
            }
          }
          return output;
        },
      },
      transaction_type: {
        // New, renewal, cancellation, rewrite, fees
      },
      commissions_expected: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      policy_status: {},
      account_type: {},
      geo_state: {
        formatter: (val) => {
          const state = states.find((s) => s.id === val);
          return state ? state.label : val;
        },
        render: (field, newData, setNewData) => {
          return (
            <EnhancedSelect
              label="State"
              enableSearch
              options={states}
              value={states.find((s) => s.id === newData.geo_state)}
              labelKey="label"
              valueKey="id"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      geo_state: val.id,
                    };
                  });
                }
              }}
            />
          );
        },
      },
      split_percentage: {
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
      },
      group_name: {},
      payment_mode: {},
      policy_term_months: {
        normalizer: Normalizer.normalizeInt,
      },
      commissionable_premium_amount: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      notes: {},
      group_id: {},
      contacts: {
        getWidth: ({ estimatedWidth, allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;
          allRows.forEach((row) => {
            row.contacts.forEach((id) => {
              const contact = mapData[id];
              const label = Formatter.contact(contact, {
                account_id: this.options?.account_id,
              });
              const width = label.length * CHAR_WIDTH;
              if (width > estimatedWidth) {
                estimatedWidth = width;
              }
            });
          });
          return estimatedWidth;
        },
        formatter: (val: string, collectionVals: { str_id: string }[] = []) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const datum = collectionVals?.find((datum) => datum.str_id === val);
            return datum ? (
              <Chip
                key={val}
                label={Formatter.contact(datum, {
                  account_id: this.options?.account_id,
                })}
                clickable
                component="a"
                href={`/agents/list?id=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val; // Not formatting when collectionVals is not available
        },
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          const enableSyncBtn = !!newData.sync_id;
          const isOverridden = newData?.config?.overrideFields?.includes(
            field.id
          );

          return (
            <EnhancedSelect
              disabled={enableSyncBtn && !isOverridden}
              label={field.label}
              enableSearch
              multiple
              disableAllOption
              enableSelectAllSearchResult={false}
              options={dynamicSelectData?.contacts || []}
              url="/contacts"
              valueKey="str_id"
              extractValue={(options) => {
                return options.filter((/** @type any */ item) =>
                  newData.contacts?.includes(item.str_id)
                );
              }}
              customLabel={(option) => {
                return Formatter.contact(option, {
                  account_id:
                    useAccountStore.getState().selectedAccount?.accountId,
                  incl_email: true,
                });
              }}
              onChange={(vals: { str_id: string }[]) => {
                if (vals) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      contacts: vals.map(
                        (/** @type any */ item) => item.str_id
                      ),
                    };
                  });
                }
              }}
              endAdornment={
                enableSyncBtn && (
                  <Box
                    sx={{ ml: 2, color: !isOverridden ? 'text.disabled' : '' }}
                  >
                    {field.endAdornment(newData, field, setNewData)}
                  </Box>
                )
              }
            />
          );
        },
        optionFormatter: (option) =>
          Formatter.contact(option, {
            account_id: this.options?.account_id,
            incl_email: true,
          }),
        optionValuer: (option) => option.str_id,
      },
      contacts_split: {
        tableFormatter: (val: Record<string, number>, row, dynamicSelects) =>
          val && (
            <Box>
              {Object.keys(val).length > 0 && dynamicSelects ? (
                <Box>
                  {Object.entries(val ?? {}).map(([k, v]) => (
                    <Box key={k}>
                      {Formatter.contact(
                        dynamicSelects?.find((e) => e.str_id === k) ?? {},
                        { account_id: this.options?.account_id }
                      )}
                      : {v}%
                    </Box>
                  ))}
                  {Object.values(val ?? {}).reduce((a, b) => +a + +b, 0) !==
                    100 && (
                    <Typography variant="caption" sx={{ color: 'red' }}>
                      Total should be 100
                    </Typography>
                  )}
                </Box>
              ) : Object.keys(val).length > 0 && !dynamicSelects ? (
                <Skeleton variant="rectangular" />
              ) : null}
            </Box>
          ),
        render: (field, row, setter, dynamicSelects) => (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ flex: 1 }}>
              <AgentCommissionSplitConfig
                title="Agent policy split"
                key={'agent-policy-split-config'}
                data={row}
                contacts={row.contacts}
                split={row.contacts_split}
                setter={setter}
                setterKey={'contacts_split'}
                dynamicSelects={dynamicSelects}
              />
            </Box>
            <Box sx={{ left: 16, top: 8, position: 'relative' }}>
              {field?.endAdornment && field.endAdornment(row, field, setter)}
            </Box>
          </Box>
        ),
      },
      contacts_commission_split: {
        tableFormatter: (val: Record<string, number>, row, dynamicSelects) =>
          val && (
            <Box>
              {Object.keys(val).length > 0 && dynamicSelects ? (
                <Box>
                  {Object.entries(val ?? {}).map(([k, v]) => (
                    <Box key={k}>
                      {Formatter.contact(
                        dynamicSelects?.find((e) => e.str_id === k) ?? {},
                        { account_id: this.options?.account_id }
                      )}
                      : {v}%
                    </Box>
                  ))}
                  {Object.values(val ?? {}).reduce((a, b) => +a + +b, 0) !==
                    100 && (
                    <Typography variant="caption" sx={{ color: 'red' }}>
                      Total should be 100
                    </Typography>
                  )}
                </Box>
              ) : Object.keys(val).length > 0 && !dynamicSelects ? (
                <Skeleton variant="rectangular" />
              ) : null}
            </Box>
          ),
        render: (field, row, setter, dynamicSelects) => (
          <AgentCommissionSplitConfig
            title="Agent commission split"
            key={'agent-commission-split-config'}
            data={row}
            contacts={row.contacts}
            split={row.contacts_commission_split}
            setter={setter}
            setterKey={'contacts_commission_split'}
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      issue_age: {
        normalizer: Normalizer.normalizeInt,
      },
      customer_paid_premium_amount: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      aggregation_id: {},
      aggregation_primary: {
        normalizer: Normalizer.normalizeBoolean,
        formatter: Formatter.boolean,
      },
      statement_data: {
        tableFormatter: (vals, row) => {
          const ctx = useContext(ComparisonContext);
          return (
            <ExpandableData
              key="statement_data"
              header={{ label: 'Commissions' }}
              formatter={(val) => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ whiteSpace: 'nowrap' }}>{val.str_id}</span>
                  <IconButton
                    onClick={() => {
                      ctx.setData({
                        statements: vals,
                        policy: row,
                        defaultOpenStatement: val.str_id,
                      });
                      ctx.setShow(true);
                    }}
                  >
                    <VerticalSplit />
                  </IconButton>
                  <IconButton
                    component={Link}
                    to={`/commissions?id=${val?.str_id}`}
                    target="_blank"
                  >
                    <Launch />
                  </IconButton>
                </Box>
              )}
              data={vals}
            />
          );
        },
        render: (field, row, setter, collectionVals) => {
          return row && Array.isArray(row.statement_data) ? (
            <DataPreviewTable
              label="Commissions"
              data={row.statement_data}
              fields={[
                {
                  label: 'Commission ID',
                  key: 'str_id',
                  formatter: (val) => (
                    <Box sx={{ whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" component="span">
                        {`${val.substring(0, 10)}...`}
                      </Typography>
                      <IconButton
                        component={Link}
                        to={`/commissions?id=${val}`}
                        target="_blank"
                      >
                        <Launch />
                      </IconButton>
                    </Box>
                  ),
                },
                {
                  label: 'Processing date',
                  key: 'processing_date',
                  formatter: Formatter.date,
                },
                {
                  label: 'Compensation type',
                  key: 'compensation_type',
                },
                {
                  label: 'Commission rate',
                  key: 'commission_rate',
                },
                {
                  label: 'Commission amount',
                  key: 'commission_amount',
                },
                {
                  label: 'Agent commissions',
                  key: 'agent_commissions',
                  formatter: (
                    val: Record<string, number>,
                    collectionVals: { str_id: string; id: number }[] = [],
                    options: { account_id?: string }
                  ) => {
                    if (!val || !Array.isArray(collectionVals)) return '';
                    return (
                      <Box>
                        {Object.entries(val)
                          .filter(([k, v]) => k !== 'total')
                          .map(([k, v]) => {
                            const contact = collectionVals?.find(
                              (e) => e?.str_id === k
                            ) ?? { id: undefined };
                            return contact.id ? (
                              <Chip
                                key={contact?.id}
                                label={`${Formatter.contact(contact, {
                                  account_id: options?.account_id,
                                })}: ${Formatter.currency(v)}`}
                                sx={{ m: 0.25 }}
                              />
                            ) : (
                              <Skeleton key={k} />
                            );
                          })}
                      </Box>
                    );
                  },
                },
                {
                  label: 'Agent commission log',
                  key: 'agent_commissions_log',
                  formatter: (
                    val: Record<string, unknown[]>,
                    collectionVals: { str_id: string; id: number }[] = [],
                    options: { account_id?: string }
                  ) => {
                    if (!val || !Array.isArray(collectionVals)) return '';
                    return (
                      <Box>
                        {Object.entries(val)
                          .filter(([k, v]) => k !== 'total')
                          .map(([k, v]) => {
                            const contact =
                              collectionVals?.find((e) => e.str_id === k) ?? {};
                            return (
                              <Box key={k}>
                                {Formatter.contact(contact, {
                                  account_id: options?.account_id,
                                })}
                                <br />
                                {v.map((e, i) => (
                                  <CommissionCalcLog
                                    commissionProfile={e}
                                    key={i}
                                  />
                                ))}
                              </Box>
                            );
                          })}
                      </Box>
                    );
                  },
                },
              ]}
              collectionVals={collectionVals}
            />
          ) : null;
        },
      },
      children_report_data: {
        tableFormatter: (vals) => (
          <Box key="children_report_data">
            {vals?.map((val) => (
              <Chip
                key={val.str_id}
                label={val.str_id}
                clickable
                component="a"
                href={`/policies?id=${val.str_id}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ))}
          </Box>
        ),
        render: (field, row) => {
          return row && Array.isArray(row.children_report_data) ? (
            <Box key="children_report_data">
              <Typography>Linked policies</Typography>
              {row.children_report_data.map((val) => (
                <Chip
                  key={val.str_id}
                  label={val.str_id}
                  clickable
                  component="a"
                  href={`/policies?id=${val.str_id}`}
                  target="_blank"
                  sx={{ m: 0.1 }}
                />
              ))}
            </Box>
          ) : null;
        },
      },
      type: {},
      tags: {
        normalizer: (val) => {
          if (Array.isArray(val) && val.length === 0) return [];
          if (typeof val === 'string')
            return val?.split(',').map((s) => s.trim()) ?? [];
          return val;
        },
        formatter: (val) => (Array.isArray(val) ? val.join(', ') : val),
      },
      document_id: {
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect
              label={field.label}
              listContainerSx={{
                minWidth: 500,
              }}
              enableSearch
              options={dynamicSelectData?.['documents']?.data || []}
              extractValue={(
                /** @type {Array<{ str_id: string }>} */ options
              ) => {
                return options.find(
                  (item) => item.str_id === newData.document_id
                );
              }}
              requestConfig={{
                params: {
                  type: 'report',
                },
              }}
              url="/documents"
              labelKey="filename"
              valueKey="str_id"
              onChange={(val: { str_id: string }) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      document_id: val.str_id,
                    };
                  });
                }
              }}
            />
          );
        },
        formatter: (val, collectionVals) => {
          const data = collectionVals?.data;
          if (data?.length > 0) {
            const datum = data.find((datum) => datum.str_id === val);
            return datum ? (
              <Chip
                key={val}
                label={`${datum.filename}`}
                clickable
                component="a"
                href={`/documents?id=${datum?.str_id}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => `${option.filename}`,
        optionValuer: (option) => option.str_id,
      },
      processing_status: {},
      first_payment_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      first_processed_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      commission_profile_id: {
        formatter: (
          val: string,
          collectionVals: { str_id: string; name: string }[] = []
        ) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter(
              (compProfile: { str_id: string }) => compProfile.str_id === val
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.name}`}
                clickable
                component="a"
                href={`/schedules/comp-profiles?id=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.name,
        optionValuer: (option) => option.str_id,
      },
      agent_payout_rate_override: {
        render: (
          field,
          row,
          setter,
          _collectionVals = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_payout_rate_override"
              disableConditions={false}
              percentageFormat="percentage"
            />
          );
        },
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;

          let maxWidth = 0;
          allRows.forEach((row) => {
            if (row.agent_payout_rate_override) {
              Object.entries(row.agent_payout_rate_override).forEach(
                ([id, v]) => {
                  const contact = mapData[id];
                  const label = `${Formatter.contact(contact, {
                    account_id: this.options?.account_id,
                  })}: ${Formatter.percentage(Number(v) / 100)}`;

                  const width = label.length * CHAR_WIDTH;
                  if (width > maxWidth) {
                    maxWidth = width;
                  }
                }
              );
            }
          });
          return maxWidth;
        },
        formatter: (
          val: Record<string, number>,
          collectionVals: { str_id: string; id: number }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find(
                    (e) => e.str_id === k
                  ) ?? { id: undefined };
                  return (
                    <Chip
                      key={contact.id}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.percentage(Number(v) / 100)}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );
        },
      },
      sync_id: {},
    };

    this.actions = [
      {
        id: 'copy_link',
        label: 'Copy link',
        onClick: (row) => {
          copy(`${window.location.origin}/policies?id=${row.str_id}`);
        },
      },
    ];

    const config = getReportFieldConfig({
      mode,
      options,
    });

    mergeConfigsWithRenderInstance(config, this);

    // Add field key as field property
    // Delete fields that are not enabled
    Object.entries(this.fields).forEach(([k, v]) => {
      this.fields[k].id = k;
      if (!v.enabled) {
        delete this.fields[k];
      }
    });

    this.fieldsCollapsed = Object.values(this.fields)
      .map((/** @type any */ field) => field.matches)
      .flat();

    this.outstandingFieldsInMobileView = [
      'policy_id',
      'customer_name',
      'effective_date',
      'carrier_name',
      'premium_amount',
    ];
  }

  getDataInfo = (data, topN = 5) => {
    const rowData: {
      index: number;
      matches: number;
      filled: number;
      length: number;
    }[] = [];
    const ranges: { start: number; end?: number }[] = [];
    let curRange: { start: number; end?: number } | null = { start: 0 };
    data.forEach((row, i) => {
      let matches = 0;
      let filled = 0;
      row.forEach((cell) => {
        if (cell || cell === 0) {
          filled += 1;
        }
        if (this.fieldsCollapsed.includes(cell.toString().toLowerCase())) {
          matches += 1;
        }
      });
      if (filled === 0 && curRange !== null) {
        curRange.end = i - 1;
        ranges.push(curRange);
        curRange = null;
      } else if (filled > 0 && curRange === null) {
        curRange = { start: i };
      }
      rowData.push({ index: i, matches, filled, length: row.length });
      // AllMatches[matches] = [...(allMatches?.[matches] || []), i];
    });
    if (curRange !== null) {
      ranges.push({ ...curRange, end: data.length - 1 });
    }

    const rangeData = rowData
      .sort((a, b) => b.matches - a.matches)
      .filter((row) => row.matches > 0)
      .splice(0, topN)
      .map((row) => ({
        index: row.index,
        count: row.matches,
        fields: data[row.index],
        data: data.filter(
          (dataRow, i) =>
            i > ranges.filter((a) => a.start === row.index)[0].start &&
            i <= (ranges.filter((a) => a.start === row.index)[0].end ?? 0)
        ),
      }));
    return { rowData, ranges, rangeData };
  };
}

export default Reports;
