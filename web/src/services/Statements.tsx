import { Launch, VerticalSplit } from '@mui/icons-material';
import {
  Box,
  Chip,
  IconButton,
  Skeleton,
  Tooltip,
  Typography,
} from '@mui/material';
import CommonFormatter from 'common/Formatter';
import {
  AgentCommissionsStatusesLabels,
  SystemRoles,
  TransactionParty,
} from 'common/globalTypes';
import { getFilenameFromPath } from 'common/helpers';
import { formatCurrency } from 'common/helpers/formatCurrency';
import copy from 'copy-to-clipboard';
import { useContext } from 'react';
import { Link } from 'react-router-dom';

import AgentCommissionsEdit from '@/components/CommissionsDataView/AgentCommissionsEdit';
import PayoutStatusEdit from '@/components/CommissionsDataView/PayoutStatusEdit';
import CommissionCalcLog from '@/components/molecules/CommissionCalcLog';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { CHAR_WIDTH } from '@/components/molecules/EnhancedTable/constants';
import type { NewTableProps } from '@/components/molecules/EnhancedTable/NewTable';
import { ComparisonContext } from '@/contexts/ReconciliationConfirmProvider';
import states from '@/data/states.json';
import API from '@/services/API';
import DataTransformation from '@/services/DataTransformation';
import Formatter from '@/services/Formatter';
import UILabels from '@/services/UILabels';
import { useAccountStore } from '@/store';
import { Field, FieldTypes, Roles } from '@/types';
import { buildReceivableValueFilter } from './helpers/transactions.helpers';
import { ROUTES } from '@/constants/routes';
import {
  ChildrenDataRenderer,
  ChildrenDataTableFormatter,
} from '@/components/Statements/ChildrenData';
import {
  policyDataIfEmpty,
  PolicyIdColumn,
} from '@/components/Statements/PolicyIdColumn';

const Normalizer = DataTransformation;

/**
 * Statements class represents the structure and behavior of statements data for data view.
 * How to create a new column at: https://github.com/Fintary/fintary/blob/main/docs/data-view/README.md
 */
class Statements {
  #mode = 'default';

  #strings = new UILabels(this.#mode);

  label = 'Statements data';
  labelSimple = 'Statements data';

  table = 'statement_data';

  copyable = true;

  filters: { [key: string]: any } = {};

  fields: { [key: string]: Field } = {};

  actions: Record<string, any>[] = [];

  outstandingFieldsInMobileView: any[] = [];

  dateFilters: any[] = [];

  fieldsCollapsed: any[] = [];

  options: Record<string, any> = {};

  stickyColumns: string[] = [];
  useNewTable = true;
  sortFilterByPosition = true;

  extraBulkEditCsvFields = [{ id: 'id', label: 'id', required: true }];

  // Only work for new table
  dynamicSelectsConfig: NewTableProps['dynamicSelectsConfig'] = [
    {
      table: 'companies',
      queryParamName: 'companyNames',
      queryParamValue: [], // Will be collected from data
      collectDataFields: ['writing_carrier_name', 'carrier_name'],
    },
    {
      table: 'contacts',
      queryParamName: 'str_id',
      queryParamValue: [], // Will be collected from data
      collectDataFields: [
        'contacts',
        'agent_commissions',
        'agent_commissions_v2',
        'comp_calc',
        'comp_calc_log',
        'comp_calc_status',
        'agent_commissions_log',
        'agent_commissions_status2',
        'agent_payout_rate',
        'agent_commission_payout_rate',
        'agent_payout_rate_override',
      ],
      // Will be used to collect data for queryParamValue
      dataExtractors: {
        agent_commissions: (row, totals) => {
          return [
            ...new Set(
              Object.keys(row?.agent_commissions || {})
                .filter((key) => key !== 'total')
                .concat(Object.keys(totals?.agent_commissions || {}))
            ),
          ];
        },
        agent_payout_rate: (row) => {
          return Object.keys(row?.agent_payout_rate || {}).filter(
            (key) => key !== 'total'
          );
        },
      },
    },
    {
      table: 'documents',
      queryParamName: 'str_id',
      queryParamValue: [], // Will be collected from data
      collectDataFields: ['document_id'],
    },
  ];

  constructor(
    mode?: string | null,
    role?: any,
    userRole?: Roles | null,
    options?: any
  ) {
    this.#mode = mode ?? this.#mode;
    this.#strings = new UILabels(mode);
    this.options = options;
    this.label = this.#strings.getLabel('cash', 'title');
    this.labelSimple = this.#strings.getLabel('cash', 'titleSimple');
    this.copyable = true;
    this.filters = {
      transaction_type: {
        label: 'Transaction type',
      },
      group_name: {
        label: 'Group name',
      },
      reconciliation_status: {
        label: 'Reconciliation status',
      },
      agent_name: {
        label: 'Agent name',
        sortPosition: 10,
      },
      carrier_name: {
        label: 'Paying entity',
        sortPosition: 8,
      },
      compensation_type: {
        label: 'Compensation type',
        sortPosition: 4,
      },
      contacts: {
        label: 'Agents',
        sortPosition: 5,
      },
      flags: {
        label: 'Flags',
        sortPosition: 11,
      },
      tags: {
        label: 'Tags',
      },
      document_id: {
        label: 'Document',
        sortPosition: 9,
        listContainerSx: {
          width: 400,
        },
      },
      payment_status: {
        label: 'Payment status',
      },
      product_name: {
        label: 'Product name',
        sortPosition: 7,
      },
      product_type: {
        label: 'Product type',
        sortPosition: 6,
      },
      status: {
        label: 'Status',
      },
      writing_carrier_name: {
        label: 'Carrier/MGA',
        sortPosition: 3,
      },
      premium_type: {
        label: 'Premium type',
      },
      account_type: {
        label: 'Account type',
      },
      agent_commissions_status: {
        label: 'Payout status',
        sortPosition: 1,
      },
      agent_commissions_status2: {
        label: 'Payout status*',
      },
      comp_calc_status: {
        label: 'Agent payout status',
      },
      report_data_id: {
        label: 'Reconciliation',
        sortPosition: 2,
      },
    };
    this.fields = {
      policy_id: {
        // String
        sticky: 'left',
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'transactionId'),
        matches: [
          'policy id',
          'policy no',
          'policy no.',
          'policy number',
          'policy #',
          'policy',
          'contract #',
          'policy_number',
          'policyid',
        ],
        reconciler: true,
        enabled: true,
        normalizer: (s) => s?.toString(),
        subPolicyDataIfEmpty: true,
        tableFormatter: (val, row) => (
          <PolicyIdColumn
            val={val}
            row={row}
            accountId={this.options?.account_id}
          />
        ),
        copyable: true,
        getWidth: ({ estimatedWidth }) => {
          return estimatedWidth + 80; // 40 for copy button
        },
      },
      // TotalAmount: {
      //   label: 'Total amount',
      //   matches: ['total', 'total amount', 'total amt', 'amount'],
      //   enabled: true,
      //   global: true,
      //   formatter: (s) => (s?.toString()?.startsWith('$') ? s : `$${s}`),
      // },
      commission_amount: {
        label: this.#strings.getLabel('cash', 'amount'),
        matches: [
          'commission',
          'commission amount',
          'gross commission',
          'gross comm earned',
          'commission earned',
          'commission due',
          'amount',
          'comm amount',
          'comm amt',
          'commissionamount',
        ],
        enabled: true,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
      },
      commission_paid_amount: {
        label: 'Commissions paid',
        matches: ['commissions paid out', 'agent commission'],
        enabled: true,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      advanced_commission_amount: {
        label: 'Advanced commissions paid',
        enabled: true,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      customer_name: {
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'customerName'),
        matches: [
          'customer name',
          'customer',
          'insuree',
          'insured',
          'client',
          'client name',
          'insured name',
          'policy holder',
          'policy holder name',
          'subscriber',
          'name of insured',
          'consumer name contract #',
          'named insured',
          'customername',
          'policyholder name',
          'individual name',
        ],
        reconciler: true,
        enabled: true,
        style: {
          pl: 2,
        },
        getWidth: ({ estimatedWidth }) => {
          // TODO: find better solution
          const headerWidth = 'Customer name'.length * CHAR_WIDTH + 12 + 24 + 8;
          return Math.max(estimatedWidth, headerWidth);
        },
        tableFormatter: policyDataIfEmpty('customer_name'),
      },
      invoice_date: {
        label: this.#strings.getLabel('cash', 'invoiceDate'),
        matches: ['invoice date', 'invoice received', 'invoiceDate'],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
        bulkEdit: true,
      },
      payment_date: {
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'paymentDate'),
        matches: [
          'payment date',
          'payment received',
          'statement date',
          'month billed',
          'paymentdate',
        ],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
        width: 216,
      },
      processing_date: {
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'processingDate'),
        matches: ['processing date', 'processingDate'],
        enabled: true,
        global: true,
        type: FieldTypes.DATE,
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      agent_name: {
        label: this.#strings.getLabel('cash', 'repName'),
        matches: [
          'assigned agent',
          'agt',
          'writing agent',
          'writing agt',
          'agent name',
          'agt name',
          'assigned_agent',
          'agent',
          'producer name',
        ],
        enabled: this.#mode === 'insurance',
        global: true,
        bulkEdit: true,
      },
      statement_number: {
        // New, renewal, cancellation, rewrite, fees
        label: this.#strings.getLabel('cash', 'invoiceNumber'),
        matches: [
          'statement number',
          'commission statement number',
          'statementnumber',
        ],
        enabled: true,
        bulkEdit: true,
      },
      transaction_type: {
        // New, renewal, cancellation, rewrite, fees
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'transactionType'),
        matches: [
          'transaction type',
          'commission type',
          'policy type',
          'transactiontype',
        ],
        subPolicyDataIfEmpty: true,
        enabled: true,
        tableFormatter: policyDataIfEmpty('transaction_type'),
      },
      writing_carrier_name: {
        // String
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'entity'),
        matches: ['carrier name', 'carrier', 'writingcarriername'],
        enabled: this.#mode === 'insurance',
        reconciler: true,
        global: true,
        width: 243,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect<{ company_name: string }>
              label={field.label}
              listContainerSx={{
                minWidth: 500,
              }}
              enableSearch
              options={dynamicSelectData?.['companies'] || []}
              extractValue={(options) => {
                return options.find(
                  (item) =>
                    item.company_name === newData.writing_carrier_name?.trim()
                );
              }}
              url="/companies"
              labelKey="company_name"
              valueKey="company_name"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      writing_carrier_name: val.company_name,
                    };
                  });
                }
              }}
            />
          );
        },
        queryParamName: 'company_name',
        formatter: (val, collectionVals = []) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter((company) => {
              return company.company_name === val?.trim();
            })?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.company_name}`}
                clickable
                component="a"
                href={`/companies?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.company_name,
      },
      premium_type: {
        label: this.#strings.getLabel('cash', 'premiumType'),
        type: FieldTypes.SELECT,
        matches: [],
        options: ['policy', 'split'],
        reconciler: true,
        enabled: this.#mode === 'insurance',
        global: true,
        bulkEdit: true,
      },
      carrier_name: {
        // String
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'payingEntity'),
        matches: ['carrier name', 'carrier', 'carriername'],
        enabled: true,
        global: true,
        width: 350,

        type: FieldTypes.CUSTOM,
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect<{ company_name: string }>
              label={field.label}
              enableSearch
              listContainerSx={{
                minWidth: 500,
              }}
              options={dynamicSelectData?.['companies'] || []}
              extractValue={(options) => {
                return options.find(
                  (item) => item.company_name === newData.carrier_name?.trim()
                );
              }}
              url="/companies"
              labelKey="company_name"
              valueKey="company_name"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      carrier_name: val.company_name,
                    };
                  });
                }
              }}
            />
          );
        },
        formatter: (val, collectionVals = []) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter(
              (company) => company.company_name === val?.trim()
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.company_name}`}
                clickable
                component="a"
                href={`/companies?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.company_name,
      },
      agent_id: {
        label: this.#strings.getLabel('cash', 'repId'),
        matches: [
          'agent id',
          'agt id',
          'agent no',
          'agt no',
          'writing agent #',
          'writing agent no',
          'writing agent id',
          'writing agt #',
          'writing agt no',
          'writing agt id',
          'agentid',
        ],
        enabled: this.#mode === 'insurance',
        bulkEdit: true,
      },
      premium_amount: {
        label: this.#strings.getLabel('cash', 'annualizedPremium'),
        description:
          'Annualized premium amount as recorded in commission statements',
        matches: [
          'premium amount',
          'premium amt',
          'premium paid',
          'premium',
          'premium - annualized',
          'annualized_premium',
          'annualized_premium2',
          'premiumamount',
          'policy premium',
          'Paid Prem',
        ],
        enabled: this.#mode === 'insurance',
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,

        type: FieldTypes.CURRENCY,
      },
      expected_result: {
        label: 'Expected result',
        enabled: true,
        readOnly: true,
      },
      commission_rate: {
        // Number
        label: this.#strings.getLabel('cash', 'rate'),
        matches: [
          'commission rate',
          'commission pct',
          'commission %',
          'commission perentage',
          'comm %',
          'commissionrate',
        ],
        enabled: this.#mode === 'insurance',
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
        type: FieldTypes.PERCENTAGE,
      },
      carrier_rate: {
        label: 'Carrier rate',
        matches: [],
        enabled: this.#mode === 'insurance',
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
        type: FieldTypes.PERCENTAGE,
        bulkEdit: true,
      },
      receivable_value_agency_rate: buildReceivableValueFilter(
        TransactionParty.AGENCY
      ),
      receivable_value_agent_rate: buildReceivableValueFilter(
        TransactionParty.AGENT
      ),
      receivable_value_override_rate: buildReceivableValueFilter(
        TransactionParty.POLICY
      ),
      // Rows - Optional
      effective_date: {
        // Date
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'startDate'),
        matches: [
          'policy effective date',
          'effective date',
          'effective',
          'policy effective',
          'in force date',
          'eff date',
          'effective_date',
          'effective_date2',
          'effectivedate',
        ],
        enabled: true,
        type: FieldTypes.DATE,
        tableFormatter: policyDataIfEmpty(
          'effective_date',
          Normalizer.formatDate
        ),
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      product_type: {
        label: this.#strings.getLabel('cash', 'productType'),
        matches: ['product type', 'product line', 'producttype'],
        reconciler: true,
        enabled: this.#mode === 'insurance',
        global: true,
        tableFormatter: policyDataIfEmpty('product_type'),
        bulkEdit: true,
      },
      product_sub_type: {
        label: 'Product sub type',
        matches: [],
        reconciler: true,
        enabled: this.#mode === 'insurance',
        tableFormatter: policyDataIfEmpty('product_sub_type'),
        global: true,
        bulkEdit: true,
      },
      product_name: {
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'productName'),
        matches: ['product name', 'product', 'policy name'],
        enabled: this.#mode === 'insurance',
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('product_name'),
      },
      product_option_name: {
        label: 'Product option name',
        matches: ['option', 'product option'],
        enabled: this.#mode === 'insurance',
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('product_option_name'),
        bulkEdit: true,
      },
      fees: {
        label: this.#strings.getLabel('cash', 'fees'),
        matches: ['fees'],
        enabled: this.#mode === 'insurance',
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      group_id: {
        label: this.#strings.getLabel('cash', 'groupId'),
        matches: [
          'group id',
          'group number',
          'group no',
          'grp #',
          'grp number',
          'groupid',
        ],
        enabled: this.#mode === 'insurance',
        bulkEdit: true,
      },
      internal_id: {
        label: 'Internal ID',
        enabled: true,
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('internal_id'),
        bulkEdit: true,
      },
      period_date: {
        bulkEdit: true,
        label: this.#strings.getLabel('cash', 'periodDate'),
        matches: ['period', 'coverage period', 'perioddate', 'due date'],
        enabled: this.#mode === 'insurance',
        type: FieldTypes.DATE,
        global: true,
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      status: {
        bulkEdit: true,
        label: 'Status',
        enabled: true,
      },
      performance_bonus: {
        label: 'Performance bonus',
        matches: ['performance bonus', 'performancebonus'],
        enabled: false,
        bulkEdit: true,
      },
      override: {
        label: 'Override',
        matches: ['override'],
        enabled: false,
        bulkEdit: true,
      },
      payee: {
        label: 'Payee',
        matches: ['payee', 'payee name'],
        enabled: false,
        bulkEdit: true,
      },
      payee_id: {
        label: 'Payee ID',
        matches: ['payee id', 'payee no', 'payeeid'],
        enabled: false,
        bulkEdit: true,
      },
      agent_level: {
        label: 'Agent level',
        matches: ['agent level', 'writing agent level', 'agentlevel'],
        enabled: false,
        bulkEdit: true,
      },
      agency: {
        label: 'Agency',
        matches: ['agency'],
        enabled: false,
        bulkEdit: true,
      }, // String
      policy_issue_date: {
        label: 'Policy issue date',
        matches: ['policy issue date', 'issue date', 'policyissuedate'],
        type: FieldTypes.DATE,
        enabled: false,
        bulkEdit: true,
      },
      policy_amount: {
        label: 'Policy amount',
        matches: [
          'policy amount',
          'original face amount',
          'face amount',
          'policyamount',
        ],
        enabled: false,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      policy_term: {
        label: 'Policy term',
        matches: ['policy term', 'policyterm'],
        enabled: false,
        bulkEdit: true,
      },
      premium_received: {
        // Number
        label: 'Premium received',
        matches: ['premium received', 'premiumreceived'],
        enabled: false,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      compensation_type: {
        label: 'Compensation type',
        matches: ['compensation type', 'compensationtype'],
        enabled: this.#mode === 'insurance',
        bulkEdit: true,
      },
      income_class: {
        label: 'Income class',
        matches: ['income class', 'tax form', 'incomeclass'],
        enabled: false,
        bulkEdit: true,
      },
      billing_frequency: {
        label: 'Billing frequency',
        matches: ['billing frequency', 'billingfrequency'],
        enabled: false,
        bulkEdit: true,
      },
      premium_transaction: {
        label: 'Premium transaction',
        matches: ['premium transaction', 'premiumtransaction'],
        enabled: false,
        bulkEdit: true,
      },
      commissionable_premium_amount: {
        label: 'Target premium',
        matches: ['target premium', 'commissionable premium'],
        enabled: true,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        type: FieldTypes.CURRENCY,
        bulkEdit: true,
      },
      account_type: {
        label: 'Account type',
        enabled: true,
        matches: ['account type', 'accounttype'],
        bulkEdit: true,
      },
      issue_age: {
        label: 'Issue age',
        enabled: true,
        normalizer: Normalizer.normalizeInt,
        matches: ['issue age'],
        type: FieldTypes.INTEGER,
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('issue_age'),
        bulkEdit: true,
      },
      customer_paid_premium_amount: {
        label: 'Customer Paid Premium Amount',
        enabled: true,
        matches: ['Basis', 'customer paid premium amount'],
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
        bulkEdit: true,
      },
      bill_mode: {
        label: 'Bill mode',
        enabled: true,
        matches: ['bill mode'],
        bulkEdit: true,
      },
      geo_state: {
        label: 'State',
        type: FieldTypes.SELECT,
        options: states,
        matches: [],
        enabled: true,
        tableFormatter: policyDataIfEmpty(
          'geo_state',
          (val: string): string => {
            const state = states.find((state) => state.id === val);
            return state ? state.label : val;
          }
        ),
        bulkEdit: true,
      },
      split_percentage: {
        label: 'Split percentage',
        enabled: true,
        matches: ['split percentage'],
        tableFormatter: (val, row) => {
          const isDiff = val !== row.report?.split_percentage;
          const policyVal =
            row.contacts?.length === 1
              ? row.report?.contacts_split?.[row.contacts[0]]
              : undefined;
          return val ? (
            <Tooltip
              title={
                isDiff && policyVal
                  ? `Policy data differs(${Normalizer.formatPercentage(
                      policyVal
                    )})`
                  : ''
              }
            >
              <span>{Normalizer.formatPercentage(val)}</span>
            </Tooltip>
          ) : row.contacts?.length === 1 &&
            row.report?.contacts_split?.[row.contacts[0]] ? (
            <Tooltip title="Sourced from policy data">
              <span>{Normalizer.formatPercentage(policyVal)}*</span>
            </Tooltip>
          ) : (
            ''
          );
        },
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
        type: FieldTypes.PERCENTAGE,
        bulkEdit: true,
      },
      group_name: {
        label: 'Group name',
        matches: ['group name'],
        required: false,
        enabled: true,
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('group_name'),
        bulkEdit: true,
      },
      payment_mode: {
        label: 'Payment mode',
        matches: ['payment mode'],
        required: false,
        enabled: true,
        tableFormatter: policyDataIfEmpty('payment_mode'),
        bulkEdit: true,
      },
      aggregation_id: {
        label: 'Aggregation ID',
        matches: [],
        enabled: true,
        subPolicyDataIfEmpty: true,
        tableFormatter: (val, row) => {
          const policyVal = row.report?.aggregation_id;
          const isDiff = val !== policyVal;
          return val ? (
            <Tooltip
              title={
                isDiff && policyVal ? `Policy data differs(${policyVal})` : ''
              }
            >
              {val}
            </Tooltip>
          ) : policyVal && row.report?.policy_id ? (
            <Tooltip title="Sourced from policy data">
              <span>{policyVal}*</span>
            </Tooltip>
          ) : (
            ''
          );
        },
        copyable: true,
        bulkEdit: true,
      },
      member_count: {
        label: 'Member count',
        matches: ['member count'],
        enabled: true,
        type: FieldTypes.INTEGER,
        normalizer: Normalizer.normalizeInt,
        bulkEdit: true,
      },
      commission_basis: {
        label: 'Commission basis',
        matches: ['commission basis'],
        enabled: true,
        bulkEdit: true,
      },
      standardized_customer_name: {
        label: 'Standardized customer name',
        matches: ['standardized customer name'],
        enabled: true,
        bulkEdit: true,
      },
      contacts: {
        bulkEdit: true,
        label: 'Agents',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect
              label={field.label}
              enableSearch
              multiple
              disableAllOption
              enableSelectAllSearchResult={false}
              options={dynamicSelectData?.contacts || []}
              url="/contacts"
              valueKey="str_id"
              extractValue={(options) => {
                return options.filter((item: any) =>
                  newData.contacts?.includes(item.str_id)
                );
              }}
              customLabel={(option) => {
                return Formatter.contact(option, {
                  account_id:
                    useAccountStore.getState().selectedAccount?.accountId,
                });
              }}
              onChange={(vals) => {
                if (vals) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      contacts: (vals as any[]).map((item) => item.str_id),
                    };
                  });
                }
              }}
            />
          );
        },
        multiple: true,
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;
          let maxWidth = 0;
          allRows.forEach((row) => {
            if (row.contacts?.length > 0) {
              row.contacts.forEach((id) => {
                const contact = mapData[id];
                const label = Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                });
                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        formatter: (val, collectionVals = []) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const datum = collectionVals?.filter(
              (datum) => datum.str_id === val
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={Formatter.contact(datum, {
                  account_id: this.options?.account_id,
                })}
                clickable
                component="a"
                href={`${ROUTES.agents.url}?id=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val; // Not formatting when collectionVals is not available
        },
        optionFormatter: (o) =>
          Formatter.contact(o, { account_id: this.options?.account_id }),
        optionValuer: (option) => option.str_id,
      },
      agent_commissions: {
        label: 'Agent commissions',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_commissions"
            />
          );
        },
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;

          let maxWidth = 0;
          allRows.forEach((row) => {
            if (row.agent_commissions) {
              Object.entries(row.agent_commissions).forEach(([id, v]) => {
                const contact = mapData[id];
                const label = `${Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                })}: ${formatCurrency(v as number)}`;

                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        tableFormatter: (
          val,
          row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find(
                    (e) => e?.str_id === k
                  ) ?? { id: undefined };
                  return contact.id ? (
                    <Chip
                      clickable
                      component="a"
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      target="_blank"
                      key={contact?.id}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
        bulkEdit: true,
      },
      agent_commissions_v2: {
        label: 'Agent commissions V2',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_commissions_v2"
            />
          );
        },
        tableFormatter: (
          val,
          row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find(
                    (e) => e?.str_id === k
                  ) ?? { id: undefined };
                  return contact.id ? (
                    <Chip
                      key={contact?.id}
                      clickable
                      component="a"
                      target="_blank"
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
        readOnly: true,
      },
      comp_calc: {
        label: 'Agent compensation',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (field, row, setter, collectionVals, dynamicSelectsData) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="comp_calc"
            />
          );
        },
        tableFormatter: (
          val,
          row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find((e) => e?.id === +k) ?? {
                    id: undefined,
                  };
                  return contact.id ? (
                    <Chip
                      key={contact?.id}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
        bulkEdit: true,
      },
      comp_calc_log: {
        label: 'Agent compensation log',
        enabled: true,
        formatter: (val, collectionVals = []) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]: [string, any]) => {
                  const contact =
                    collectionVals?.find((e) => e?.id === +k) ?? {};
                  // After grouping if is not an array means that grouped data is not the same so we render ***
                  if (!Array.isArray(v)) {
                    return <Box key={k}>***</Box>;
                  }
                  return (
                    <Box key={k}>
                      {Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}
                      <br />
                      {v.map((e, i) => (
                        <CommissionCalcLog commissionProfile={e} key={i} />
                      ))}
                    </Box>
                  );
                })}
            </Box>
          );
        },
        readOnly: true,
      },
      comp_calc_status: {
        label: 'Agent payout status',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          row,
          setter,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return (
            <PayoutStatusEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelects={collectionVals}
              key="comp_calc_status"
            />
          );
        },
        tableFormatter: (
          val,
          row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val).map(([k, v]) => {
                const contact = collectionVals?.find((e) => e?.id === +k) ?? {
                  id: undefined,
                };
                return contact.id ? (
                  <Chip
                    key={contact?.id}
                    label={`${Formatter.contact(contact, {
                      account_id: this.options?.account_id,
                    })}: ${v}`}
                    sx={{ m: 0.1 }}
                  />
                ) : (
                  <Skeleton key={k} />
                );
              })}
            </Box>
          );
        },
      },
      agent_commissions_log: {
        label: 'Agent commissions log',
        enabled: true,
        getWidth: ({}) => {
          // The formatter renders complex component, can't calculate width for now
          // TODO: find a better solution
          return 650;
        },
        formatter: (val, collectionVals = []) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]: [string, any]) => {
                  const contact =
                    collectionVals?.find((e) => e.str_id === k) ?? {};
                  // After grouping if is not an array means that grouped data is not the same so we render ***
                  if (!Array.isArray(v)) {
                    return <Box key={k}>***</Box>;
                  }

                  return (
                    <Box key={k}>
                      {Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}
                      <br />
                      {v.map((e, i) => (
                        <CommissionCalcLog
                          statement_id={this.options.statement_id}
                          commissionProfile={e}
                          key={i}
                        />
                      ))}
                    </Box>
                  );
                })}
            </Box>
          );
        },
        readOnly: true,
        // Formatter: (val) => (val ? JSON.stringify(val) : ''),
      },
      flags: {
        label: 'Flags',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          allRows.forEach((row) => {
            const flags = row?.flags;
            if (!flags) return 2;
            const keyWithMaxLengthValue = Object.keys(flags).reduce(
              (maxKey, currentKey) => {
                return flags[currentKey].length > flags[maxKey].length
                  ? currentKey
                  : maxKey;
              },
              Object.keys(flags)[0]
            );
            const width =
              ((keyWithMaxLengthValue?.length || 0) as number) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 50 * 2;
        },
        render: (
          _field,
          row,
          _setter,
          _collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return row && row.flags ? (
            <Box key="flags">
              <Typography>Flags</Typography>
              {Object.entries(row.flags).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={String(`${key}: ${value}`)}
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          ) : null;
        },
        formatter: (val) => {
          if (!val) return '';
          return (
            <Box>
              {Object.entries(val).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={String(`${key}: ${value}`)}
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          );
        },
      },
      flags_log: {
        label: 'Flags log',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          allRows.forEach((row) => {
            const flags = row?.flags_log;
            if (!flags) return 2;
            const keyWithMaxLengthValue = Object.keys(flags).reduce(
              (maxKey, currentKey) => {
                return flags[currentKey].length > flags[maxKey].length
                  ? currentKey
                  : maxKey;
              },
              Object.keys(flags)[0]
            );
            const width =
              ((keyWithMaxLengthValue?.length || 0) as number) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 50 * 2;
        },
        render: (_field, row, _setter) => {
          return row && row.flags_log ? (
            <Box key="flags_log">
              <Typography>Flags log</Typography>
              {Object.entries(row.flags_log).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={
                      value && typeof value === 'object' && 'message' in value
                        ? String((value as { message: any }).message)
                        : String(value)
                    }
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          ) : null;
        },
        formatter: (val) => {
          if (!val) return '';
          return (
            <Box>
              {Object.entries(val).map(([key, keyValue]) => {
                return (
                  <Chip
                    key={key}
                    label={
                      keyValue &&
                      typeof keyValue === 'object' &&
                      'message' in keyValue
                        ? String((keyValue as { message: any }).message)
                        : String(keyValue)
                    }
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          );
        },
      },
      agent_commissions_status: {
        bulkEdit: true,
        label: 'Payout status',
        enabled: true,
        type: FieldTypes.SELECT,
        options: Object.values(AgentCommissionsStatusesLabels),
      },
      agent_commissions_status2: {
        label: 'Payout status*',
        enabled: true,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          row,
          setter,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return (
            <PayoutStatusEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelects={collectionVals}
              key="agent_commissions_status2"
            />
          );
        },
        tableFormatter: (
          val,
          row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val).map(([k, v]) => {
                const contact = collectionVals?.find(
                  (e) => e?.str_id === k
                ) ?? { id: undefined };
                return contact.id ? (
                  <Chip
                    key={contact?.id}
                    label={`${Formatter.contact(contact, {
                      account_id: this.options?.account_id,
                    })}: ${v}`}
                    sx={{ m: 0.1 }}
                  />
                ) : (
                  <Skeleton key={k} />
                );
              })}
            </Box>
          );
        },
      },
      notes: {
        bulkEdit: true,
        label: 'Notes',
        enabled: true,
        matches: ['RevCd', 'notes'],
        subPolicyDataIfEmpty: true,
        tableFormatter: policyDataIfEmpty('notes'),
      },
      tags: {
        label: 'Tags',
        enabled: true,
        normalizer: (val) => {
          if (Array.isArray(val) && val.length === 0) return [];
          if (typeof val === 'string')
            return val?.split(',').map((s) => s.trim()) ?? [];
          return val;
        },
        formatter: (val) => (Array.isArray(val) ? val.join(', ') : val),
        bulkEdit: true,
      },
      document_id: {
        bulkEdit: true,
        width: 300,
        label: 'Document',
        matches: ['document id', 'document'],
        enabled: true,
        required: false,
        type: FieldTypes.CUSTOM,
        render: (
          field,
          newData,
          setNewData,
          dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect<any>
              label={field.label}
              enableSearch
              listContainerSx={{
                minWidth: 500,
              }}
              options={dynamicSelectData?.['documents']?.data || []}
              extractValue={(options) => {
                return options.find((item) => {
                  return item.str_id === newData.document_id;
                });
              }}
              customLabel={(item) => {
                return `${item?.file_path?.endsWith(item.filename) ? item.filename : `${getFilenameFromPath(item.file_path)}`}`;
              }}
              url="/documents"
              labelKey="label"
              valueKey="str_id"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      document_id: val.str_id,
                    };
                  });
                }
              }}
            />
          );
        },
        queryParamValue: 'statement',
        queryParamName: 'type',
        getWidth: ({ dynamicSelectData, allRows }) => {
          const map =
            dynamicSelectData?.reduce((acc, item) => {
              acc[item.str_id] = item;
              return acc;
            }, {}) || {};
          let estimatedWidth = 0;
          allRows?.forEach((row) => {
            const doc = map[row.document_id];
            if (doc) {
              const filename = getFilenameFromPath(doc.file_path);
              const width = (filename?.length || 0) * CHAR_WIDTH;
              if (width > estimatedWidth) {
                estimatedWidth = width;
              }
            }
          });

          return estimatedWidth;
        },
        formatter: (val, collectionVals = []) => {
          const dynamicSelectsData = collectionVals?.data || collectionVals;
          if (dynamicSelectsData?.length) {
            const item = dynamicSelectsData?.find(
              (item) => item.str_id === val
            );
            return item ? (
              <Chip
                clickable
                component="a"
                href={`/documents?id=${item?.str_id}`}
                target="_blank"
                label={`${item?.file_path?.endsWith(item.filename) ? item.filename : `${getFilenameFromPath(item.file_path)}`}`}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) =>
          `${option.file_path?.endsWith(option.filename) ? option.filename : `${getFilenameFromPath(option.file_path)}`}`,
        optionValuer: (option) => option.str_id,
      },
      processing_status: {
        label: 'Processing status',
        options: ['new', 'processed', 'frozen'],
        matches: [],
        type: FieldTypes.SELECT,
        enabled: true,
        bulkEdit: true,
      },
      children_data: {
        label: 'Linked records',
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        tableFormatter: (vals, row) => (
          <ChildrenDataTableFormatter vals={vals} row={row} />
        ),
        render: (_field, row, _setter, _collectionVals): React.ReactElement =>
          row?.children_data && (
            <ChildrenDataRenderer
              row={row}
              field="children_data"
              collectionVals={row.children_data}
              accountId={this.options?.account_id}
            />
          ),
      },
      details: {
        label: 'Payment allocations',
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        tableFormatter: (vals, row) => (
          <ChildrenDataTableFormatter
            vals={vals}
            row={row}
            title="Payment allocations"
          />
        ),
        render: (_field, row, _setter, _collectionVals): React.ReactElement => {
          return (
            row?.details && (
              <ChildrenDataRenderer
                row={row}
                field="details"
                title="Payment allocations"
                collectionVals={row.details}
                accountId={this.options?.account_id}
              />
            )
          );
        },
      },
      payment_status: {
        label: 'Payment status',
        enabled: true,
        bulkEdit: true,
      },
      agent_payout_rate: {
        label: 'Agent payout rate',
        enabled: true,
        type: FieldTypes.CUSTOM,
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;
          let maxWidth = 0;
          allRows.forEach((row) => {
            if (row.agent_payout_rate) {
              Object.entries(row.agent_payout_rate).forEach(([id, v]) => {
                const contact = mapData[id];
                const label = `${Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                })}: ${formatCurrency(v as number)}`;

                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_payout_rates"
              percentageFormat="percentage"
            />
          );
        },
        formatter: (val, collectionVals = []) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact =
                    collectionVals?.find((e) => e.str_id === k) ?? {};
                  return (
                    <Chip
                      key={contact.id}
                      component={'a'}
                      target="_blank"
                      clickable
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );
        },
      },
      agent_payout_rate_override: {
        label: 'Agent payout rate override',
        enabled: true,
        type: FieldTypes.CUSTOM,
        subPolicyDataIfEmpty: true,
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              disableConditions={false}
              key="agent_payout_rate_override"
              percentageFormat="percentage"
              showOverrideMode={true}
            />
          );
        },
        dynamicFormatter: (val, collectionVals = [], row = null) => {
          const agentPayoutRateOverride =
            val ?? row?.report?.agent_payout_rate_override;

          if (!agentPayoutRateOverride || !Array.isArray(collectionVals))
            return '';

          const agentPayoutRateOverrideDisplay = (
            <Box>
              {Object.entries(agentPayoutRateOverride)
                .filter(([key]) => key !== 'total' && key !== 'config')
                .map(([key, value]) => {
                  const contact =
                    collectionVals.find((e) => e.str_id === key) || {};
                  return (
                    <Chip
                      key={contact.id || key}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.percentage(Number(value) / 100)}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );

          return val ? (
            agentPayoutRateOverrideDisplay
          ) : (
            <Tooltip title="Sourced from policy data">
              {agentPayoutRateOverrideDisplay}
            </Tooltip>
          );
        },
        bulkEdit: true,
      },
      agent_commission_payout_rate: {
        label: 'Agent commission payout rate',
        enabled: true,
        type: FieldTypes.CUSTOM,
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;

          let maxWidth = 0;
          allRows.forEach((row) => {
            if (row.agent_commission_payout_rate) {
              Object.entries(row.agent_commission_payout_rate).forEach(
                ([id, v]) => {
                  const contact = mapData[id];
                  const label = `${Formatter.contact(contact, {
                    account_id: this.options?.account_id,
                  })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`;

                  const width = label.length * CHAR_WIDTH;
                  if (width > maxWidth) {
                    maxWidth = width;
                  }
                }
              );
            }
          });
          return maxWidth;
        },
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_payout_rates"
              percentageFormat="percentage"
            />
          );
        },
        formatter: (val, collectionVals = []) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact =
                    collectionVals?.find((e) => e.str_id === k) ?? {};
                  return (
                    <Chip
                      key={contact.id}
                      component={'a'}
                      target="_blank"
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      clickable
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );
        },
      },
      report_data_id: {
        label: 'Linked policy',
        enabled: true,
        readOnly: true,
        type: FieldTypes.CUSTOM,
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          allRows.forEach((row) => {
            const width = (row?.report?.str_id?.length || 0) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 40 * 2;
        }, // 40 *2 for 2 icons
        render(field, row) {
          return row[field.id] ? (
            <Box sx={{ m: 0.1 }}>
              <Chip
                label="Linked policy"
                component={Link}
                to={`/policies?id=${row?.report?.str_id}`}
                target="_blank"
                clickable
              />
            </Box>
          ) : (
            <Box sx={{ m: 0.1 }}>
              <Chip label="No linked policy" sx={{ cursor: 'default' }} />
            </Box>
          );
        },
        tableFormatter(val, row) {
          const ctx = useContext(ComparisonContext);
          return val ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ whiteSpace: 'nowrap' }}>
                {row?.report?.str_id}
              </span>
              <IconButton
                onClick={() => {
                  ctx.setData({
                    statements: [row],
                    policy: row.report,
                    defaultOpenStatement: row.str_id,
                  });
                  ctx.setShow(true);
                }}
              >
                <VerticalSplit />
              </IconButton>
              <IconButton
                component={Link}
                to={`/policies?id=${row?.report?.str_id}`}
                target="_blank"
              >
                <Launch />
              </IconButton>
            </Box>
          ) : null;
        },
      },
      reconciliation_status: {
        label: 'Commission status',
        enabled: true,
        readOnly: true,
        type: FieldTypes.TEXT,
      },
      is_virtual: {
        label: 'Virtual',
        enabled: true,
        readOnly: true,
        type: FieldTypes.BOOLEAN,
      },
      virtual_type: {
        label: 'Virtual type',
        enabled: true,
        readOnly: true,
        type: FieldTypes.TEXT,
      },
      reconciliation_method: {
        label: 'Reconciler 🔒',
        enabled: role === SystemRoles.ADMIN,
        readOnly: true,
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'reconcilers',
        formatter: (val, collectionVals = []) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const datum = collectionVals?.find((datum) => datum.id === +val);
            if (val === '0') {
              return 'Manual';
            }
            return datum ? (
              <Chip
                key={val}
                label={datum.name}
                clickable
                component={Link}
                to={`/reconciliation/reconcilers?id=${datum.str_id}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
      },
    };

    this.stickyColumns = ['policy_id'];
    this.actions =
      userRole === Roles.ACCOUNT_ADMIN
        ? [
            {
              id: 'comp_calc',
              label: 'Calculate comp',
              disabled: (row) =>
                [
                  'Approved',
                  'Manual',
                  'No payment',
                  'Offset',
                  'Paid',
                  'Reviewed',
                ].includes(row.agent_commissions_status),
              toolTipMessage:
                'Comp calculation not supported for the following payout statuses: Approved, Manual, No payment, Offset, Paid, Reviewed',
              onClick: async (row) => {
                // TODO: Temp implementation for TG demo. Figure out how to use API for the call. If not, replace with Axios.
                const headers = await API.getHeaders();
                await fetch(
                  `${process.env.REACT_APP_API}/api/data_processing/commissions/agents`,
                  {
                    method: 'post',
                    headers,
                    body: JSON.stringify({
                      isSync: true,
                      useGroupedCommissions: true,
                      id: row.id,
                    }),
                  }
                );
              },
            },
            {
              id: 'copy_link',
              label: 'Copy link',
              onClick: (row) => {
                copy(`${window.location.origin}/commissions?id=${row.str_id}`);
              },
            },
            {
              id: 'comp_profile_matcher',
              label: 'Comp profile matcher',
              onClick: (row) => {
                let url = `${window.location.origin}/admin/tools/comp-profile-matcher?statement_id=${row.str_id}`;
                const agents = row.contacts;
                if (agents.length > 0) {
                  const agentsQuery = agents.map(encodeURIComponent).join(',');
                  url += `&agents=${agentsQuery}`;
                }
                window.open(url, '_blank');
              },
              access: SystemRoles.ADMIN,
            },
          ]
        : [
            {
              id: 'copy_link',
              label: 'Copy link',
              onClick: (row) => {
                copy(`${window.location.origin}/commissions?id=${row.str_id}`);
              },
            },
          ];
    this.dateFilters = [
      {
        filterFieldName: 'Payment date',
        filterFieldId: 'payment_date',
        filters: [
          {
            label: 'Payment date start',
            filterKey: 'payment_date_start',
          },
          {
            label: 'Payment date end',
            filterKey: 'payment_date_end',
          },
          { filterKey: 'payment_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Processing date',
        filterFieldId: 'processing_date',
        filters: [
          {
            label: 'Processing date start',
            filterKey: 'processing_date_start',
          },
          {
            label: 'Processing date end',
            filterKey: 'processing_date_end',
          },
          { filterKey: 'processing_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Statement date',
        filterFieldId: 'invoice_date',
        filters: [
          {
            label: 'Statement date start',
            filterKey: 'invoice_date_start',
          },
          {
            label: 'Statement date end',
            filterKey: 'invoice_date_end',
          },
          { filterKey: 'invoice_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
      {
        filterFieldName: 'Effective date',
        filterFieldId: 'effective_date',
        filters: [
          {
            label: 'Effective date start',
            filterKey: 'effective_date_start',
          },
          {
            label: 'Effective date end',
            filterKey: 'effective_date_end',
          },
          { filterKey: 'effective_date_empty', type: FieldTypes.BOOLEAN },
        ],
      },
    ];
    this.outstandingFieldsInMobileView = [
      'policy_id',
      'commission_amount',
      'customer_name',
      'payment_date',
      'carrier_name',
      'premium_amount',
      'agent_payout_rate',
    ];

    // Add field key as field property
    // Delete fields that are not enabled
    Object.entries(this.fields).forEach(([k, v]) => {
      this.fields[k].id = k;
      if (!v.enabled) {
        delete this.fields[k];
      }
    });

    const allFields = Object.values(this.fields)
      .map((field) => {
        const rowMatches = field.id
          ? [field.id, ...(field.matches ?? [])]
          : (field.matches ?? []);
        return rowMatches.filter(Boolean);
      })
      .flat();

    this.fieldsCollapsed = [
      ...new Set(allFields.map((field) => field?.toString().toLowerCase())),
    ];
  }

  getDataInfo = (tableData: (string | number)[][], topN = 8) => {
    const rowData: any[] = [];
    const ranges: { start: number; end: number }[] = [];
    let curRange: { start: number; end: number } | null = {
      start: 0,
      end: 999,
    };

    const filteredTableData = tableData.filter((row) => Array.isArray(row));
    filteredTableData.forEach((row, i) => {
      let matches = 0;
      let filled = 0;

      const dateStyleRegex =
        /(?:\d{1,2})(?:\/|-)(?:\d{1,2})(?:\/|-)(?:\d{1,4})/;
      const hasDate = dateStyleRegex.test(row.join(' '));

      const hasCurrencyValue = row.filter((cell) =>
        cell?.toString().match(/\$\d{1,}/)
      ).length;

      const hasTotalTitle = row.toString().toLowerCase().includes('total:');

      row.forEach((cell) => {
        if (cell || cell === 0) {
          filled += 1;
        }
        if (this.fieldsCollapsed.includes(cell?.toString()?.toLowerCase())) {
          matches += 1;
        }
      });
      if (filled === 0 && curRange !== null) {
        curRange.end = i - 1;
        ranges.push(curRange);
        curRange = null;
      } else if (filled > 0 && curRange === null) {
        curRange = { start: i, end: 999 };
      }
      if (!hasDate && !hasCurrencyValue && !hasTotalTitle) {
        rowData.push({ index: i, matches, filled, length: row.length });
      }
    });
    if (curRange !== null) {
      ranges.push({ ...curRange, end: filteredTableData.length - 1 });
    }

    let rowDataMatches;
    // No matches - use top n rows
    if (rowData.filter((row) => row.matches > 0).length === 0) {
      rowDataMatches = rowData.splice(0, Math.min(topN, rowData.length));
    } else {
      rowDataMatches = rowData
        .sort((a, b) => b.matches - a.matches)
        .filter((row) => row.matches > 0)
        .splice(
          0,
          Math.min(
            topN,
            rowData.filter((row) => row.matches > 0).length,
            rowData.length
          )
        );
    }

    const rangeData = rowDataMatches.map((row) => ({
      index: row.index,
      count: row.matches,
      fields: filteredTableData[row.index],
      data: filteredTableData.filter(
        (dataRow, i) =>
          i >
            ranges.filter((a) => a.start <= row.index)[
              ranges.filter((a) => a.start <= row.index).length - 1
            ]?.start &&
          i <=
            ranges.filter((a) => a.start <= row.index)[
              ranges.filter((a) => a.start <= row.index).length - 1
            ]?.end &&
          i > row.index
      ),
    }));
    return { rowData, ranges, rangeData };
  };
}

export default Statements;
