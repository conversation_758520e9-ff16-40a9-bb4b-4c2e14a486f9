import * as XLSX from 'xlsx';

class Spreadsheet {
  #data;

  // Private
  constructor(data) {
    this.#data = data;
  }

  static loadSpreadsheet = async (file) => {
    const data = XLSX.read(await file.arrayBuffer(), {
      raw: file.type === 'text/csv',
      cellStyles: true,
    });
    return new Spreadsheet(data);
  };

  static indexToCol = (index) => {
    return index < 0
      ? ''
      : this.indexToCol(index / 26 - 1) +
          String.fromCharCode((index % 26) + 65);
  };

  getSheets = () => {
    const originalSheets = this.#data.SheetNames;

    if (originalSheets.length > 1) {
      return ['All', ...originalSheets];
    }
    return originalSheets;
  };

  getJson = (sheetName, isRawData = false) => {
    if (sheetName === 'All') {
      return this.createCombinedData(isRawData);
    }

    if (sheetName && this.#data.SheetNames.includes(sheetName)) {
      const json = XLSX.utils.sheet_to_json(this.#data.Sheets[sheetName], {
        header: 1,
        raw: false,
        blankrows: false,
      });

      if (!isRawData) {
        let lastRow = json.length - 1;
        for (; lastRow > 0; lastRow -= 1) {
          if (json[lastRow].length > 0) {
            break;
          }
        }
        return json.slice(0, lastRow + 1);
      }
      return XLSX.utils.sheet_to_json(this.#data.Sheets[sheetName]);
    }
    return {};
  };

  createCombinedData = (isRawData = false) => {
    const originalSheets = this.#data.SheetNames;
    const combinedData = [];
    let isFirstSheet = true;

    originalSheets.forEach((sheet) => {
      try {
        const sheetData = this.getSingleSheetJson(sheet, isRawData);

        if (sheetData && Array.isArray(sheetData) && sheetData.length > 0) {
          if (!isFirstSheet) {
            combinedData.push([]);
          }

          combinedData.push(['Sheet name:', sheet]);

          sheetData.forEach((row) => {
            if (row && Array.isArray(row)) {
              combinedData.push([...row]);
            }
          });

          isFirstSheet = false;
        }
      } catch (error) {
        console.warn(`Failed to process sheet "${sheet}":`, error);
      }
    });

    if (combinedData.length === 0) {
      return [];
    }

    return combinedData;
  };

  getSingleSheetJson = (sheetName, isRawData = false) => {
    if (sheetName && this.#data.SheetNames.includes(sheetName)) {
      const json = XLSX.utils.sheet_to_json(this.#data.Sheets[sheetName], {
        header: 1,
        raw: false,
        blankrows: false,
      });

      if (!isRawData) {
        let lastRow = json.length - 1;
        for (; lastRow > 0; lastRow -= 1) {
          if (json[lastRow].length > 0) {
            break;
          }
        }
        return json.slice(0, lastRow + 1);
      }
      return XLSX.utils.sheet_to_json(this.#data.Sheets[sheetName]);
    }
    return [];
  };

  getCombinedInfo = () => {
    const originalSheets = this.#data.SheetNames;

    if (originalSheets.length <= 1) {
      return null;
    }

    let totalRows = 0;
    let validSheets = 0;
    const sheetInfo = [];

    originalSheets.forEach((sheet) => {
      try {
        const sheetData = this.getSingleSheetJson(sheet, false);
        const dataRows = sheetData.length > 1 ? sheetData.length - 1 : 0;

        if (dataRows > 0) {
          validSheets++;
          totalRows += dataRows;
          sheetInfo.push({
            name: sheet,
            rows: dataRows,
            columns: sheetData[0] ? sheetData[0].length : 0,
          });
        }
      } catch (error) {
        console.warn(`Failed to analyze sheet "${sheet}":`, error);
      }
    });

    return {
      totalSheets: originalSheets.length,
      validSheets,
      totalDataRows: totalRows,
      sheets: sheetInfo,
    };
  };
}

export default Spreadsheet;
