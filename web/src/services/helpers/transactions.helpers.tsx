import Box from '@mui/system/Box/Box';
import { TransactionParty } from 'common/globalTypes';
import { ReceivableValueMap } from 'common/constants/receivable_value';

import { ReceivableValueColumn } from '@/components/CommissionsDataView/columns/ReceivableValueColumn';
import { FieldTypes } from '@/types';

export const buildReceivableValueFilter = (
  transactionParty: TransactionParty
): {
  label: string;
  enabled: boolean;
  required: boolean;
  type: FieldTypes;
  tableFormatter: (
    fields?: Record<string, any>,
    row?: Record<string, any>
  ) => JSX.Element;
  render: (field, row) => JSX.Element;
} => {
  const rate_field = ReceivableValueMap[transactionParty].rate_field;
  const label = ReceivableValueMap[transactionParty].label;

  return {
    label,
    enabled: true,
    required: false,
    type: FieldTypes.CUSTOM,
    tableFormatter: (fields, row) => {
      const rowParsed = row || {};
      const { value, str_id } = fields || rowParsed[rate_field] || {};
      return <ReceivableValueColumn rateFormatted={value} strId={str_id} />;
    },
    render: (field, row) => (
      <Box>
        {label}: {row[field.id]?.value || 'N/A'}
      </Box>
    ),
  };
};

export const buildReceivableValueFilterRenderOnly = (
  transactionParty: TransactionParty
): {
  tableFormatter: (
    fields?: Record<string, any>,
    row?: Record<string, any>
  ) => JSX.Element;
  render: (field, row) => JSX.Element;
} => {
  const rate_field = ReceivableValueMap[transactionParty].rate_field;
  const label = ReceivableValueMap[transactionParty].label;

  return {
    tableFormatter: (fields, row) => {
      const rowParsed = row || {};
      const rateFieldValue = rowParsed[rate_field];

      if (!rateFieldValue) {
        return null;
      }

      if (
        typeof rateFieldValue === 'object' &&
        !Array.isArray(rateFieldValue)
      ) {
        const { value, str_id } = fields || rateFieldValue || {};
        return <ReceivableValueColumn rateFormatted={value} strId={str_id} />;
      }

      return rateFieldValue.map((rateFieldItem, index) => {
        const { value, str_id } = rateFieldItem || {};
        return (
          <ReceivableValueColumn
            key={index}
            rateFormatted={value}
            strId={str_id}
          />
        );
      });
    },
    render: (field, row) => (
      <Box>
        {label}: {row[field.id]?.value || 'N/A'}
      </Box>
    ),
  };
};
