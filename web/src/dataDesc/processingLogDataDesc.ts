import CommonFormatter from 'common/Formatter';
import { DataProcessingTypes } from 'common/dataProcessingType';
import { capitalize } from 'lodash-es';

import Formatter from '@/services/Formatter';

export const getProcessingLogDataDesc = (): DataDesc.Props => ({
  label: 'Activity logs',
  table: 'v2/data_processing',
  editable: true,
  fields: [
    {
      id: 'created_at',
      label: 'Date',
      formatter: Formatter.dateTime,
      readOnly: true,
    },
    {
      id: 'status',
      label: 'Status',
      tableFormatter: Formatter.status,
      readOnly: true,
    },
    { id: 'type', label: 'Type', readOnly: true },
    {
      id: 'params',
      label: 'Params',
      readOnly: true,
      disableSort: true,
      tableFormatter: (val) => Formatter.getJsonStringToggle({ value: val }),
    },
    {
      id: 'duration',
      label: 'Duration',
      tableFormatter: CommonFormatter.duration,
      readOnly: true,
    },
    { id: 'notes', label: 'Notes', disableSort: true },
    {
      id: 'stats',
      label: 'Stats',
      readOnly: true,
      formatter: (val) => Formatter.getJsonStringToggle({ value: val }),
    },
    {
      id: 'created_by',
      label: 'User',
      formatter: (val, row) =>
        `${Formatter.contact(row.user)}${row.proxy_user ? ` (via ${Formatter.contact(row.proxy_user)})` : ''}`,
      readOnly: true,
    },
  ],
  filters: [
    {
      label: 'Type',
      type: 'select',
      apiParamKey: 'qc',
      options: [
        {
          label: 'All',
          value: '0',
        },
      ].concat(
        Object.values(DataProcessingTypes).map((type) => ({
          label: capitalize(type.replaceAll('_', ' ')),
          value: type,
        }))
      ),
    },
  ],
});
