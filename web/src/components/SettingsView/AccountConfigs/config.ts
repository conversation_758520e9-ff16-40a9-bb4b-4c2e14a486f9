import { WorkerNames } from 'common/constants';

import { IntegrationConfig } from './types';

export const INTEGRATION_CONFIGS: IntegrationConfig[] = [
  {
    value: WorkerNames.SmartOfficeWorker,
    label: 'Smart Office',
    credentials: [
      { key: 'username', label: 'Username', required: true },
      { key: 'siteName', label: 'Site Name', required: true },
      { key: 'apiKey', label: 'API Key', required: true },
      { key: 'apiSecret', label: 'API Secret', required: true },
      { key: 'endpoint', label: 'Endpoint', required: false },
    ],
    defaultValues: {
      username: '',
      siteName: '',
      apiKey: '',
      apiSecret: '',
      endpoint: '',
    },
  },
  {
    value: WorkerNames.BenefitPointWorker,
    label: 'Benefit Point',
    credentials: [
      { key: 'brokerageId', label: 'Brokerage ID', required: true },
      { key: 'username', label: 'Username', required: true },
      { key: 'password', label: 'Password', required: true },
    ],
    defaultValues: {
      brokerageId: '',
      username: '',
      password: '',
    },
  },
  {
    value: WorkerNames.OneHQWorker,
    label: 'OneHQ',
    credentials: [{ key: 'apiKey', label: 'API Key', required: true }],
    defaultValues: {
      apiKey: '',
    },
  },
  {
    value: WorkerNames.AgencyIntegratorWorker,
    label: 'Agency Integrator',
    credentials: [
      { key: 'apiKey', label: 'API Key', required: true },
      { key: 'endpoint', label: 'Endpoint', required: true },
    ],
    defaultValues: {
      apiKey: '',
      endpoint: '',
    },
  },
  {
    value: WorkerNames.MyAdvisorGridsWorker,
    label: 'My Advisor Grids',
    credentials: [
      { key: 'apiKey', label: 'API Key', required: true },
      { key: 'endpoint', label: 'Endpoint', required: true },
    ],
    defaultValues: {
      apiKey: '',
      endpoint: '',
    },
  },
  {
    value: WorkerNames.NowCertsWorker,
    label: 'NowCerts',
    credentials: [
      { key: 'username', label: 'Username', required: true },
      { key: 'password', label: 'Password', required: true },
    ],
    defaultValues: {
      username: '',
      password: '',
    },
  },
];
