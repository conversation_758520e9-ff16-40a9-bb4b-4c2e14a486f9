export interface ValueEditorProps {
  field: {
    id: string;
    [key: string]: any;
  };
  row: {
    type: string;
    [key: string]: any;
  };
  setter: (updatedRow: any) => void;
}

export interface CredentialField {
  key: string;
  label: string;
  required: boolean;
}

export interface DataSyncValue {
  worker: string;
  credentials: Record<string, string>;
  entities: string[];
}

export interface IntegrationConfig {
  value: string;
  label: string;
  credentials: CredentialField[];
  defaultValues: Record<string, string>;
}
