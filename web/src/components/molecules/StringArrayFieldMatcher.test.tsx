import { render, screen, fireEvent } from '@testing-library/react';

import StringArrayFieldMatcher from '@/components/molecules/StringArrayFieldMatcher';

const renderComponent = (props = {}) => {
  const defaultProps = {
    fieldMatcher: { value: '' },
    fieldsMatchers: [{ value: '' }],
    i: 0,
    setValue: jest.fn(),
    ...props,
  };

  return render(<StringArrayFieldMatcher {...defaultProps} />);
};

describe('StringArrayFieldMatcher Component', () => {
  test('Renders correctly with empty initial values', () => {
    renderComponent();
    expect(screen.getByLabelText('Values')).toBeInTheDocument();
    expect(screen.getByText('Add')).toBeInTheDocument();
    expect(screen.getByText('Add')).toBeDisabled();
  });

  test('Enables Add button when input has value', () => {
    renderComponent();
    const input = screen.getByLabelText('Values');

    fireEvent.change(input, { target: { value: 'test-item' } });

    expect(screen.getByText('Add')).not.toBeDisabled();
  });

  test('Adds a new value when clicking Add button', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: '' },
      fieldsMatchers: [{ value: '' }],
      setValue,
    });

    const input = screen.getByLabelText('Values');
    fireEvent.change(input, { target: { value: 'test-item' } });

    const addButton = screen.getByText('Add');
    fireEvent.click(addButton);

    expect(setValue).toHaveBeenCalledWith([{ value: 'test-item' }]);
    expect(input).toHaveValue(''); // Input should be cleared
  });

  test('Adds a new value when pressing Enter', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: '' },
      fieldsMatchers: [{ value: '' }],
      setValue,
    });

    const input = screen.getByLabelText('Values');
    fireEvent.change(input, { target: { value: 'test-item' } });
    fireEvent.keyDown(input, { key: 'Enter' });

    expect(setValue).toHaveBeenCalledWith([{ value: 'test-item' }]);
    expect(input).toHaveValue('');
  });

  test('Displays existing values as chips', () => {
    renderComponent({
      fieldMatcher: { value: 'item1,item2,item3' },
      fieldsMatchers: [{ value: 'item1,item2,item3' }],
    });

    expect(screen.getByText('item1')).toBeInTheDocument();
    expect(screen.getByText('item2')).toBeInTheDocument();
    expect(screen.getByText('item3')).toBeInTheDocument();
  });

  test('Deletes a value when chip delete button is clicked', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: 'item1,item2,item3' },
      fieldsMatchers: [{ value: 'item1,item2,item3' }],
      setValue,
    });

    const deleteIcon = screen.getByTestId('delete-chip-item1');
    fireEvent.click(deleteIcon);

    expect(setValue).toHaveBeenCalledWith([{ value: 'item2,item3' }]);
  });

  test('Parses array values correctly', () => {
    renderComponent({
      fieldMatcher: { value: ['item1', 'item2', 'item3'] },
      fieldsMatchers: [{ value: ['item1', 'item2', 'item3'] }],
    });

    expect(screen.getByText('item1')).toBeInTheDocument();
    expect(screen.getByText('item2')).toBeInTheDocument();
    expect(screen.getByText('item3')).toBeInTheDocument();
  });

  test('Handles empty strings properly', () => {
    renderComponent({
      fieldMatcher: { value: '' },
      fieldsMatchers: [{ value: '' }],
    });

    // No chips should be rendered
    const chipElements = screen.queryAllByRole('button', { name: /delete/i });
    expect(chipElements.length).toBe(0);
  });

  test('Trims whitespace from added values', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: '' },
      fieldsMatchers: [{ value: '' }],
      setValue,
    });

    const input = screen.getByLabelText('Values');
    fireEvent.change(input, { target: { value: '  test-item  ' } });

    const addButton = screen.getByText('Add');
    fireEvent.click(addButton);

    expect(setValue).toHaveBeenCalledWith([{ value: 'test-item' }]);
  });

  test('Does not add empty values', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: '' },
      fieldsMatchers: [{ value: '' }],
      setValue,
    });

    const input = screen.getByLabelText('Values');
    fireEvent.change(input, { target: { value: '   ' } });

    const addButton = screen.getByText('Add');
    expect(addButton).toBeDisabled();

    fireEvent.click(addButton);
    expect(setValue).not.toHaveBeenCalled();
  });

  test('Appends new values to existing values', () => {
    const setValue = jest.fn();
    renderComponent({
      fieldMatcher: { value: 'item1,item2' },
      fieldsMatchers: [{ value: 'item1,item2' }],
      setValue,
    });

    const input = screen.getByLabelText('Values');
    fireEvent.change(input, { target: { value: 'item3' } });

    const addButton = screen.getByText('Add');
    fireEvent.click(addButton);

    expect(setValue).toHaveBeenCalledWith([{ value: 'item1,item2,item3' }]);
  });
});
