import { PopoverOrigin, PopoverProps, SxProps } from '@mui/material';
import React, { MouseEvent } from 'react';

export type Option = {
  value: string | number;
  label?: string;
  // This is to keep other properties
  // For example, your option item may contain a property "type"
  // and you want to use it
  [key: string]: unknown;
  subList?: Option[];
};

export type BaseSelectProps = {
  options: Option[];
  onChange: (item: Option | Option[]) => void;
  value?: Option | Option[];
  multiple?: boolean;
  disabled?: boolean;
  label?: string;
  sx?: SxProps;
  listContainerSx?: SxProps;
  placeholder?: string;
  enableSearch?: boolean;
  disableAllOption?: boolean;
  renderLabel?: (props: {
    isSelected: boolean;
    sx: SxProps;
    key: Option['value'];
  }) => React.ReactNode;
  onSearch?: (v: string) => void;
  searchKeyword?: string;
  tokenizeSearch?: boolean;
  enableSelectAllSearchResult?: boolean;
  sortLabel?: boolean;
  enableActiveColor?: boolean;
  renderField?: (props: {
    onClick: (evt: MouseEvent<HTMLElement>) => void;
    displayValue?: string;
  }) => React.ReactNode;
  renderValue?: (value?: Option | Option[]) => React.ReactNode;
  anchorOrigin?: PopoverOrigin;
  popoverProps?: Omit<PopoverProps, 'open' | 'onClose'>;
  containerWidth?: number;
  onReachBottom?: () => void;
  isLoading?: boolean;
  dataTestId?: string;
  endAdornment?: React.ReactNode;
};

export type ItemData = {
  options: BaseSelectProps['options'];
  onChange: (item: Option) => void;
  selectedValues: { [key: Option['value']]: true };
  multiple?: boolean;
  isAllSelected?: boolean;
  onTargetSelect: (item: Option) => void;
  renderLabel?: BaseSelectProps['renderLabel'];
  disableAllOption?: boolean;
  containerWidth?: number;
  isLoading?: boolean;
};

export type SelectProps<T, K extends boolean = false> = {
  options: T[];
  valueKey?: string;
  labelKey?: string;
  onChange: (value: K extends false ? T : T[]) => void;
  renderValue?: (value?: K extends false ? T : T[]) => React.ReactNode;
  multiple?: K;
  disabled?: boolean;
  value?: T | T[];

  // Async Select Props
  extractValue?: (item: T[]) => T | T[] | undefined;
  customLabel?: (item: any) => string;
  url?: string;
  dataKey?: string;
  requestConfig?: {
    method?: 'get' | 'post';
    data?: object;
    params?: object;
  };
} & Pick<
  BaseSelectProps,
  | 'sx'
  | 'label'
  | 'placeholder'
  | 'enableSearch'
  | 'disableAllOption'
  | 'renderLabel'
  | 'onSearch'
  | 'searchKeyword'
  | 'tokenizeSearch'
  | 'listContainerSx'
  | 'enableSelectAllSearchResult'
  | 'sortLabel'
  | 'enableActiveColor'
  | 'renderField'
  | 'popoverProps'
  | 'containerWidth'
  | 'onReachBottom'
  | 'isLoading'
  | 'dataTestId'
  | 'endAdornment'
  | 'renderValue'
>;
