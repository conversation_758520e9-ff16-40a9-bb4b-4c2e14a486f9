import { Download, History } from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { CompReportViewTypes } from 'common/globalTypes';
import { cloneDeep } from 'lodash-es';
import { useContext, useState } from 'react';
import CommonFormatter from 'common/Formatter';

import EnhancedTable from '@/components/molecules/EnhancedTable';
import { UIStateContext } from '@/contexts/UIStateProvider';
import { auth } from '@/firebase';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import Reconciliations from '@/services/Reconciliations';
import Reports from '@/services/Reports';
import Statements from '@/services/Statements';
import { exportToCsv } from '@/services/helpers';
import { useAccountStore, useRoleStore } from '@/store';
import { Roles, savedReportsGroupsTemplates } from '@/types';
import CommissionCalcLog from './CommissionCalcLog';
import useSnackbar from '@/contexts/useSnackbar';

interface ReportDataProps {
  selectedSnapshotReport: any;
  filteredData?: any[];
  isLoading: boolean;
  setOpenHistory: (open: boolean) => void;
  handleGroupingChange?: (event: any, newGrouping: string | null) => void;
  grouping?: string;
  handleViewChange?: (event: any, newGrouping: string | null) => void;
  view?: string;
  refetch?: () => void;
  showActions?: boolean;
  isHistoryView?: boolean;
}

const ReportData: React.FC<ReportDataProps> = ({
  selectedSnapshotReport,
  filteredData = [],
  isLoading,
  setOpenHistory,
  handleGroupingChange = null,
  grouping = '',
  handleViewChange = null,
  view = '',
  refetch = () => {},
  showActions = true,
  isHistoryView = false,
}): JSX.Element => {
  const isMobile = useMediaQuery('(max-width:600px)');
  const [isDownloading, setIsDownloading] = useState(false);
  const {
    role: [role],
  } = useContext(UIStateContext);
  const { selectedAccount } = useAccountStore();
  const { userRole } = useRoleStore();

  const { showSnackbar } = useSnackbar();

  const {
    isLoading: isLoadingAgentCommissionsSettings,
    data: agentCommissionsSettings,
  } = API.getBasicQuery('accounts/settings/agent-commissions');

  const SavedReportsPatcher = API.getMutation('saved_reports/reports', 'PATCH');

  if (
    selectedSnapshotReport?.snapshot_data.reportPage === 'commissions' &&
    !isLoadingAgentCommissionsSettings
  ) {
    const agentCommissionsDownlineData =
      agentCommissionsSettings?.agentCommissionsDirectDownlineDataAccess ||
      agentCommissionsSettings?.agentCommissionsExtendedDownlineDataAccess
        ? true
        : false;

    const statements = new Statements('insurance', role, userRole, {
      account_id: selectedAccount?.accountId,
    });

    selectedSnapshotReport.snapshot_data.headers.forEach((header) => {
      if (!Object.prototype.hasOwnProperty.call(statements.fields, header.id)) {
        console.warn(
          `Header ID "${header.id}" does not match any key in statements.fields.`
        );
        return;
      }
      // Match statements.fields with headers based on label
      const field = statements.fields[header.id];

      if (field) {
        Object.assign(header, field);
        statements.dynamicSelectsConfig?.forEach(
          ({ table, collectDataFields }) => {
            if (collectDataFields.includes(header.id)) {
              header.table = table;
            }
          }
        );
      }
      // Add a new field header.formatter if field is found using field.formatter
      if (field && field.formatter) {
        header.formatter = field.formatter;
      }
      if (field && field.tableFormatter) {
        header.tableFormatter = field.tableFormatter;
      }
      if (field && field.optionFormatter) {
        header.optionFormatter = field.optionFormatter;
      }
      if (field && field.optionValuer) {
        header.optionValuer = field.optionValuer;
      }
      if (field && field.render) {
        header.render = field.render;
      }
      // Custom formatters for commission payout reports
      // TODO: We need to refactor this to use a map for header formatters and extract formatter logic into separate functions to reduce nesting
      if (
        selectedSnapshotReport.saved_report_group?.template ===
        savedReportsGroupsTemplates.COMMISSION_PAYOUT
      ) {
        if (
          !agentCommissionsDownlineData &&
          (header.id === 'agent_commissions' || header.id === 'comp_calc')
        ) {
          header.tableFormatter = (val) => {
            if (!val) return '';
            return (
              <Box>
                {Object.entries(val)
                  .filter(([k, v]) => k !== 'total')
                  .map(([k, v]) => {
                    return (
                      <Chip
                        key={k}
                        label={`${Formatter.currency(v)}`}
                        sx={{ m: 0.1 }}
                      />
                    );
                  })}
              </Box>
            );
          };
        }
        if (
          header.id === 'agent_commissions_log' ||
          header.id === 'comp_calc_log'
        ) {
          header.tableFormatter = (val) => {
            if (!val) return '';
            return (
              <Box>
                {Object.entries(val)
                  .filter(([k, v]) => k !== 'total')
                  .map(([k, v]: [string, any]) => {
                    return (
                      <Box key={k}>
                        {Array.isArray(v) ? (
                          v.map((e, i) => (
                            <CommissionCalcLog commissionProfile={e} key={i} />
                          ))
                        ) : (
                          <Chip label={'***'} />
                        )}
                      </Box>
                    );
                  })}
              </Box>
            );
          };
        } else if (header.id === 'agent_payout_rate') {
          header.tableFormatter = (val) => {
            if (!val) return '';
            return (
              <Box>
                {Object.entries(val)
                  .filter(([k, v]) => k !== 'total')
                  .map(([k, v]) => {
                    return (
                      <Chip
                        key={k}
                        label={`${CommonFormatter.percentage(v as string, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`}
                        sx={{ m: 0.1 }}
                      />
                    );
                  })}
              </Box>
            );
          };
        }
      }
    });
    // Custom totals for commission payout reports
    if (
      selectedSnapshotReport?.snapshot_data.data?.totals &&
      selectedSnapshotReport.saved_report_group?.template ===
        savedReportsGroupsTemplates.COMMISSION_PAYOUT
    ) {
      const totals = selectedSnapshotReport.snapshot_data.data.totals;
      Object.entries(totals).forEach(([k, v]) => {
        // Remove the contact id for agent commissions totals
        if (
          !agentCommissionsDownlineData &&
          k === 'agent_commissions' &&
          typeof v === 'object' &&
          v !== null
        ) {
          totals[k] =
            v[selectedSnapshotReport?.snapshot_data?.data?.contactStrId] ??
            Object.values(v)[0];
        }
      });
    }
  }

  if (selectedSnapshotReport?.snapshot_data.reportPage === 'reconciliation') {
    const reconciliations = new Reconciliations('insurance', '');

    selectedSnapshotReport.snapshot_data.headers.forEach((header) => {
      // Match reconciliations.fields with headers based on label
      if (
        !Object.prototype.hasOwnProperty.call(reconciliations.fields, header.id)
      ) {
        console.warn(
          `Header ID "${header.id}" does not match any key in reconciliations.fields.`
        );
        return;
      }
      const field = reconciliations.fields[header.id];

      // Assign the field configs to the header
      if (field) {
        Object.assign(header, field);
      }
      // Add a new field header.formatter if field is found using field.formatter
      if (field && field.formatter) {
        header.formatter = field.formatter;
      }
      if (field && field.optionFormatter) {
        header.optionFormatter = field.optionFormatter;
      }
      if (field && field.optionValuer) {
        header.optionValuer = field.optionValuer;
      }
      if (field && field.getter) {
        header.getter = field.getter;
      }
      if (field && field.render) {
        header.render = field.render;
      }
    });
  }

  if (selectedSnapshotReport?.snapshot_data.reportPage === 'policies') {
    const reports = new Reports('insurance', {});

    selectedSnapshotReport.snapshot_data.headers.forEach((header) => {
      // Match reports.fields with headers based on label
      if (!Object.prototype.hasOwnProperty.call(reports.fields, header.id)) {
        console.warn(
          `Header ID "${header.id}" does not match any key in reports.fields.`
        );
        return;
      }
      const field = reports.fields[header.id];

      if (field) {
        Object.assign(header, field);
      }

      // Add a new field header.formatter if field is found using field.formatter
      if (field && field.formatter) {
        header.formatter = field.formatter;
      }
      if (field && field.optionFormatter) {
        header.optionFormatter = field.optionFormatter;
      }
      if (field && field.optionValuer) {
        header.optionValuer = field.optionValuer;
      }
      if (field && field.render) {
        header.render = field.render;
      }
    });
  }

  const onExport = async () => {
    setIsDownloading(true);
    const idToken = await auth.currentUser?.getIdToken(true);
    await exportToCsv(
      {
        q: selectedSnapshotReport.str_id,
      },
      {
        idToken: idToken || '',
        endpoint: 'saved_reports',
        exportOptions: { fileName: selectedSnapshotReport.name, view: view },
      }
    );
    setIsDownloading(false);
  };

  const handleBulkEdit = async (selected, updateData) => {
    let data = cloneDeep(selectedSnapshotReport?.snapshot_data.data?.data);

    data = data
      .map((item) => {
        if (selected.includes(item.id)) {
          Object.keys(item).forEach((key) => {
            if (
              !Object.prototype.hasOwnProperty.call(updateData, key) &&
              key !== 'id'
            ) {
              delete item[key];
            }
          });
          Object.assign(item, updateData);
          return item;
        }
        return null;
      })
      .filter((item) => item !== null);

    const params = {
      id: selectedSnapshotReport.id,
      data,
    };

    const response = await SavedReportsPatcher.mutateAsync(params);

    if (response.status === 200) {
      showSnackbar('Report updated!', 'success');
      refetch();
    } else {
      showSnackbar('Error updating report!', 'error');
    }
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          mx: 2,
          flexWrap: isMobile ? 'wrap' : 'nowrap',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            mb: 0.5,
          }}
        >
          {typeof handleGroupingChange === 'function' &&
          typeof handleViewChange === 'function' ? (
            <>
              <ToggleButtonGroup
                sx={{ height: '34px', mr: 1 }}
                exclusive
                orientation={isMobile ? 'vertical' : 'horizontal'}
                color="primary"
                onChange={handleGroupingChange}
                size="small"
                value={grouping}
              >
                <ToggleButton value="policyNumber">
                  Group by policy
                </ToggleButton>
                <ToggleButton value="none">Commission data</ToggleButton>
              </ToggleButtonGroup>
              {userRole === Roles.ACCOUNT_ADMIN && (
                <ToggleButtonGroup
                  sx={{ height: '34px' }}
                  exclusive
                  orientation={isMobile ? 'vertical' : 'horizontal'}
                  color="primary"
                  onChange={handleViewChange}
                  value={view}
                >
                  <ToggleButton value={CompReportViewTypes.ADMIN_VIEW}>
                    Admin view
                  </ToggleButton>
                  <ToggleButton value={CompReportViewTypes.PRODUCER_VIEW}>
                    Producer view
                  </ToggleButton>
                </ToggleButtonGroup>
              )}
            </>
          ) : null}
        </Box>
        {showActions && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-end',
              height: '20px',
            }}
          >
            <Button
              onClick={() => setOpenHistory(true)}
              startIcon={<History />}
              variant="outlined"
              size="small"
              sx={{ mr: 1 }}
            >
              Report history
            </Button>
            <Button
              disabled={isDownloading}
              onClick={onExport}
              startIcon={<Download />}
              variant="outlined"
              size="small"
            >
              Export
            </Button>
          </Box>
        )}
      </Box>
      {isLoading || isLoadingAgentCommissionsSettings ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            mr: 2,
            mt: 2,
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 500, ml: 1 }}>
            Loading...
          </Typography>
        </Box>
      ) : (
        <EnhancedTable
          dense
          headers={selectedSnapshotReport?.snapshot_data.headers}
          rows={
            filteredData
              ? filteredData
              : selectedSnapshotReport?.snapshot_data.data?.data
          }
          onBulkEdit={async (selected, updateData) => {
            await handleBulkEdit(selected, updateData);
          }}
          actionsEnabled={(v) => false}
          onDelete={!isHistoryView ? (v) => false : undefined}
          stickyHeader
          paginated
          showTotals={true}
          totals={selectedSnapshotReport?.snapshot_data.data?.totals}
        />
      )}
    </>
  );
};

export default ReportData;
