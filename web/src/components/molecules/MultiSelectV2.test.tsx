import { render, screen, fireEvent } from '@testing-library/react';

import MultiSelectV2, {
  MultiSelectProps,
} from '@/components/molecules/MultiSelectV2';

const renderComponent = (props: Partial<MultiSelectProps> = {}) => {
  const defaultProps: MultiSelectProps = {
    options: [],
    onChange: jest.fn(),
    valuer: jest.fn(),
    formatter: jest.fn(),
    label: 'Test Label',
    multiple: true,
    disabled: false,
    enablePagination: false,
    itemsPerPage: 10,
    placeholder: 'Select options',
    loading: false,
    ...props,
  };
  return render(<MultiSelectV2 {...defaultProps} />);
};

describe('MultiSelectV2 Component', () => {
  test('renders correctly with default props', () => {
    renderComponent();
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Select options')).toBeInTheDocument();
  });

  test('displays string[] options correctly', () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const formatter = (option: any) => option;
    renderComponent({ options, formatter });
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    options.forEach((option) => {
      expect(screen.getByText(option)).toBeInTheDocument();
    });
  });

  test('displays object options correctly', () => {
    const options = [
      { id: 1, label: 'Option 1' },
      { id: 2, label: 'Option 2' },
      { id: 3, label: 'Option 3' },
    ];
    const formatter = (option: any) => option.label;
    renderComponent({ options, formatter });
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    options.forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
  });

  test('handles pagination correctly', () => {
    const options = Array.from({ length: 20 }, (_, i) => `Option ${i + 1}`);
    const formatter = (option: any) => option;
    renderComponent({
      options,
      formatter,
      enablePagination: true,
      itemsPerPage: 10,
    });
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    options.slice(0, 10).forEach((option) => {
      expect(screen.getByText(option)).toBeInTheDocument();
    });
    fireEvent.click(screen.getByText('Show More'));
    options.slice(10, 20).forEach((option) => {
      expect(screen.getByText(option)).toBeInTheDocument();
    });
  });

  test('handles pagination for object options correctly', () => {
    const options = Array.from({ length: 20 }, (_, i) => ({
      value: `Option ${i + 1}`,
      label: `Option ${i + 1}`,
    }));
    const formatter = (option: any) => option.label;
    renderComponent({
      options,
      enablePagination: true,
      itemsPerPage: 10,
      formatter,
    });
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    options.slice(0, 10).forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
    fireEvent.click(screen.getByText('Show More'));
    options.slice(10, 20).forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
    });
  });

  test('displays loading state correctly', () => {
    renderComponent({ loading: true });
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles disabled prop correctly', () => {
    renderComponent({ disabled: true });
    const selectElement = screen.getByLabelText('Test Label');
    expect(selectElement).toBeDisabled();
  });

  test('renders custom options correctly', () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const formatter = (option: any) => option;
    const renderCustomOption = jest.fn(() => <div>Custom Option</div>);
    renderComponent({ options, renderCustomOption, formatter });
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    expect(screen.getByText('Custom Option')).toBeInTheDocument();
    expect(renderCustomOption).toHaveBeenCalledWith([], expect.any(Array));
  });

  test('handles selection changes correctly', () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const formatter = (option: any) => option;
    const onChange = jest.fn();
    renderComponent({ options, onChange, formatter });
    fireEvent.mouseDown(screen.getByLabelText('Test Label'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(onChange).toHaveBeenCalledWith(
      [{ value: 'Option 1', label: 'Option 1', data: 'Option 1' }],
      options
    );
  });

  test('handles selection changes for object options correctly', () => {
    const options = [
      { id: 1, label: 'Option 1', value: '1 option' },
      { id: 2, label: 'Option 2', value: '2 option' },
      { id: 3, label: 'Option 3', value: '3 option' },
    ];
    const formatter = (option: any) => option.label;
    const valuer = (option: any) => option.id;
    const onChange = jest.fn();
    renderComponent({ options, onChange, formatter, valuer });
    fireEvent.mouseDown(screen.getByLabelText('Test Label'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(onChange).toHaveBeenCalledWith(
      [
        {
          value: 1,
          label: 'Option 1',
          data: { id: 1, label: 'Option 1', value: '1 option' },
        },
      ],
      options
    );
  });

  test('handles custom formatter and valuer for object options correctly', () => {
    const options = [
      { id: 1, label: 'Option 1', value: '1 option' },
      { id: 2, label: 'Option 2', value: '2 option' },
      { id: 3, label: 'Option 3', value: '3 option' },
    ];
    const formatter = (option: any) => `Custom: ${option.label}`;
    const valuer = (option: any) => `Custom: ${option.id}`;
    const onChange = jest.fn();
    renderComponent({ options, onChange, formatter, valuer });
    fireEvent.mouseDown(screen.getByLabelText('Test Label'));
    fireEvent.click(screen.getByText('Custom: Option 1'));
    expect(onChange).toHaveBeenCalledWith(
      [
        {
          value: 'Custom: 1',
          label: 'Custom: Option 1',
          data: { id: 1, label: 'Option 1', value: '1 option' },
        },
      ],
      options
    );
  });

  test('handles single selection mode correctly', () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const formatter = (option: any) => option;
    const onChange = jest.fn();
    renderComponent({ options, formatter, onChange, multiple: false });
    fireEvent.mouseDown(screen.getByLabelText('Test Label'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(onChange).toHaveBeenCalledWith(
      { value: 'Option 1', label: 'Option 1', data: 'Option 1' },
      options
    );
  });

  test('calls renderCustomOption with selected value and available options', () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const formatter = (option: any) => option;
    const renderCustomOption = jest.fn(() => <div>Custom Option</div>);
    renderComponent({ options, renderCustomOption, formatter });
    fireEvent.mouseDown(screen.getByLabelText('Test Label'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(renderCustomOption).toHaveBeenCalledWith(
      [{ value: 'Option 1', label: 'Option 1', data: 'Option 1' }],
      expect.any(Array)
    );
  });
});
