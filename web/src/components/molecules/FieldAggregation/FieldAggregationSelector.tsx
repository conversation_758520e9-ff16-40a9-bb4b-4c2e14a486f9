import React, { useMemo, useState } from 'react';
import { Icon<PERSON>utton, Menu, MenuItem, Box, Tooltip, Chip } from '@mui/material';
import CalculateOutlined from '@mui/icons-material/CalculateOutlined';
import PriceCheckOutlined from '@mui/icons-material/PriceCheckOutlined';
import ClearIcon from '@mui/icons-material/Clear';
import {
  DEFAULT_FIELD_VALUE,
  AggregationMethod,
  FormatterMethod,
} from 'common/constants/widget';

import { EnhancedSelect } from '../EnhancedSelect';

const FieldAggregationSelector = ({
  fields,
  selectedField,
  onRemove,
  onUpdate,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElFormatter, setAnchorElFormatter] = useState(null);
  const selectedAggregation = selectedField.aggregation_method || '';
  const selectedFieldValue = selectedField.field || '';
  const selectedFieldFormatter = selectedField.formatter || '';

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleFormatterMenuOpen = (event) => {
    setAnchorElFormatter(event.currentTarget);
  };

  const handleFormatterMenuClose = () => {
    setAnchorElFormatter(null);
  };

  const handleAggregationChange = (method) => {
    onUpdate({ ...selectedField, aggregation_method: method });
    handleMenuClose();
  };

  const handleFormatterChange = (method) => {
    onUpdate({ ...selectedField, formatter: method });
    handleFormatterMenuClose();
  };

  const fieldsSelection = useMemo(() => {
    const anyField = {
      name: DEFAULT_FIELD_VALUE,
      displayName: 'Any',
      defaultFormatter: FormatterMethod.NUMBER,
    };
    return [anyField, ...fields];
  }, [fields]);

  const handleFieldChange = (event, value) => {
    const dataUpdate = { ...selectedField, field: value };

    const selectedFieldData = fieldsSelection.find(
      (field) => field.name === value
    );

    if (value === DEFAULT_FIELD_VALUE) {
      if (
        ![AggregationMethod.COUNT, AggregationMethod.COUNT_ACCUMULATE].includes(
          selectedAggregation as AggregationMethod
        )
      ) {
        // The 'any' field should use 'Count' or 'CountAccumulate' as the aggregation method.
        dataUpdate.aggregation_method = AggregationMethod.COUNT;
      }
    }

    if (selectedFieldData && selectedFieldData.defaultFormatter) {
      dataUpdate.formatter = selectedFieldData.defaultFormatter;
    }

    onUpdate(dataUpdate);
  };

  const options = fieldsSelection.map((field) => ({
    id: field.name,
    label: field.displayName || field.name,
  }));

  const getFieldValue = () => {
    return (
      fieldsSelection.find((field) => field.name == selectedFieldValue)
        ?.displayName || selectedFieldValue
    );
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      className="mt-3"
    >
      <Box data-name="field-aggregator-field" className="w-1/2">
        <EnhancedSelect
          label="Field"
          options={options}
          value={{
            id: selectedFieldValue,
            label: getFieldValue(),
          }}
          onChange={(value) => {
            handleFieldChange(null, value.id);
          }}
          sx={{ width: '100%' }}
        />
      </Box>

      <Box ml={1} display={'flex'} alignItems={'center'}>
        {selectedAggregation ? (
          <Chip
            sx={{ marginRight: 1 }}
            data-name="field-aggregator-aggregation"
            onClick={handleMenuOpen}
            label={selectedAggregation}
            avatar={<CalculateOutlined />}
          />
        ) : (
          <Tooltip title="Aggregation method">
            <IconButton onClick={handleMenuOpen}>
              <CalculateOutlined />
            </IconButton>
          </Tooltip>
        )}

        {selectedFieldFormatter ? (
          <Chip
            data-name="field-aggregator-formatter"
            label={selectedFieldFormatter}
            avatar={<PriceCheckOutlined />}
            onClick={handleFormatterMenuOpen}
          />
        ) : (
          <Tooltip title="Result formatter">
            <IconButton onClick={handleFormatterMenuOpen}>
              <PriceCheckOutlined />
            </IconButton>
          </Tooltip>
        )}

        <IconButton onClick={onRemove}>
          <ClearIcon />
        </IconButton>
      </Box>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedFieldValue && selectedFieldValue !== 'any' && (
          <>
            <MenuItem
              onClick={() => handleAggregationChange(AggregationMethod.SUM)}
            >
              Sum
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleAggregationChange(AggregationMethod.SUM_ACCUMULATE)
              }
            >
              Sum (Accumulate)
            </MenuItem>
            {selectedFieldValue && selectedFieldValue.type === 'obj' && (
              <MenuItem
                onClick={() =>
                  handleAggregationChange(AggregationMethod.AGGREGATE)
                }
              >
                Aggregate
              </MenuItem>
            )}
            <MenuItem
              onClick={() => handleAggregationChange(AggregationMethod.AVERAGE)}
            >
              Average
            </MenuItem>
          </>
        )}
        <MenuItem
          onClick={() => handleAggregationChange(AggregationMethod.COUNT)}
        >
          Count
        </MenuItem>
        <MenuItem
          onClick={() =>
            handleAggregationChange(AggregationMethod.COUNT_ACCUMULATE)
          }
        >
          Count (Accumulate)
        </MenuItem>
      </Menu>
      <Menu
        anchorEl={anchorElFormatter}
        open={Boolean(anchorElFormatter)}
        onClose={handleFormatterMenuClose}
      >
        {selectedFieldValue && selectedFieldValue !== DEFAULT_FIELD_VALUE && (
          <>
            <MenuItem
              onClick={() => handleFormatterChange(FormatterMethod.CURRENCY)}
            >
              Currency
            </MenuItem>
            <MenuItem
              onClick={() => handleFormatterChange(FormatterMethod.PERCENTAGE)}
            >
              Percentage
            </MenuItem>
          </>
        )}
        <MenuItem onClick={() => handleFormatterChange(FormatterMethod.NUMBER)}>
          Number
        </MenuItem>
      </Menu>
    </div>
  );
};

export default FieldAggregationSelector;
