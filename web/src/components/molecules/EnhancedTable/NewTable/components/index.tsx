import { Box, SxProps, Tooltip } from '@mui/material';
import { AccountIds } from 'common/constants';
import {
  MaterialReactTable,
  MRT_ColumnDef,
  MRT_Row,
  MRT_SortingState,
  MRT_TablePagination,
} from 'material-react-table';
import { useEffect, useMemo, useRef } from 'react';
import dayjs from 'dayjs';
import { DataStates } from 'common/types/common';

import { requiresFintaryAdmin } from '@/services/helpers';
import { useAccountStore } from '@/store';
import { CHAR_WIDTH } from '../../constants';
import { RowsPerPage } from '../constants';
import {
  FloatingActionsProps,
  NewTableProps as InitialNewTableProps,
} from '../types';
import { Cell } from './Cell';
import { FloatingActions } from './FloatingActions';
import { useDragToScroll } from '@/hooks/useDragToScroll';
import { useInitDynamicSelects } from '../hooks/useInitDynamicSelects';
import { Footer } from './Footer';

export type NewTableFullProps = InitialNewTableProps & FloatingActionsProps;

/**
 * @deprecated This component is deprecated. Please use the `TableView` component instead.
 */
export const NewTable = ({
  data,
  columns,
  isLoading,
  rowKey = 'id',
  pagination,
  selectedRows,
  setSelected,
  setSelectedData,
  sorting,
  actionLoading,
  onEdit,
  actions,
  actionsEnabled,
  getSetActionLoadingByRowId,
  showTotals,
  sums,
  dynamicSelectsConfig,
  totals,
}: NewTableFullProps) => {
  const { refContainer, refMoveTargetEl } = useDragToScroll();
  const { selectedAccount } = useAccountStore();
  const refShiftKeyPressed = useRef(false);

  const { dynamicSelectsData } = useInitDynamicSelects({
    dynamicSelectsConfig,
    data,
    totals,
  });

  const _columns = useMemo<MRT_ColumnDef<(typeof data)[0]>[]>(() => {
    const cols = columns.map(
      (column, index): MRT_ColumnDef<(typeof data)[0]> => {
        const getMaxWidth = (key: string) => {
          let maxWidth = 0;
          data.forEach((row) => {
            const text = dayjs(row[key]).isValid() ? 'YYYY-MM-DD' : row[key];

            const width = (text?.length || 0) * CHAR_WIDTH;
            if (width > maxWidth) {
              maxWidth = width;
            }
          });

          return maxWidth < 80 ? 80 : maxWidth;
        };

        let estimatedWidth = getMaxWidth(column.id);
        const config = dynamicSelectsConfig?.find((item) => {
          return item.collectDataFields.includes(column.id);
        });

        let _dynamicData =
          config && dynamicSelectsData
            ? dynamicSelectsData[config.table]
            : undefined;

        if (!Array.isArray(_dynamicData)) {
          _dynamicData = _dynamicData?.data || [];
        }

        if (column.getWidth) {
          estimatedWidth = column.getWidth({
            estimatedWidth,
            allRows: data || [],
            dynamicSelectData: _dynamicData,
          });
        }
        const headerWidth = (column.label?.length || 0) * CHAR_WIDTH + 24 + 12; // 24 for sort icon, 12 for padding
        const width = Math.min(Math.max(estimatedWidth, headerWidth), 500);

        return {
          enableResizing: !column.sticky,
          accessorKey: column.id,
          header: '',
          enableColumnActions: false,
          footer: '',
          minSize: 80,
          maxSize: 1000,
          size: (width ?? 120) + (index === columns.length - 1 ? 140 : 0),
          Footer: () => {
            return (
              <Footer
                colIndex={index}
                column={column}
                showTotals={showTotals}
                sums={sums}
                dynamicSelectsData={dynamicSelectsData}
              />
            );
          },
          muiTableBodyCellProps: () => {
            return {
              sx: {
                ...column.style,
              },
            };
          },
          muiTableHeadCellProps: {
            sx: {
              ...column.style,
              py: 1,
              fontWeight: 500,
              '& .Mui-TableHeadCell-ResizeHandle-Wrapper': {
                visibility: 'hidden',
              },
              '& .MuiBadge-root': {
                visibility:
                  column.id === sorting.orderBy ? 'visible' : 'hidden',
              },
              ':hover': {
                '& .MuiBadge-root': {
                  visibility: 'visible',
                },
                '& .Mui-TableHeadCell-ResizeHandle-Wrapper': {
                  visibility: 'visible',
                },
              },
            },
          },
          Header: () => {
            const showToltip =
              (column.width || 0) <= column.label.length * CHAR_WIDTH + 24; // 24 is sort icon width
            const content = (
              <Box
                sx={{
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                }}
              >
                {column.label}
                {column.infoIcon && ' ⓘ'}
                {requiresFintaryAdmin(column.access) && ' 🔒'}
              </Box>
            );
            if (column.description)
              return (
                <Tooltip
                  title={
                    <Box>
                      <div>{column.description}</div>
                    </Box>
                  }
                  placement="top"
                >
                  {content}
                </Tooltip>
              );

            if (showToltip) {
              return (
                <Tooltip
                  title={
                    <Box>
                      <div>{column.label}</div>
                    </Box>
                  }
                  placement="top"
                >
                  {content}
                </Tooltip>
              );
            }
            return content;
          },

          Cell: ({ row }) => {
            const config = dynamicSelectsConfig?.find((item) => {
              return item.collectDataFields.includes(column.id);
            });

            const _dynamicData =
              config && dynamicSelectsData
                ? dynamicSelectsData[config.table]
                : undefined;

            return (
              <Cell
                column={column}
                rowData={row.original}
                dynamicData={_dynamicData || _dynamicData?.data}
              />
            );
          },
          enableSorting: !column.disableSort,
        };
      }
    );
    cols.push({
      accessorKey: 'mrt-row-actions',
      header: '',
      enableColumnActions: false,
      size: 0,
      maxSize: 0,
      minSize: 0,

      enableSorting: false,
      Header: () => <Box />,

      Cell: ({ row: tableRow }) => {
        const row = tableRow.original;
        return (
          <FloatingActions
            actionLoading={actionLoading}
            onEdit={onEdit}
            actions={actions}
            actionsEnabled={actionsEnabled}
            getSetActionLoadingByRowId={getSetActionLoadingByRowId}
            rowData={row}
          />
        );
      },
    });

    return cols;
  }, [
    actionLoading,
    actions,
    actionsEnabled,
    columns,
    dynamicSelectsConfig,
    dynamicSelectsData,
    getSetActionLoadingByRowId,
    onEdit,
    showTotals,
    sorting.orderBy,
    sums,
    data,
  ]);

  const rowSelection = useMemo(() => {
    return selectedRows.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {} as MRT_SortingState);
  }, [selectedRows]);

  const _pagination = useMemo(() => {
    return {
      pageIndex: pagination.page,
      pageSize: pagination.rowsPerPage,
    };
  }, [pagination.page, pagination.rowsPerPage]);

  const handlePagination = (updater: any) => {
    type Updater = (currentState: { pageIndex: number; pageSize: number }) => {
      pageIndex: number;
      pageSize: number;
    };
    const currentState = {
      pageIndex: pagination.page,
      pageSize: pagination.rowsPerPage,
    };
    const newState = (updater as Updater)(currentState);

    if (newState.pageIndex !== pagination.page)
      pagination.onPageChange(null, newState.pageIndex);

    if (newState.pageSize !== pagination.rowsPerPage)
      pagination.onRowsPerPageChange({ target: { value: newState.pageSize } });
  };

  const handleSelection = (updater: any) => {
    type Updater = (currentState: MRT_SortingState) => MRT_SortingState;
    const currentSelectedIdMap = selectedRows.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {} as MRT_SortingState);

    const newState = (updater as Updater)(currentSelectedIdMap);
    const newSelectedIdSet = new Set<any>();
    for (const key in newState) {
      if (newState[key]) {
        const _key = JSON.parse(key); // Convert back to real type
        newSelectedIdSet.add(_key);
      }
    }

    if (refShiftKeyPressed.current) {
      const indexes = [...newSelectedIdSet].map((id) =>
        data.findIndex(
          (v) => v.id === id || v.str_id === id || v[rowKey] === id
        )
      );

      const maxIndex = Math.max(...indexes);
      const minIndex = Math.min(...indexes);

      data.forEach((item, index) => {
        if (index >= minIndex && index <= maxIndex) {
          newSelectedIdSet.add(item.id || item.str_id || item[rowKey]);
        }
      });
    }

    const newList = [...newSelectedIdSet];

    setSelected(newList);
    setSelectedData(
      newList.map((id) =>
        data.find((v) => v.id === id || v.str_id === id || v[rowKey] === id)
      )
    );
  };

  const handleSorting = (updater: any) => {
    type Updater = (currentState: MRT_SortingState) => MRT_SortingState;
    const currentSorting = [
      { id: sorting.orderBy, desc: sorting.order === 'desc' },
    ];

    const newState = (updater as Updater)(currentSorting);
    sorting.setOrder(newState[0]?.desc ? 'desc' : 'asc');
    sorting.setOrderBy(newState[0]?.id);
  };

  const groupedCellBgColor = (row: MRT_Row<any>) => {
    return row.original.state === DataStates.GROUPED ||
      row.original.state === DataStates.ALLOCATED
      ? {
          backgroundColor: 'rgb(242, 242, 242)',
          '& td': {
            opacity: 0.65,
          },
        }
      : {};
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('NewTable render');
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Shift') {
        refShiftKeyPressed.current = true;
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.key === 'Shift') {
        refShiftKeyPressed.current = false;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  return (
    <MaterialReactTable
      enableBatchRowSelection={false}
      enableColumnResizing
      enableRowNumbers={AccountIds.TRANSGLOBAL === selectedAccount?.accountId}
      // === Sorting
      manualSorting
      onSortingChange={handleSorting}
      // === Virtualization
      enableRowVirtualization
      rowVirtualizerOptions={{
        overscan: 25,
      }}
      // === Sticky headers and footers
      enableFilters={false}
      enableStickyHeader
      // MaterialReactTable always convert id to string, even number
      // So using JSON.stringify to keep its type (convert back in handleSelection)
      getRowId={(v) => JSON.stringify(v.id || v.str_id || v[rowKey])}
      columns={_columns}
      data={data || []}
      rowCount={pagination.count}
      enableGlobalFilter={false}
      initialState={{
        density: 'compact',
      }}
      displayColumnDefOptions={{
        'mrt-row-select': {
          size: 28,
        },
        'mrt-row-numbers': {
          size: 28,
        },
      }}
      state={{
        isLoading,
        pagination: _pagination,
        sorting: [{ id: sorting.orderBy, desc: sorting.order === 'desc' }],
        columnPinning: {
          left: [
            'mrt-row-select',
            'mrt-row-numbers',
            ...columns.filter((v) => v.sticky === 'left').map((v) => v.id),
          ],
          right: ['mrt-row-actions'],
        },
        rowSelection,
      }}
      muiTablePaperProps={{
        elevation: 0,
        sx: {
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
      muiTableContainerProps={{
        ref: refMoveTargetEl as any,
        sx: {
          flex: 1,
          maxHeight: 'calc(100vh - 64px - 109px)',
        }, // 64px = AppBar, 109px = TopToolbar
      }}
      muiTableFooterProps={{
        sx: {
          '& .MuiTableCell-footer[data-pinned="true"]': {
            boxShadow: 'rgba(0, 0, 0, 0.05) 2px 0px 4px',
          },
          '& .MuiTableCell-footer[data-pinned="true"]:before': {
            boxShadow: 'none',
            backgroundColor: 'white',
          },
        },
      }}
      muiTableHeadProps={() => {
        return {
          sx: {
            '& .MuiTableCell-head[data-pinned="true"]': {
              boxShadow: 'rgba(0, 0, 0, 0.05) 2px 0px 4px',
            },
            '& .MuiTableCell-head[data-pinned="true"]:before': {
              boxShadow: 'none',
              backgroundColor: 'white',
            },
          },
        };
      }}
      muiTableBodyRowProps={({ row }) => {
        return {
          sx: {
            ...groupedCellBgColor(row),
            '& td.MuiTableCell-root[data-pinned="true"]': {
              boxShadow: 'rgba(0, 0, 0, 0.05) 2px 0px 4px',
            },
            '& td.MuiTableCell-root:before': {
              backgroundColor: 'white',
              boxShadow: 'none',
              ...groupedCellBgColor(row),
            },
            '& .MuiTableCell-root:after': {
              backgroundColor: 'rgba(33, 150, 243, 0.12)',
            },
            '&.MuiTableRow-root:hover': {
              '& .MuiTableCell-root:after': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
              '& .action-buttons': {
                display: 'flex',
              },
            },
          },
        };
      }}
      muiTableBodyCellProps={({ column }) => {
        let colActionSx: SxProps = {};
        if (column.id === 'mrt-row-actions')
          colActionSx = {
            overflow: 'visible',
            '&.MuiTableCell-body:before': {
              boxShadow: 'none',
            },
          };

        return {
          sx: {
            ...colActionSx,
          },
        };
      }}
      manualPagination
      muiPaginationProps={{
        rowsPerPageOptions: RowsPerPage,
      }}
      muiTableBodyProps={{
        ref: refContainer as any,
      }}
      onRowSelectionChange={handleSelection}
      enableRowSelection
      onPaginationChange={handlePagination}
      enableColumnPinning
      enableTableFooter
      renderBottomToolbar={({ table }) => {
        return (
          <Box
            sx={{
              background: 'white',
              position: 'absolute',
              right: 0,
              bottom: 0,
              '& .MuiTablePagination-root': {
                paddingTop: 0.5,
                paddingBottom: 0.5,
              },
            }}
          >
            <MRT_TablePagination table={table} />
          </Box>
        );
      }}
      enableTopToolbar={false}
    />
  );
};
