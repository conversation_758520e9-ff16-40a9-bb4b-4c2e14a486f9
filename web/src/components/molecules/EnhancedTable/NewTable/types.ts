import { SxProps } from '@mui/material';

import { FieldTypes } from '@/types';

export type NewTableProps = {
  dynamicSelectsConfig?: {
    table: string;
    queryParamName: string;
    queryParamValue: (string | number)[];
    collectDataFields: string[];
    dataExtractors?: {
      [field: string]: (
        rows: any,
        totals?: { [key: string]: any }
      ) => (string | number)[];
    };
    mapKey?: string;
  }[];
  sums: any[];
  showTotals?: boolean;
  data: any[];
  columns: Column[];
  isLoading: boolean;
  rowKey?: string;
  pagination: {
    count: number;
    page: number;
    rowsPerPage: number;
    onPageChange: (event: any, page: number) => void;
    onRowsPerPageChange: (event: { target: { value: number } }) => void;
  };
  selectedRows: any[];
  setSelected: (data: any[]) => void;
  setSelectedData: (data: any[]) => void;
  sorting: {
    orderBy: string;
    order: 'asc' | 'desc';
    setOrderBy: (orderBy: string) => void;
    setOrder: (order: 'asc' | 'desc') => void;
  };
  totals?: { [key: string]: any };
};

export type Column = {
  id: string;
  id2?: string;
  label: string;
  table: string;
  type?: FieldTypes;
  dynamicFormatter?: (
    v: any,
    dynamicSelects: any,
    rowData: any,
    column: Column
  ) => React.ReactNode;
  formatter?: (v: any, rowData: any, navigate?: any) => React.ReactNode;
  tableFormatter?: (
    v: any,
    rowData: any,
    dynamicSelects: any,
    column: Column
  ) => React.ReactNode;
  disableSort?: string;
  infoIcon?: boolean;
  access?: string;
  description?: string;
  copyable?: boolean;
  onClick?: (rowData: any) => void;
  itemOnClick?: (rowData: any) => void;
  linker?: (rowData: any) => string;
  keyAs?: string;
  delimiter?: string;
  getter?: (rowData: any) => any;
  options?: any[];
  width?: number;
  sticky?: 'left' | 'right';
  style?: SxProps;
  getWidth?: (data: {
    estimatedWidth: number;
    allRows: any[];
    dynamicSelectData: any[];
  }) => number;
};

export type FloatingActionsProps = {
  actionLoading: { [key: string]: boolean };
  onEdit?: (row: any) => void;
  actions?: {
    id: string;
    label: string;
    type: 'button' | 'iconButton' | 'icon' | 'custom';
    icon?: React.ReactNode;
    enabled?: (row: any) => boolean;
    onClick: (row: any, event: any) => void;
    getComponent?: (row: any) => React.ReactNode;
  }[];
  actionsEnabled: (row: any) => boolean;
  getSetActionLoadingByRowId: (rowId: string) => (loading: boolean) => void;
};

export type DynamicSelectData = {
  [table: string]: any;
};
