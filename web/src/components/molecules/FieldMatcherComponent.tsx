import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { FiltersOperators } from 'common/globalTypes';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import { z } from 'zod';
import { useState } from 'react';
import { FieldMatchertDateOperatorOptions } from 'common/globalTypes';
import { Box } from '@mui/material';

import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { FieldTypes } from '@/types';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { ExpandableTextField } from '@/components/molecules/ExpandableTextField';
import StringArrayFieldMatcher from '@/components/molecules/StringArrayFieldMatcher';

const numericSchema = z
  .string()
  .regex(/^-?\d+(\.\d+)?$/, 'Invalid numeric value');

// TODO: Extract the following components to their own files and create a field matcher directory for better organization
const NumericTextField = ({
  fieldMatcher,
  fieldsMatchers,
  i,
  setValue,
  adornment = '',
  adornmentPosition = 'end',
}: {
  fieldMatcher: any;
  fieldsMatchers: any[];
  i: number;
  setValue: (value: any[]) => void;
  adornment?: string;
  adornmentPosition?: 'start' | 'end';
}) => {
  const [error, setError] = useState<string | null>(null);

  return (
    <TextField
      label="Value"
      value={fieldMatcher.value}
      onChange={(e) => {
        const value = e.target.value;
        const result = numericSchema.safeParse(value);
        if (result.success) {
          setError(null);
          const newFieldMatchers = [...fieldsMatchers];
          newFieldMatchers[i].value = value;
          setValue(newFieldMatchers);
        } else {
          setError(result.error.errors[0].message);
        }
      }}
      error={!!error}
      helperText={error}
      InputProps={{
        [`${adornmentPosition}Adornment`]: (
          <InputAdornment position={adornmentPosition}>
            {adornment}
          </InputAdornment>
        ),
      }}
      sx={{ mr: 1 }}
    />
  );
};

const fieldTypeComponents = {
  [FieldTypes.DATE]: ({ fieldMatcher, fieldsMatchers, i, setValue }) => (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <BasicDatePicker
        sx={{ mr: 1 }}
        label="Value"
        value={fieldMatcher.value}
        setValue={(value) => {
          const newFieldMatchers = [...fieldsMatchers];
          newFieldMatchers[i].value = dayjs(value).utc().format('YYYY-MM-DD');
          setValue(newFieldMatchers);
        }}
      />
    </LocalizationProvider>
  ),
  [FieldTypes.SELECT]: ({
    field,
    fieldMatcher,
    fieldsMatchers,
    i,
    setValue,
    componentOptions,
  }) => (
    <EnhancedSelect
      label="Value"
      options={field?.options ?? []}
      multiple={componentOptions.multiple ? componentOptions.multiple : false}
      value={
        componentOptions.multiple
          ? (field?.options ?? []).filter((item) =>
              fieldMatcher.value?.includes(
                typeof item === 'string' ? item : item.id
              )
            )
          : (field?.options ?? []).find((item) => {
              if (typeof item === 'string') return item === fieldMatcher.value;
              return item.id === fieldMatcher.value;
            })
      }
      onChange={(v) => {
        const newFieldMatchers = [...fieldsMatchers];
        if (componentOptions.multiple) {
          newFieldMatchers[i].value = v.map((item) =>
            typeof item === 'string' ? item : item.id
          );
        } else {
          newFieldMatchers[i].value = typeof v === 'string' ? v : v.id;
        }
        setValue(newFieldMatchers);
      }}
      sx={{ mr: 1 }}
    />
  ),
  [FieldTypes.PERCENTAGE]: ({ fieldMatcher, fieldsMatchers, i, setValue }) => (
    <NumericTextField
      fieldMatcher={fieldMatcher}
      fieldsMatchers={fieldsMatchers}
      i={i}
      setValue={setValue}
      adornment="%"
    />
  ),
  [FieldTypes.CURRENCY]: ({ fieldMatcher, fieldsMatchers, i, setValue }) => (
    <NumericTextField
      fieldMatcher={fieldMatcher}
      fieldsMatchers={fieldsMatchers}
      i={i}
      setValue={setValue}
      adornment="$"
      adornmentPosition="start"
    />
  ),
  [FieldTypes.INTEGER]: ({ fieldMatcher, fieldsMatchers, i, setValue }) => (
    <NumericTextField
      fieldMatcher={fieldMatcher}
      fieldsMatchers={fieldsMatchers}
      i={i}
      setValue={setValue}
    />
  ),
  [FieldTypes.STRING_ARRAY]: StringArrayFieldMatcher,
  // Add more field types and their corresponding components here
};

const DefaultFieldComponent = ({
  fieldMatcher,
  fieldsMatchers,
  i,
  setValue,
}) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <ExpandableTextField
        label="Values"
        defaultValue={fieldMatcher.value}
        onChange={(value) => {
          const newFieldMatchers = [...fieldsMatchers];
          newFieldMatchers[i].value = value;

          setValue(newFieldMatchers);
        }}
        sx={{ maxWidth: 320 }}
      />
    </Box>
  );
};

const dateFilters = [
  FiltersOperators.WITHIN,
  FiltersOperators.AFTER,
  FiltersOperators.BEFORE_EQUALS,
  FiltersOperators.AFTER_EQUALS,
  FiltersOperators.BEFORE,
];

// TODO: Define prop types for FieldMatcherComponent
const FieldMatcherComponent = ({
  fields,
  fieldMatcher,
  fieldsMatchers,
  i,
  setValue,
}) => {
  const field = fields.find((f) => f.id === fieldMatcher?.field);
  let FieldComponent =
    fieldTypeComponents[field?.type] || DefaultFieldComponent;

  // TODO: We need to deprecate withinOneYear and atLeastOneYear operators
  if (
    fieldMatcher?.op === FiltersOperators.WITHIN_ONE_YEAR ||
    fieldMatcher?.op === FiltersOperators.AT_LEAST_ONE_YEAR
  ) {
    FieldComponent = fieldTypeComponents[FieldTypes.SELECT];
  }

  if (
    dateFilters.includes(fieldMatcher?.op) &&
    fieldMatcher?.from === FieldMatchertDateOperatorOptions.FromDateField
  ) {
    FieldComponent = fieldTypeComponents[FieldTypes.SELECT];
  }

  const componentOptions = {
    multiple: false,
  };

  if (
    fieldMatcher?.op === FiltersOperators.CONTAINEDIN ||
    fieldMatcher?.op === FiltersOperators.NCONTAINEDIN
  ) {
    componentOptions.multiple = true;
  }

  if (
    fieldMatcher?.op === FiltersOperators.CONTAINS ||
    fieldMatcher?.op === FiltersOperators.NCONTAINS
  ) {
    FieldComponent = fieldTypeComponents[FieldTypes.STRING_ARRAY];
  }

  if (
    fieldMatcher?.op === FiltersOperators.IS_EMPTY ||
    fieldMatcher?.op === FiltersOperators.IS_NOT_EMPTY
  ) {
    return <></>;
  }

  return (
    <FieldComponent
      field={field}
      fieldMatcher={fieldMatcher}
      fieldsMatchers={fieldsMatchers}
      i={i}
      setValue={setValue}
      componentOptions={componentOptions}
    />
  );
};

export default FieldMatcherComponent;
