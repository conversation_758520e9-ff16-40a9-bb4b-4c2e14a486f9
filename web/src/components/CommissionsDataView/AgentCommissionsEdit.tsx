import { Close, Close as CloseIcon, InfoOutlined } from '@mui/icons-material';
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Skeleton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  CustomMethodConditionType,
  CustomMethodOperatorOptions,
} from 'common/interface';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Formatter from 'common/Formatter';

import { CONDITION_TYPE_OPTIONS } from '@/components/contacts/ContactsView/CompProfilesAdd';
import { useAccountStore } from '@/store';
import { AgentData, AgentSelect } from './AgentSelect';

type AgentCommissionsEditProps = {
  data: {
    agent_commissions: Record<string, number>;
    agent_commissions_v2?: Record<string, number>;
    agent_payout_rate?: Record<string, number>;
    agent_payout_rate_override?: Record<string, number>;
    agent_commission_payout_rate?: Record<string, number>;
    contacts: string[];
    comp_calc?: Record<string, number>;
  };
  disableConditions?: boolean;
  setter: (fn: (prevData: any) => any) => void;
  field: any;
  dynamicSelectsData: any;
  readOnly?: boolean;
  showOverrideMode?: boolean;
  percentageFormat?: 'decimal' | 'percentage';
};

// Add new type for conditions
type Condition = {
  type: string;
  value: string;
  operator: string;
};

const AgentCommissionsEdit: React.FC<AgentCommissionsEditProps> = ({
  data,
  field,
  setter,
  dynamicSelectsData,
  readOnly = false,
  disableConditions = true,
  showOverrideMode = false,
  percentageFormat = 'decimal',
}) => {
  const dynamicSelects = dynamicSelectsData?.contacts || [];
  const [selectedOptions, setSelectedOptions] = useState<AgentData[]>([]);
  const idToUse = useMemo(() => {
    return ['comp_calc', 'comp_calc_status'].includes(field.id)
      ? 'id'
      : 'str_id';
  }, [field.id]);

  const units = useMemo(() => {
    return [
      'agent_payout_rate',
      'agent_payout_rate_override',
      'agent_commission_payout_rate',
    ].includes(field.id)
      ? 'rate'
      : 'amount';
  }, [field.id]);

  const agentDataToUse = useMemo(() => {
    let dataToUse: Record<string, number> = {};

    const {
      agent_commissions: agentCommissions,
      agent_commissions_v2: agentCommissionsV2,
      agent_payout_rate: agentPayoutRate,
      agent_payout_rate_override: agentPayoutRateOverride,
      agent_commission_payout_rate: agentCommissionPayoutRate,
      comp_calc: compCalc,
    } = data || {};

    switch (field.id) {
      case 'agent_commissions':
        dataToUse = agentCommissions || {};
        break;
      case 'agent_commissions_v2':
        dataToUse = agentCommissionsV2 || {};
        break;
      case 'agent_payout_rate':
        dataToUse = agentPayoutRate || {};
        break;
      case 'agent_payout_rate_override':
        dataToUse = agentPayoutRateOverride || {};
        break;
      case 'agent_commission_payout_rate':
        dataToUse = agentCommissionPayoutRate || {};
        break;
      case 'comp_calc':
        dataToUse = compCalc || {};
        break;
      default:
        dataToUse = {};
    }

    return dataToUse;
  }, [data, field.id]);

  const { selectedAccount } = useAccountStore();
  const [renderedValues, setRenderedValues] = React.useState({});
  const [dataSetted, setDataSetted] = React.useState(false);

  const [showConditions] = React.useState(!disableConditions);

  const updateAgentData = (agentId: string, value: string) => {
    setter((prevData) => {
      const newAgentCommissions = { ...prevData[field.id] };
      newAgentCommissions[agentId] = value;

      const { total: _total, ...valuesWithoutTotal } = newAgentCommissions;

      if (units === 'amount') {
        const newTotal = (Object.values(valuesWithoutTotal) as string[]).reduce(
          (sum: number, current: string) =>
            sum + (Number.isNaN(+current) ? 0 : +current),
          0
        );
        newAgentCommissions['total'] = newTotal;
      }

      if (units === 'rate') {
        const newTotal = (Object.values(valuesWithoutTotal) as string[]).reduce(
          (sum: number, current: string) =>
            sum + (Number.isNaN(+current) ? 0 : +current),
          0
        );
        newAgentCommissions['total'] = newTotal;
      }

      return {
        ...prevData,
        [field.id]: newAgentCommissions,
      };
    });
  };

  const formatAgentCommissionsData = useCallback(
    (v: number | string) => {
      if (units === 'amount') {
        return Formatter.currency(v, { withoutSymbol: true });
      }
      if (percentageFormat === 'percentage') {
        return Formatter.percentage(v, {
          isPercentage: true,
          addPrecisionForRateLowerThanTen: true,
          withoutSymbol: true,
        });
      }

      return Formatter.percentage(v, {
        isPercentage: false,
        addPrecisionForRateLowerThanTen: true,
        withoutSymbol: false,
      });
    },
    [units, percentageFormat]
  );

  const removeAgentData = (agentId: string) => {
    setter((prevData) => {
      const newAgentCommissions = { ...prevData[field.id] };
      delete newAgentCommissions[agentId];
      delete newAgentCommissions.config?.[agentId];

      const { total: _total, ...commissionsWithoutTotal } = newAgentCommissions;

      if (units === 'amount') {
        const newTotal = (
          Object.values(commissionsWithoutTotal) as number[]
        ).reduce((sum: number, current: number) => sum + current, 0);
        newAgentCommissions['total'] = newTotal;
      }

      return {
        ...prevData,
        [field.id]: newAgentCommissions,
      };
    });
  };

  // Update the handler for adding conditions
  const handleAddCondition = (agentId: string) => {
    setter((prevData) => {
      const newData = { ...prevData };
      const fieldData = newData[field.id] || {};

      // Initialize or update the config structure
      if (!fieldData.config) {
        fieldData.config = {};
      }

      if (!fieldData.config[agentId]) {
        fieldData.config[agentId] = { conditions: [] };
      }

      if (!fieldData.config[agentId].conditions) {
        fieldData.config[agentId].conditions = [];
      }

      // Add new empty condition
      fieldData.config[agentId].conditions.push({});

      newData[field.id] = fieldData;
      return newData;
    });
  };

  const updateCondition = (
    agentId: string,
    index: number,
    fieldId: keyof Condition,
    value: string
  ) => {
    setter((prevData) => {
      const newData = { ...prevData };
      const fieldData = newData[field.id];

      if (!fieldData.config?.[agentId]?.conditions?.[index]) {
        return prevData;
      }

      fieldData.config[agentId].conditions[index] = {
        ...fieldData.config[agentId].conditions[index],
        [fieldId]: value,
      };

      return newData;
    });
  };

  const handleRemoveCondition = (agentId: string, index: number) => {
    setter((prevData) => {
      const newData = { ...prevData };
      const fieldData = newData[field.id] || {};

      if (fieldData.config?.[agentId]?.conditions) {
        fieldData.config[agentId].conditions.splice(index, 1);
      }

      newData[field.id] = fieldData;
      return newData;
    });
  };

  const onChange = (v: AgentData[]) => {
    setSelectedOptions(v);

    v.forEach((item) => {
      if (!agentDataToUse[item[idToUse]]) {
        // TODO: refactor this to not use '0', easy to cause bugs
        updateAgentData(item[idToUse], '0');
      }
    });

    Object.keys(agentDataToUse).forEach((key) => {
      if (!v.find((item) => item[idToUse] === key)) {
        removeAgentData(key);
      }
    });
  };

  const initializeFormattedDisplayValues = useCallback(() => {
    const hasAgentData =
      agentDataToUse && Object.keys(agentDataToUse).length > 0;
    const isFirstInitialization = !dataSetted;

    if (!hasAgentData || !isFirstInitialization) return;

    const initialFormattedValues = {};

    Object.entries(agentDataToUse)
      .filter(([key, _value]) => key !== 'total' && key !== 'config')
      .forEach(([agentId, rawValue]) => {
        initialFormattedValues[agentId] = formatAgentCommissionsData(rawValue);
      });

    setRenderedValues(initialFormattedValues);
    setDataSetted(true);
  }, [agentDataToUse, dataSetted, formatAgentCommissionsData]);

  useEffect(() => {
    initializeFormattedDisplayValues();
  }, [initializeFormattedDisplayValues]);

  const options = [...dynamicSelects, ...selectedOptions];

  return (
    <Box>
      <Typography sx={{ mb: 1 }} variant="subtitle1">
        {field.label}
      </Typography>
      {!readOnly && (
        <Box display="flex" justifyContent="space-between" position="relative">
          <AgentSelect
            agentDataToUse={agentDataToUse}
            initialOptions={dynamicSelects}
            idToUse={idToUse}
            onChange={onChange}
          />
        </Box>
      )}

      <Box
        sx={{
          mt: 0.5,
          p: 0.5,
          display: Object.keys(agentDataToUse).filter(
            (item) => item !== 'config'
          ).length
            ? 'flex'
            : 'none',
          flexDirection: 'column',
          borderStyle: 'solid',
          borderColor: 'silver',
          borderWidth: 1,
          borderRadius: 4,
          width: '100%',
          backgroundColor: '#2196f308',
        }}
      >
        <Box sx={{ my: 0.5, display: 'flex', flexWrap: 'wrap' }}>
          {agentDataToUse &&
            Object.entries(agentDataToUse)
              .filter(([k, v]) => k !== 'total' && k !== 'config')
              .map(([k, v]) => {
                const contact = options?.find(
                  (e) => e?.[idToUse] === (idToUse === 'id' ? +k : k)
                ) ?? { id: undefined };
                return contact.id ? (
                  <Box
                    key={`${contact.id}`}
                    sx={{
                      m: 0.5,
                      p: 1,
                      borderStyle: 'solid',
                      borderColor: 'silver',
                      borderWidth: 1,
                      borderRadius: 4,
                      display: 'inline-block',
                      minWidth: 180,
                      backgroundColor: '#2196f30a',
                    }}
                  >
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      position="relative"
                      sx={{ ml: 0.5 }}
                    >
                      <Typography variant="body2">
                        {Formatter.contact(contact, {
                          account_id: selectedAccount?.accountId,
                        })}
                      </Typography>
                      {!readOnly && (
                        <IconButton
                          onClick={() => {
                            removeAgentData(contact[idToUse]);
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                      )}
                    </Box>
                    {showOverrideMode && !readOnly && (
                      <Box sx={{ mb: 1 }}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Override mode</InputLabel>
                          <Select
                            label="Override mode"
                            value={
                              agentDataToUse?.config?.[contact[idToUse]]
                                ?.overrideMode ?? 'replace'
                            }
                            onChange={(e) => {
                              setter((prevData) => {
                                const newData = { ...prevData };
                                const fieldData = newData[field.id] || {};

                                if (!fieldData.config) {
                                  fieldData.config = {};
                                }

                                if (!fieldData.config[contact[idToUse]]) {
                                  fieldData.config[contact[idToUse]] = {};
                                }

                                fieldData.config[
                                  contact[idToUse]
                                ].overrideMode = e.target.value;

                                newData[field.id] = fieldData;
                                return newData;
                              });
                            }}
                          >
                            <MenuItem value="replace">
                              Replace existing calculation method
                            </MenuItem>
                            <MenuItem value="add">
                              Add to existing calculation method
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                    )}
                    <Box
                      display="flex"
                      justifyContent="flex-start"
                      flexDirection="row"
                      alignItems="center"
                      gap={1}
                    >
                      <TextField
                        label={`Commission ${units}`}
                        variant="outlined"
                        value={renderedValues[k]}
                        onChange={(e) => {
                          setRenderedValues((prevRendered) => ({
                            ...prevRendered,
                            [k]: e.target.value,
                          }));
                          updateAgentData(contact[idToUse], e.target.value);
                        }}
                        InputProps={{
                          startAdornment:
                            units === 'amount' ? (
                              <InputAdornment position="start" sx={{ ml: 0 }}>
                                $
                              </InputAdornment>
                            ) : null,
                          endAdornment:
                            units === 'rate' ? (
                              <InputAdornment position="end" sx={{ ml: 0 }}>
                                %
                              </InputAdornment>
                            ) : null,
                        }}
                        onBlur={(e) => {
                          setRenderedValues((prevRendered) => ({
                            ...prevRendered,
                            [k]: formatAgentCommissionsData(prevRendered[k]),
                          }));
                          setter((prevData) => {
                            const newData = { ...prevData };
                            const fieldData = { ...newData[field.id] };
                            const { config, ...rest } = fieldData;

                            // Convert all non-config values to numbers
                            Object.entries(rest).forEach(([k, v]) => {
                              const amount: string = v as any;
                              fieldData[k] = Number.isNaN(+amount)
                                ? 0
                                : +amount;
                            });

                            // Preserve the config
                            if (config) {
                              fieldData.config = config;
                            }

                            newData[field.id] = fieldData;
                            return newData;
                          });
                        }}
                        sx={{ my: 0.5 }}
                        disabled={readOnly}
                      />
                      {field.id === 'agent_commission_payout_rate' && (
                        <Tooltip title="The payout rate is based on total commission. Overriding it won’t change the agent’s commission amount automatically.">
                          <InfoOutlined
                            sx={{
                              color: 'grey',
                            }}
                          />
                        </Tooltip>
                      )}
                    </Box>
                    {showConditions && (
                      <Box sx={{ mt: 0.25, mx: 1 }}>
                        {agentDataToUse?.config?.[
                          contact[idToUse]
                        ]?.conditions?.map(
                          (condition: Condition, index: number) => (
                            <Box
                              key={index}
                              sx={{
                                display: 'flex',
                                gap: 1,
                                mt: 0.5,
                                mx: 1,
                                alignItems: 'center',
                              }}
                            >
                              <FormControl sx={{ width: '50%' }}>
                                <InputLabel>Condition type</InputLabel>
                                <Select
                                  value={condition.type ?? ''}
                                  label="Condition type"
                                  onChange={(e) =>
                                    updateCondition(
                                      contact[idToUse],
                                      index,
                                      'type',
                                      e.target.value
                                    )
                                  }
                                >
                                  {CONDITION_TYPE_OPTIONS.filter(
                                    (condition) =>
                                      condition.value ===
                                      CustomMethodConditionType.COMPENSATION_TYPE
                                  ).map((option) => (
                                    <MenuItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                              <FormControl sx={{ width: '50%', mx: 1 }}>
                                <InputLabel>Operator</InputLabel>
                                <Select
                                  key={`operator-${condition.type}-${index}`}
                                  value={condition.operator ?? ''}
                                  label="Operator"
                                  onChange={(e) =>
                                    updateCondition(
                                      contact[idToUse],
                                      index,
                                      'operator',
                                      e.target.value
                                    )
                                  }
                                >
                                  {CustomMethodOperatorOptions.map((option) => (
                                    <MenuItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                              <TextField
                                sx={{ width: '50%' }}
                                label={
                                  CONDITION_TYPE_OPTIONS.find(
                                    (opt) => opt.value === condition.type
                                  )?.label ?? 'Amount'
                                }
                                value={condition.value ?? ''}
                                onChange={(e) =>
                                  updateCondition(
                                    contact[idToUse],
                                    index,
                                    'value',
                                    e.target.value
                                  )
                                }
                              />
                              <IconButton
                                size="small"
                                onClick={() =>
                                  handleRemoveCondition(contact[idToUse], index)
                                }
                              >
                                <Close />
                              </IconButton>
                            </Box>
                          )
                        )}
                        <Button
                          onClick={() => handleAddCondition(contact[idToUse])}
                          disabled={readOnly}
                        >
                          Add condition
                        </Button>
                      </Box>
                    )}
                  </Box>
                ) : (
                  <Skeleton key={k} />
                );
              })}

          {units === 'amount' && (
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              position="relative"
              sx={{ mx: 0.5 }}
            >
              <TextField
                label="Total"
                variant="outlined"
                value={Formatter.currency(
                  agentDataToUse?.total ? agentDataToUse.total : 0
                )}
                disabled
                sx={{ my: 0.5 }}
              />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AgentCommissionsEdit;
