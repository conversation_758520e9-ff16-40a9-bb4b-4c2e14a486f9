import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import {
  DateRangeOutlined,
  Launch,
  Share,
  Verified,
  PriceCheck,
} from '@mui/icons-material';
import { Button, Checkbox, FormControlLabel, Tooltip } from '@mui/material';
import { capitalize } from 'lodash-es';
import { useEffect, useState } from 'react';
import CommonFormatter from 'common/Formatter';
import {
  ConflictingReportData,
  ContactPayableStatuses,
  SavedReportStatuses,
  SavedReportStatusesLabels,
  SavedReportStatusOptions,
} from 'common/globalTypes';
import {
  AGENT_COMMISSION_THIS_PERIOD_COLUMN,
  TOTAL_COMMISSION_PAYMENT_COLUMN,
  TOTAL_COMMISSION_COLUMN,
  TOTAL_PREMIUM_COLUMN,
} from 'common/constants/excel-export';
import { ExportOptions } from 'common/exports';

import DataView from '@/components/DataView';
import API from '@/services/API';
import DataTransformation from '@/services/DataTransformation';
import Formatter from '@/services/Formatter';
import { useAccountStore } from '@/store';
import { savedReportsGroupsTemplates } from '@/types';
import CommissionPayoutExportConfig from '@/components/ReportsGroupView/CommissionPayoutExportConfig';
import useSnackbar from '@/contexts/useSnackbar';
import ApproveReportDialog from '@/components/ReportsGroupView/ApproveReportDialog';
import PayReportDialog from './PayReportDialog';

const Normalizer = DataTransformation;

export interface Report {
  id: number;
  str_id: string;
  name: string;
  processing_date_start: string;
  processing_date_end: string;
  status: SavedReportStatuses;
  totals: {
    fees: number | null;
    agent_commissions: Record<string, number>;
    commission_amount: number;
    commission_paid_amount: number | null;
    customer_paid_premium_amount: number | null;
    commissionable_premium_amount: number | null;
  };
  agent_balance: number;
  current_balance: number;
  report_balance: number;
  report_notes: string | null;
  agent_name: string;
  agent_email: string;
  agent_status: string | null;
  agent_payable_status: string | null;
  agent_bank_info: string | null;
  agent: {
    id: number;
    str_id: string;
    first_name: string;
    last_name: string;
    email: string;
    status: string | null;
    payable_status: string | null;
    user_str_id: string;
  };
}

export interface DataToApprove {
  reportId: number;
  reportName: string;
  contactStrId: string;
  includesPaidCommission: boolean;
  hasConflictingReport: boolean;
  conflictingReportsData: ConflictingReportData[];
}

export interface DataToPay {
  reportStrId: string;
  reportName: string;
  reportStatus: SavedReportStatuses;
  agentName: string;
}

const ReportsGroupDetailsView = (): JSX.Element => {
  const { id } = useParams();

  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedData, setSelectedData] = useState<Report[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [refresh, setRefresh] = useState(0);
  const [exportOptions, setExportOptions] = useState<ExportOptions | null>(
    null
  );
  const [openApproveDialog, setOpenApproveDialog] = useState(false);
  const [openPayDialog, setOpenPayDialog] = useState(false);
  const [approvalLoading, setApprovalLoading] = useState(false);
  const [dataToApprove, setDataToApprove] = useState<DataToApprove[]>([]);
  const [allSelectedReportsApproved, setAllSelectedReportsApproved] =
    useState(false);
  const [allSelectedReportsDraft, setAllSelectedReportsDraft] = useState(false);

  useEffect(() => {
    setAllSelectedReportsApproved(
      selectedData.every(
        (report) => report.status === SavedReportStatuses.APPROVED
      )
    );

    setAllSelectedReportsDraft(
      selectedData.every(
        (report) => report.status === SavedReportStatuses.DRAFT
      )
    );
  }, [selectedData]);

  const handleCloseApproveDialog = () => {
    setOpenApproveDialog(false);
  };

  const navigate = useNavigate();

  const bulkSharePoster = API.getMutation(
    'saved_reports/groups/bulk_share',
    'POST'
  );
  const approveReportsPoster = API.getMutation(
    'saved_reports/groups/approve-comp-report',
    'POST'
  );
  const approveReportsPreviewPoster = API.getMutation(
    'saved_reports/groups/approve-comp-report-preview',
    'POST'
  );
  const { data: contactOptions, isFetched: isFetchedContactOptions } =
    API.getBasicQuery(`contacts/options?saved_report_group=${id}`);
  const { data: savedReportResponse } = API.getBasicQuery(
    `saved_reports/groups?id=${id}`
  );
  const reportGroupPatcher = API.getMutation(
    'saved_reports/groups/details',
    'PATCH'
  );
  const reportGroupDeleter = API.getMutation(
    'saved_reports/groups/details',
    'DELETE'
  );

  const setPayedReportsPoster = API.getMutation(
    'saved_reports/groups/set-report-as-paid',
    'POST'
  );

  const savedReport = savedReportResponse?.data;

  const { selectedAccount } = useAccountStore();
  const { showSnackbar } = useSnackbar();

  const isAccountTransactionsEnabled =
    selectedAccount?.accountingTransactionsEnabled;

  // TODO: Currently getting dates from report name. These should be saved as params in the saved report for this type.
  let processing_date_start = '';
  let processing_date_end = '';
  if (Array.isArray(savedReport) && savedReport.length === 1) {
    const report = savedReport[0];
    const processingDates = report?.name?.match(/\d\d?\/\d\d?\/\d{4}/g);
    if (processingDates) {
      [processing_date_start, processing_date_end] = processingDates;
    }
  }

  const payReportsHandler = async (reportStrIds) => {
    try {
      const response = await setPayedReportsPoster.mutateAsync({
        report_str_ids: reportStrIds,
      });
      if (response?.data) {
        setRefresh(refresh + 1);
        showSnackbar('Reports marked as paid successfully', 'success');
      } else {
        showSnackbar('An error ocurred when setting reports as paid.', 'error');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
  };

  const approveReportsHandler = async (previewOnly: boolean) => {
    setApprovalLoading(true);
    try {
      let response;
      if (previewOnly)
        response = await approveReportsPreviewPoster.mutateAsync({
          preview_only: previewOnly,
          report_str_ids: selectedData.map((report) => report.str_id),
        });
      else
        response = await approveReportsPoster.mutateAsync({
          preview_only: previewOnly,
          report_str_ids: selectedData.map((report) => report.str_id),
        });

      if (response?.data) {
        if (previewOnly) setDataToApprove(response.data);
        else {
          setRefresh(refresh + 1);
          showSnackbar('Reports approved successfully', 'success');
        }
        setApprovalLoading(false);
        return response.data;
      } else {
        showSnackbar('An error ocurred when approving reports.', 'error');
        setApprovalLoading(false);
        return null;
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
      setApprovalLoading(false);
      return null;
    }
  };

  const extraActions = [
    ...(processing_date_start && processing_date_end
      ? [
          <FormControlLabel
            control={
              <Checkbox checked={searchParams.get('hide_empty') === 'true'} />
            }
            label="Hide empty"
            sx={{ mr: 1 }}
            onChange={(e: any) => {
              setSearchParams((prev: any) => {
                if (e.target.checked) {
                  prev.set('hide_empty', 'true');
                } else {
                  prev.delete('hide_empty');
                }
                return prev;
              });
            }}
            key="emptyFilter"
          />,
          <Button
            key={`${processing_date_end}-${processing_date_start}-key`}
            href={`/commissions/?processing_date_start=${processing_date_start}&processing_date_end=${processing_date_end}`}
            variant="outlined"
            startIcon={<DateRangeOutlined />}
          >
            View current data
          </Button>,
        ]
      : []),
    <Tooltip key={`shareReportsButtonKey`} title="Share report with producer">
      <span>
        <Button
          onClick={async () => {
            try {
              const response = await bulkSharePoster.mutateAsync(selectedData);
              if (response && response.statusText === 'ok') {
                showSnackbar('Reports shared successfully', 'success');
              } else if (response && response.error) {
                showSnackbar(response.error, 'error');
              } else {
                showSnackbar('Error sharing reports', 'error');
              }
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : JSON.stringify(error);
              showSnackbar(errorMessage, 'error');
            }
          }}
          variant="outlined"
          startIcon={<Share />}
          disabled={selectedData.length === 0}
          sx={{ ml: 1 }}
        >
          Share reports
        </Button>
      </span>
    </Tooltip>,
    <Tooltip
      key={`approveReportsButtonKey`}
      title={
        allSelectedReportsDraft
          ? 'Approve selected reports'
          : 'All selected reports must be in draft status'
      }
    >
      <span>
        <Button
          onClick={async () => {
            setOpenApproveDialog(true);

            if (selectedData.length === 0) {
              showSnackbar('No reports selected', 'error');
              return;
            }
            if (allSelectedReportsApproved) {
              showSnackbar('Selected reports are already approved', 'error');
              return;
            }

            await approveReportsHandler(true);
          }}
          variant="outlined"
          startIcon={<Verified />}
          disabled={selectedData.length === 0 || !allSelectedReportsDraft}
          sx={{ ml: 1 }}
        >
          Approve reports
        </Button>
      </span>
    </Tooltip>,
    <Tooltip
      key={`payReportsButtonKey`}
      title={
        allSelectedReportsApproved
          ? 'Set selected reports as paid'
          : 'All selected reports must be approved'
      }
    >
      <span>
        <Button
          onClick={async () => {
            setOpenPayDialog(true);

            if (selectedData.length === 0) {
              showSnackbar('No reports selected', 'error');
              return;
            }
          }}
          variant="outlined"
          startIcon={<PriceCheck />}
          disabled={selectedData.length === 0 || !allSelectedReportsApproved}
          sx={{ ml: 1 }}
        >
          Mark as paid
        </Button>
      </span>
    </Tooltip>,
  ];

  const dataDesc = {
    label: 'Report group summary',
    table: `saved_reports/groups/details?id=${id}`,
    fields: [
      {
        id: 'name',
        label: 'Report name',
        tableFormatter: (field, row) => {
          return (
            <Button
              onClick={() => {
                window.open(`/reports/${row.str_id}`, '_blank');
              }}
              endIcon={<Launch />}
            >
              {field}
            </Button>
          );
        },
      },
      {
        id: 'processing_date_start',
        label: 'Start date',
        formatter: (val) => {
          if (val) {
            return CommonFormatter.date(val);
          }
          return '';
        },
      },
      {
        id: 'processing_date_end',
        label: 'End date',
        formatter: (val) => {
          if (val) {
            return CommonFormatter.date(val);
          }
          return '';
        },
      },
      {
        id: 'totals',
        label: AGENT_COMMISSION_THIS_PERIOD_COLUMN,
        formatter: (val: Record<string, number>, item: Report) => {
          if (val.agent_commissions)
            return CommonFormatter.currency(
              Normalizer.normalizeCurrency(
                JSON.stringify(
                  val.agent_commissions?.[item?.agent?.str_id] ??
                    Object.values(val.agent_commissions)?.[0] ??
                    0
                )
              )
            );
        },
      },
      {
        id: 'totals2',
        label: TOTAL_COMMISSION_COLUMN,
        formatter: (val) => {
          if (val.commission_amount)
            return CommonFormatter.currency(val.commission_amount ?? 0);
        },
      },
      {
        id: 'totals2',
        label: TOTAL_PREMIUM_COLUMN,
        formatter: (val) => {
          if (val.premium_amount)
            return CommonFormatter.currency(val.premium_amount ?? 0);
        },
      },
      ...(isAccountTransactionsEnabled
        ? [
            {
              id: 'agent_balance',
              label: 'Agent balance',
              formatter: (val) => {
                if (val) return CommonFormatter.currency(val);
              },
            },
            {
              id: 'current_balance',
              label: TOTAL_COMMISSION_PAYMENT_COLUMN,
              formatter: (val) => {
                if (val) return CommonFormatter.currency(val);
              },
            },
          ]
        : []),
      {
        id: 'report_notes',
        label: 'Notes',
        bulkEdit: true,
      },
      {
        id: 'status',
        label: 'Payout status',
        type: 'select',
        options: SavedReportStatusOptions.map((status) => ({
          id: status.value,
          label: status.label,
        })),
        formatter: (val) =>
          Formatter.statusChip(val, {
            mapping: {
              [SavedReportStatusesLabels[SavedReportStatuses.APPROVED]]: 'blue',
              [SavedReportStatusesLabels[SavedReportStatuses.PAID]]: 'green',
              [SavedReportStatusesLabels[SavedReportStatuses.DRAFT]]: '',
            },
          }),
      },
      {
        id: 'agent_name',
        label: 'Agent',
        bulkEdit: true,
      },
      {
        id: 'agent_email',
        label: 'Agent email',
      },
      {
        id: 'agent_status',
        label: 'Agent status',
        queryable: true,
        condition: () => isFetchedContactOptions,
        formatter: (val) => capitalize(val),
      },
      {
        id: 'agent_payable_status',
        label: 'Payable status',
        queryable: true,
        type: 'select',
        options: [
          ...new Set([
            '',
            ContactPayableStatuses.PAYABLE,
            ContactPayableStatuses.NON_PAYABLE,
            ...(contactOptions?.agent_payable_status ?? []),
          ]),
        ],
        condition: () => isFetchedContactOptions,
        formatter: (val) => capitalize(val),
      },
      {
        id: 'agent_bank_info',
        label: 'Agent bank info',
      },
      {
        id: 'agent',
        readOnly: true,
        visible: false,
      },
    ],
    filterConfigs: {
      agent_status: { type: 'select', label: 'Agent status', options: {} },
      agent_payable_status: {
        type: 'select',
        label: 'Agent payable status',
        options: {},
      },
    },
  };

  if (contactOptions) {
    contactOptions.status.forEach((option) => {
      dataDesc.filterConfigs.agent_status.options[option] = {
        id: option,
        label: capitalize(option),
        query: { agent_status: option },
      };
    });
    contactOptions.payable_status.forEach((option) => {
      dataDesc.filterConfigs.agent_payable_status.options[option] = {
        id: option,
        label: capitalize(option),
        query: { agent_payable_status: option },
      };
    });
  }

  const formattedDate = savedReport?.[0]?.created_at
    ? CommonFormatter.date(savedReport[0]?.created_at)
    : '';

  const defaultExportOptions: {
    id: string;
    label: string;
    options: ExportOptions;
  }[] = [
    {
      id: 'export',
      label: 'Export',
      options: {
        export_type: 'zip',
        fileName:
          savedReport && Array.isArray(savedReport) && savedReport.length > 0
            ? savedReport[0]?.template ===
              savedReportsGroupsTemplates.COMMISSION_PAYOUT
              ? `${selectedAccount?.accountName} - ${formattedDate}`
              : savedReport[0]?.name
            : '',
        date_suffix: formattedDate,
      },
    },
    {
      id: 'export-zip',
      label: 'Export reports as zip',
      options: {
        export_type: 'zip',
        fileName:
          savedReport && Array.isArray(savedReport) && savedReport.length > 0
            ? savedReport[0]?.template ===
              savedReportsGroupsTemplates.COMMISSION_PAYOUT
              ? `${selectedAccount?.accountName} - ${formattedDate}`
              : savedReport[0]?.name
            : '',
        date_suffix: formattedDate,
      },
    },
    {
      id: 'export-xlsx',
      label: 'Export reports as xlsx',
      options: {
        export_type: 'xlsx',
        fileName:
          savedReport && Array.isArray(savedReport) && savedReport.length > 0
            ? savedReport[0]?.template ===
              savedReportsGroupsTemplates.COMMISSION_PAYOUT
              ? `${selectedAccount?.accountName} - ${formattedDate}`
              : savedReport[0]?.name
            : '',
        date_suffix: formattedDate,
      },
    },
  ];

  const handleCustomExportOptions = (onExport) => {
    if (exportOptions && Object.keys(exportOptions).length > 0) {
      onExport({ ...exportOptions });
      setExportOptions(null);
    }
  };

  const handleBulkDelete = async (selected) => {
    try {
      const body = {
        saved_reports_ids: selected,
        report_group_str_id: id,
      };
      const response = await reportGroupDeleter.mutateAsync(body);
      if (response && response.statusText === 'ok') {
        showSnackbar('Reports deleted', 'success');
        if (response.refresh) {
          setRefresh(refresh + 1);
        } else {
          navigate('/reports/summary');
        }
      } else if (response && response.error) {
        showSnackbar(response.error, 'error');
      } else {
        showSnackbar('Error deleting reports', 'error');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
  };

  const handleBulkEdit = async (selected, updateData) => {
    try {
      const body = {
        saved_reports_ids: selected,
        ...updateData,
      };
      const response = await reportGroupPatcher.mutateAsync(body);
      if (response && response.statusText === 'ok') {
        showSnackbar('Reports updated successfully', 'success');
        setRefresh(refresh + 1);
      } else if (response && response.error) {
        showSnackbar(response.error, 'error');
      } else {
        showSnackbar('Error updating reports', 'error');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
  };

  return (
    <>
      <DataView
        refresh={refresh}
        enableAutoRefetch={true}
        dataDesc={dataDesc}
        setSelectedData={setSelectedData}
        onDelete={async (selected) => {
          await handleBulkDelete(selected);
        }}
        onBulkEdit={async (selected, updateData) => {
          await handleBulkEdit(selected, updateData);
        }}
        exportOptions={defaultExportOptions}
        viewOnly
        extraActions={extraActions}
        handleCustomExportOptions={handleCustomExportOptions}
        {...(savedReport &&
        Array.isArray(savedReport) &&
        savedReport.length > 0 &&
        savedReport[0]?.template ===
          savedReportsGroupsTemplates.COMMISSION_PAYOUT
          ? {
              customExport: true,
              customExportCallback: () => setShowModal(true),
            }
          : {})}
        headingOffset={122}
        enablePagination
      />
      <ApproveReportDialog
        open={openApproveDialog}
        onClose={handleCloseApproveDialog}
        dataToApprove={dataToApprove}
        loading={approvalLoading}
        approveReportsHandler={approveReportsHandler}
        showSnackbar={showSnackbar}
      />
      <PayReportDialog
        open={openPayDialog}
        onClose={() => setOpenPayDialog(false)}
        dataToPay={selectedData.map((report) => ({
          reportStrId: report.str_id,
          reportName: report.name,
          reportStatus: report.status,
          agentName: report.agent_name,
        }))}
        payReportsHandler={payReportsHandler}
      />
      <CommissionPayoutExportConfig
        open={showModal}
        setOpen={setShowModal}
        setExportOptions={setExportOptions}
        savedReport={savedReport}
        selectedAccount={selectedAccount}
        selectedData={selectedData}
      />
    </>
  );
};

export default ReportsGroupDetailsView;
