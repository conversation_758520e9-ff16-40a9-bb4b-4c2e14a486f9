import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  IconButton,
  Box,
  Typography,
  TextField,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Close,
  CloudUpload,
  PictureAsPdf,
  Article,
  InsertDriveFile,
} from '@mui/icons-material';
import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Autocomplete } from '@mui/material';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import { UploadReportModalProps, UploadFile } from '../types';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';

const UploadReportModal = ({
  open,
  handleClose,
  onUploadSuccess,
}: UploadReportModalProps & { onUploadSuccess?: () => void }) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [reportType, setReportType] = useState<any | null>(null);
  const [reportName, setReportName] = useState('');
  const [description, setDescription] = useState('');
  const [reportTypes, setReportTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [access, setAccess] = useState('global'); // Default to global access
  const [userWhitelist, setUserWhitelist] = useState<string[]>([]);
  const [allUsers] = useState<string[]>([]); // For user selection

  const { showSnackbar } = useSnackbar();

  // Get report types from API
  useEffect(() => {
    if (open) {
      setLoading(true);
      API.get('saved_reports/file/filter')
        .then((response) => {
          setReportTypes(response.data || []);
        })
        .catch((error) => {
          console.error('Failed to load report types:', error);
          showSnackbar(
            'Failed to load report types. Please try again.',
            'error'
          );
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open, showSnackbar]);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) =>
      Object.assign(file, {
        preview: URL.createObjectURL(file),
      })
    );
    setFiles((prevFiles) => [...prevFiles, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.ms-excel': ['.xls', '.xlsx', '.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'text/plain': ['.txt'],
    },
    multiple: true,
  });

  // Handle file removal
  const removeFile = (index: number) => {
    const newFiles = [...files];
    URL.revokeObjectURL(newFiles[index].preview || '');
    newFiles.splice(index, index + 1);
    setFiles(newFiles);
  };

  // Use API.getMutation for upload
  const uploadMutation = API.getMutation('saved_reports/upload', 'POST');

  // Handle form submission
  const handleSubmit = async () => {
    if (!reportType) {
      showSnackbar('Please select a report type', 'error');
      return;
    }
    if (files.length === 0) {
      showSnackbar('Please select at least one file', 'error');
      return;
    }
    setUploading(true);
    try {
      // Prepare payload as array
      const payload = await Promise.all(
        files.map(async (file) => {
          const fileContent = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () =>
              resolve((reader.result as string).split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
          return {
            name: reportName || 'Untitled Report',
            notes: description,
            access,
            users_white_list:
              access === 'user_list' || access === 'user' ? userWhitelist : [],
            uploaded_saved_reports: {
              file_content: fileContent,
              file_name: file.name,
              file_type: file.type,
            },
          };
        })
      );
      await uploadMutation.mutateAsync(payload);
      if (onUploadSuccess) onUploadSuccess();
      showSnackbar('Reports uploaded successfully', 'success');
      resetForm();
      handleClose();
    } catch (error) {
      console.error('Error uploading reports:', error);
      showSnackbar('Failed to upload reports. Please try again.', 'error');
    } finally {
      setUploading(false);
    }
  };

  // Reset form state
  const resetForm = () => {
    setFiles([]);
    setReportType(null);
    setReportName('');
    setDescription('');
    setAccess('user');
    setUserWhitelist([]);
    // Cleanup previews
    files.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      files.forEach((file) => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [files]);

  // When modal closes
  const onClose = () => {
    if (!uploading) {
      resetForm();
      handleClose();
    }
  };

  // Get icon based on file type
  const getFileIcon = (file: File) => {
    const extension = file.name.split('.').pop()?.toLowerCase();

    if (extension === 'pdf') {
      return <PictureAsPdf color="error" />;
    } else if (['xls', 'xlsx', 'csv'].includes(extension || '')) {
      return <Article color="success" />;
    } else if (extension === 'json') {
      return <Article color="info" />;
    } else {
      return <InsertDriveFile />;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Upload Custom Reports
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
          disabled={uploading}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="Report Name"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              placeholder="Enter a name for your report"
              margin="normal"
              disabled={uploading}
            />

            <TextField
              fullWidth
              label="Description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter a description (optional)"
              margin="normal"
              multiline
              rows={2}
              disabled={uploading}
            />

            {/* Access and User Whitelist using EnhancedSelect */}
            <EnhancedSelect
              label="Access"
              sx={{ mt: 2, mb: 2 }}
              options={[
                // { id: 'user', label: 'User' },
                // { id: 'user_list', label: 'User List' },
                { id: 'account', label: 'Account' },
              ]}
              value={{
                id: access,
                label:
                  access.charAt(0).toUpperCase() +
                  access.slice(1).replace('_', ' '),
              }}
              onChange={(value) => setAccess(value.id)}
              disabled={uploading}
            />
            {(access === 'user_list' || access === 'user') && (
              <EnhancedSelect
                label="User Whitelist"
                multiple
                options={allUsers.map((u) => ({ id: u, label: u }))}
                value={userWhitelist.map((u) => ({ id: u, label: u }))}
                onChange={(values) => setUserWhitelist(values.map((v) => v.id))}
                disabled={uploading}
                sx={{ mt: 2 }}
              />
            )}

            {/* Report Type using MUI Autocomplete for free text and select */}
            <Autocomplete
              freeSolo
              options={reportTypes.map((type) => type.name)}
              value={reportType ? reportType.name : ''}
              onChange={(_, newValue) => {
                if (typeof newValue === 'string') {
                  setReportType({ id: -1, name: newValue });
                } else if (newValue && typeof newValue === 'object') {
                  setReportType({ id: -1, name: newValue.name });
                } else {
                  setReportType(null);
                }
              }}
              onInputChange={(_, newInputValue) => {
                setReportType({ id: -1, name: newInputValue });
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Report Type"
                  margin="normal"
                  disabled={uploading || loading}
                  placeholder="Select or type to add a new report type"
                />
              )}
              disabled={uploading || loading}
            />
          </Box>

          <Box
            {...getRootProps()}
            sx={{
              border: '2px dashed',
              borderColor: 'primary.main',
              borderRadius: 2,
              p: 2,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'primary.100' : 'inherit',
            }}
          >
            <input {...getInputProps()} />
            {isDragActive ? (
              <Typography variant="body1" color="textSecondary">
                Drop the files here ...
              </Typography>
            ) : (
              <>
                <CloudUpload color="action" sx={{ fontSize: 40 }} />
                <Typography variant="body1" color="textSecondary">
                  Drag and drop files here, or click to select files
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Supported formats: PDF, Excel, CSV, JSON, TXT
                </Typography>
              </>
            )}
          </Box>

          <Box sx={{ mt: 2 }}>
            {files.map((file, index) => (
              <Chip
                key={index}
                label={file.name}
                onDelete={() => removeFile(index)}
                deleteIcon={<Close />}
                sx={{ mr: 1, mb: 1 }}
                color="primary"
                variant="outlined"
                size="medium"
                disabled={uploading}
                icon={getFileIcon(file)}
              />
            ))}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={uploading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={uploading}
          endIcon={uploading ? <CircularProgress size={20} /> : null}
        >
          {uploading ? 'Uploading...' : 'Upload Reports'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UploadReportModal;
