import { ExpandLessOutlined, ExpandMoreOutlined } from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { arrayToMap } from 'common/utils';
import { useState } from 'react';

import DataView from '@/components/DataView';
import LazyLoadDynamicSelect from '@/components/molecules/LazyLoadDynamicSelect';
import CompProfilesAdd from '@/components/schedules/CompProfilesView/CompProfilesAdd';
import CompRuleConfig from '@/components/schedules/CompProfilesView/CompRuleConfig';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import { useAccountStore, useRoleStore } from '@/store';
import { FieldTypes, Roles } from '@/types';
import {
  BlankLabel,
  BlankValue,
} from '@/components/molecules/EnhancedSelect/constants';

const CommissionProfileRules = ({ rules, dynamicSelects }) => {
  const [expanded, setExpanded] = useState(false);
  const ruleKeys = [
    { id: 'product_id', label: 'Product(s)' },
    { id: 'product_option_id', label: 'Option(s)' },
    { id: 'issue_age_min', label: 'Issue age min' },
    { id: 'issue_age_max', label: 'Issue age max' },
    { id: 'transaction_type', label: 'Transaction type' },
    { id: 'compensation_type', label: 'Compensation type' },
    { id: 'match_criteria', label: 'Other criteria' },
    { id: 'commission_rate', label: 'Comm rate' },
    { id: 'receivable_rate', label: 'Recv rate' },
    { id: 'payout_rate', label: 'Payout rate' },
    { id: 'keep_rate', label: 'Flat rate' },
    { id: 'up_to_received_rate', label: 'Up to received rate' },
    { id: 'plus_rate', label: 'Percentage of received' },
    { id: 'carrier_grid_level', label: 'Carrier grid level' },
    { id: 'carrier_grid_override_rate', label: 'Carrier grid level rate' },
    { id: 'split', label: 'Split pct' },
  ];
  const { data: optionsLookup } = API.getBasicQuery(
    'companies/products/options'
  );

  const optionsMap = optionsLookup?.data?.reduce((acc, option) => {
    acc[option.id] = option;
    return acc;
  }, {});

  return (
    <Box>
      <Button
        endIcon={expanded ? <ExpandLessOutlined /> : <ExpandMoreOutlined />}
        onClick={() => setExpanded(!expanded)}
        sx={{
          display: 'flex',
          alignItems: 'center',
          whiteSpace: 'nowrap',
          color: 'text.primary',
          fontWeight: 500,
        }}
      >
        {`${rules.length} rules`}
      </Button>
      {expanded && (
        <Table>
          <TableHead>
            <TableRow>
              {ruleKeys.map((ruleKey) => (
                <TableCell key={ruleKey.id}>{ruleKey.label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rules.map((rule, i) => (
              <TableRow key={`rule-${i}`}>
                {ruleKeys.map((ruleKey) => (
                  <TableCell key={ruleKey.id}>
                    {['product_id', 'product_option_id'].includes(ruleKey.id) &&
                    Array.isArray(rule?.[ruleKey.id])
                      ? rule?.[ruleKey.id]?.map((id) => (
                          <Chip
                            label={
                              ruleKey.id === 'product_id'
                                ? dynamicSelects?.find(
                                    (option) => option.id === id
                                  )?.product_name
                                : optionsMap[id]?.name
                            }
                            sx={{ height: 24, m: 0.1 }}
                            key={`rule-${i}::${ruleKey.id}-${id}`}
                          />
                        ))
                      : ruleKey.id === 'match_criteria'
                        ? rule?.[ruleKey.id]?.map((matcher, i) => (
                            <Chip
                              label={Formatter.fieldMatcher(matcher)}
                              sx={{ height: 24, m: 0.1 }}
                              key={`rule-${i}::${ruleKey.id}-${i}`}
                            />
                          ))
                        : rule[ruleKey.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </Box>
  );
};

const CommissionProfileCriteriaRules = ({
  companyId,
  payeeLevelId,
  payerLevelId,
  rules,
  dynamicSelects,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [fetchData, setFetchData] = useState(false);

  const { data: payeeRates } = API.getBasicQuery(
    'comp-grids/rates',
    [
      payeeLevelId ? `comp_grid_level_id=${payeeLevelId}` : null,
      companyId ? `company_id=${companyId}` : null,
      'is_dynamic_select=true',
    ]
      .filter(Boolean)
      .join('&'),
    fetchData
  );
  const { data: payerRates } = API.getBasicQuery(
    'comp-grids/rates',
    [
      payerLevelId ? `comp_grid_level_id=${payerLevelId}` : null,
      companyId ? `company_id=${companyId}` : null,
      'is_dynamic_select=true',
    ]
      .filter(Boolean)
      .join('&'),
    fetchData
  );
  const payeeRatesMap = arrayToMap(
    payeeRates?.data,
    (e) => `${e.comp_grid_level_id}::${e.comp_grid_criterion_id}`
  );
  const payerRatesMap = arrayToMap(
    payerRates?.data,
    (e) => `${e.comp_grid_level_id}::${e.comp_grid_criterion_id}`
  );

  const ruleKeys = [
    { id: 'comp_grid_criteria_id', label: 'Criteria' },
    { id: 'match_criteria', label: 'Other criteria' },
    { id: 'calculation_method', label: 'Calculation method' },
  ];

  return (
    <Box>
      <Button
        onClick={() => {
          setExpanded(!expanded);
          if (!fetchData) setFetchData(true);
        }}
        endIcon={expanded ? <ExpandLessOutlined /> : <ExpandMoreOutlined />}
      >
        {`${rules.length} rules`}
      </Button>
      {expanded && (
        <Table sx={{ minWidth: 1000 }}>
          <TableHead>
            <TableRow>
              {ruleKeys.map((ruleKey) => (
                <TableCell key={ruleKey.id}>{ruleKey.label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rules.map((rule, i) => (
              <TableRow key={`rule-${i}`}>
                {ruleKeys.map((ruleKey) => (
                  <TableCell key={ruleKey.id}>
                    {ruleKey.id === 'match_criteria'
                      ? rule?.[ruleKey.id]?.map((matcher, i) => (
                          <Chip
                            label={Formatter.fieldMatcher(matcher)}
                            sx={{ height: 24, m: 0.1 }}
                            key={`rule-${i}::${ruleKey.id}-${i}`}
                          />
                        ))
                      : ruleKey.id === 'comp_grid_criteria_id'
                        ? rule?.[ruleKey.id]?.map((criterion_id) => (
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                              }}
                            >
                              <Typography variant="body2">
                                {Formatter.getDynamicSelectFormatter(
                                  Formatter.compGridCriterion
                                )(criterion_id, dynamicSelects)}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ whiteSpace: 'nowrap' }}
                              >
                                {rule?.calculation_method === 'compGridLevel'
                                  ? `(@ ${payerRatesMap[`${payerLevelId}::${criterion_id}`]?.rate}% - ${payeeRatesMap[`${payeeLevelId}::${criterion_id}`]?.rate}% = ${+(payerRatesMap[`${payerLevelId}::${criterion_id}`]?.rate ?? 0) - +(payeeRatesMap[`${payeeLevelId}::${criterion_id}`]?.rate ?? 0)}%)`
                                  : rule?.calculation_method === 'payHouseRate'
                                    ? `(@ ${payeeRatesMap[`${payeeLevelId}::${criterion_id}`]?.house_rate}%)`
                                    : rule?.calculation_method ===
                                        'payOverrideUpToTotalRate'
                                      ? `(@ ${payeeRatesMap[`${payeeLevelId}::${criterion_id}`]?.rate}%)`
                                      : ''}
                              </Typography>
                            </Box>
                          ))
                        : rule[ruleKey.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </Box>
  );
};

const CompProfilesView = () => {
  const { userRole } = useRoleStore();
  const { selectedAccount } = useAccountStore();
  const { data: accountSettings } = API.getBasicQuery('accounts/settings');
  const { data: companies } = API.getBasicQuery(
    'companies',
    'type=Carrier&is_dynamic_select=true'
  );

  const isNewCompGrids = accountSettings?.comp_grids_enabled;

  const viewSettings = accountSettings?.pages_settings?.compensation_profiles;
  const viewOnly = viewSettings?.read_only ?? false;

  const dataDesc = {
    label: 'Compensation profiles',
    table: 'schedules/comp-profiles',
    editable: userRole === Roles.ACCOUNT_ADMIN,
    copyable: true,
    validateData: (data) => data.name,
    bulkAdd: true,
    fields: [
      { id: 'name', label: 'Name' },
      {
        id: 'companies',
        label: 'Carriers',
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'companies',
        field: 'id',
        nullable: true,
        multiple: true,
        formatter: (
          val,
          collectionVals: { id: string; company_name: string }[] = []
        ) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            return (
              collectionVals?.find((item) => item.id === val)?.company_name ||
              'Not found'
            );
          }
          return typeof val === 'string' ? val : '';
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => (option?.id ? option.id : option),
      },
      {
        id: 'product_type',
        label: 'Product type',
        type: 'dynamic-select',
        nullable: true,
        multiple: true,
        table: 'comp-grids/products?only_types=true',
        queryParamName: 'comp_grid_id',
        queryParamValue: 'comp_grid_id',
        formatter: (val) => {
          if (val === '') return '';
          return <Chip label={val} sx={{ mb: 0.5 }} />;
        },
      },
      {
        id: 'payee_types',
        label: 'Payout recipients',
        type: 'select',
        options: [
          { id: 'agent', label: 'Agent' },
          { id: 'upline', label: "Agents' hierarchy upline" },
        ],
      },
      {
        id: 'hierarchy_processing',
        label: 'Hierarchy processing',
        type: 'select',
        options: [
          { id: 'none', label: 'None (assigned agent only)' },
          { id: 'upline', label: 'Agent and upline' },
          { id: 'all', label: 'Entire hierarchy' },
        ],
      },
      {
        id: 'calculation_basis',
        label: 'Calculation basis',
        type: 'select',
        options: [
          { id: 'annual_premium', label: 'Policy data: Annual premium' },
          { id: 'target_premium', label: 'Policy data: Target premium' },
          {
            id: 'normalized_premium',
            label: 'Commission data: Normalize premium based on split',
          },
          { id: 'premium', label: 'Commission data: Premium' },
          { id: 'commissions', label: 'Commissions' },
          { id: 'commissions_remaining', label: 'Commissions remaining' },
        ],
      },
      {
        id: 'calculation_scope',
        label: 'Calculation scope',
        type: 'select',
        options: [
          { id: 'commission', label: 'Per commission' },
          { id: 'policy', label: 'Per policy' },
        ],
      },
      {
        id: 'payout_timing',
        label: 'Payout timing',
        type: 'select',
        options: [
          { id: 'first_payment_date', label: 'First payment date' },
          { id: 'first_processed_date', label: 'First processing date' },
        ],
      },
      {
        id: 'payer_grid_level_name',
        label: 'Carrier grid level name',
      },
      {
        id: 'payee_grid_level_name',
        label: 'House payout grid level name',
      },
      {
        id: 'schedules',
        label: 'Comp calculation rules',
        disableSort: true,
        type: FieldTypes.CUSTOM,
        table: isNewCompGrids ? 'comp-grids/criteria' : 'companies/products',
        tableFormatter: (rules, row, dynamicSelects) =>
          isNewCompGrids ? (
            <CommissionProfileCriteriaRules
              companyId={row.company_id}
              payeeLevelId={row.payee_comp_grid_level_id}
              payerLevelId={row.payer_comp_grid_level_id}
              rules={rules}
              dynamicSelects={dynamicSelects}
            />
          ) : (
            <CommissionProfileRules
              rules={rules}
              dynamicSelects={dynamicSelects}
            />
          ),
        render: (field, row, setter) => (
          <CompRuleConfig
            key={'schedules'}
            fieldId="schedules"
            companyId={row.company_id}
            compGridId={row.comp_grid_id}
            payerLevelId={row.payer_comp_grid_level_id}
            payeeLevelId={row.payee_comp_grid_level_id}
            data={row}
            setter={setter}
            readOnly={userRole === Roles.DATA_SPECIALIST}
          />
        ),
      },
      { id: 'notes', label: 'Notes' },
      {
        id: 'contacts_agent_commission_profiles_names',
        label: 'Agents',
        disableSort: true,
        type: 'custom',
        table: 'contacts',
        tableFormatter: (field, row, dynamicSelects, header) => (
          <LazyLoadDynamicSelect
            data={field}
            header={header}
            formatter={(o) =>
              Formatter.contact(o, { account_id: selectedAccount?.accountId })
            }
          />
        ),
        optionFormatter: (option) => `${option.email}`,
        optionValuer: (option) => option?.id,
        render: (field, row, setter, dynamicSelects) => (
          <CompProfilesAdd
            key="contacts_agent_commission_schedule_profiles"
            table="contacts_agent_commission_schedule_profiles"
            data={row}
            setter={setter}
            field={field}
            dynamicSelects={dynamicSelects}
            readOnly={userRole !== Roles.ACCOUNT_ADMIN}
          />
        ),
      },
      { id: 'divider', type: 'divider' },
      {
        id: 'created_at',
        label: 'Created at',
        condition: (data) => !!data.created_at,
        formatter: Formatter.dateTime,
        readOnly: true,
        visible: ['form'],
      },
      {
        id: 'updated_at',
        label: 'Updated at',
        condition: (data) => !!data.updated_at,
        formatter: Formatter.dateTime,
        readOnly: true,
        visible: ['form'],
      },
    ],
    // QueryChips: {},
    filterConfigs: {
      company_id: {
        type: 'select',
        label: 'Carriers',
        options: companies
          ? [
              { id: BlankValue, label: BlankLabel },
              ...companies.map((company) => ({
                id: company.id,
                str_id: company.str_id,
                label: company.company_name,
              })),
            ]
          : [],
      },
    },
  };

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }

  return (
    <DataView
      dataDesc={dataDesc}
      hideExport
      readOnly={userRole === Roles.DATA_SPECIALIST}
      viewOnly={viewOnly}
      enablePagination
      headingOffset={122}
    />
  );
};

export default CompProfilesView;
