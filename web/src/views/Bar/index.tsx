import { AppBar, Avatar, Box, Link, Toolbar, Typography } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

import { useAccountStore } from '@/store';
import { ToggleMenuButton } from './ToggleMenuButton';
import { Notification } from './Notification';
import { UserMenu } from './UserMenu';
import { App } from '@/constants/app';
import firebase from '@/firebase';

export type BarProps = {
  onHelpClick: () => void;
  onSignOutClick: () => void;
  user: firebase.User | null;
};

const Bar = ({ onHelpClick, onSignOutClick, user }: BarProps) => {
  const { selectedAccount, logoUrl } = useAccountStore();
  const url = logoUrl || '/logo192.png';
  const Logo = () => (
    <Avatar
      alt="Logo"
      src={url}
      sx={{ ml: 1 }}
      slotProps={
        url === '/logo192.png'
          ? {
              img: {
                sx: {
                  width: '75%',
                  height: '75%',
                  objectFit: 'contain',
                  borderRadius: '100%',
                },
              },
            }
          : {}
      }
    />
  );

  return (
    <AppBar color="primary" position="fixed" sx={{ zIndex: 1300 }}>
      <Toolbar disableGutters sx={{ mx: 1 }}>
        {user && <ToggleMenuButton />}
        <Link
          color="inherit"
          component={RouterLink}
          to="/"
          underline="none"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', my: 1 }}>
            <Logo />
            <Typography
              color="inherit"
              variant="h5"
              sx={{ textWrap: 'nowrap', ml: 1 }}
            >
              {selectedAccount?.accountName ?? App.Title}
            </Typography>
          </Box>
        </Link>
        <Box
          sx={{
            position: 'absolute',
            right: 0,
            display: 'flex',
          }}
        >
          <Box
            sx={{
              width: 16,
              backgroundImage:
                'linear-gradient(to right, rgba(0,0,0,0), #2196f3)',
            }}
          />
          <Box sx={{ backgroundColor: '#2196f3' }}>
            {user && (
              <>
                <Notification />
                <UserMenu
                  onHelpClick={onHelpClick}
                  onSignOutClick={onSignOutClick}
                  user={user}
                />
              </>
            )}
          </Box>
        </Box>
      </Toolbar>
      {process.env.REACT_APP_ENVIRONMENT === 'development' && (
        <>
          <Box sx={{ position: 'absolute', top: 0, right: 4 }}>🧪</Box>
          <Box sx={{ position: 'absolute', top: 0, left: 4 }}>🧪</Box>
        </>
      )}
    </AppBar>
  );
};

export default Bar;
