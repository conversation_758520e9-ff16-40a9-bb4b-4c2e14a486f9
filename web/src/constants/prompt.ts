import { AIMode<PERSON>, AI_MODEL_LABELS } from 'common/constants/prompt';

export const modelOptions = [
  {
    label: AI_MODEL_LABELS[AIModel.GEMINI],
    value: AIModel.GEMINI,
  },
  {
    label: AI_MODEL_LABELS[AIModel.CHATGPT],
    value: AIModel.CHATGPT,
  },
  {
    label: AI_MODEL_LABELS[AIModel.CLAUDE],
    value: AIModel.CLAUDE,
  },
  {
    label: AI_MOD<PERSON>_LABELS[AIModel.LLAMA_INDEX],
    value: AIModel.LLAMA_INDEX,
  },
  {
    label: AI_MODEL_LABELS[AIModel.GROK],
    value: AIModel.GROK,
  },
  {
    label: AI_MODEL_LABELS[AIModel.MISTRAL],
    value: AIModel.MISTRAL,
  },
];
