import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import type { MergeConfig } from '@/type/merge';

interface MergeMutationVariables {
  sourceRecord: Record<string, any>;
  targetRecord: Record<string, any>;
  mergedData: Record<string, any>;
}

export const useMergeRecord = (mergeConfig: MergeConfig, table: string) => {
  const [showMergeDialog, setShowMergeDialog] = useState<boolean>(false);
  const { showSnackbar } = useSnackbar();

  const updateMutation = API.getMutation(
    mergeConfig.targetTable as any,
    'PATCH'
  );
  const deleteMutation = API.getMutation(table as any, 'DELETE');

  const mergeMutation = useMutation<void, unknown, MergeMutationVariables>({
    mutationFn: async ({ sourceRecord, targetRecord, mergedData }) => {
      await updateMutation.mutateAsync({
        id: targetRecord.id,
        ...mergedData,
      });

      await deleteMutation.mutateAsync({
        ids: [sourceRecord.id],
      });
    },
    onSuccess: () => {
      showSnackbar('Records merged successfully', 'success');
      setShowMergeDialog(false);
    },
    onError: (error: any) => {
      showSnackbar(error.message || 'Merge failed', 'error');
    },
  });

  return {
    showMergeDialog,
    setShowMergeDialog,
    mergeMutation,
    handleMerge: () => setShowMergeDialog(true),
  };
};
