// @vitest-environment jsdom
import { renderHook } from '@testing-library/react';
import { RefetchOptions, QueryObserverResult } from '@tanstack/react-query';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { useExportOptions } from './useExportOptions';
import API from '@/services/API';

vi.mock('@/services/API', () => ({
  default: {
    getBasicQuery: vi.fn(),
  },
}));

const mockGetBasicQuery = vi.mocked(API.getBasicQuery);

const validProcessor = {
  name: 'Test Processor',
  str_id: 'test-processor',
  processor: '() => {}',
};

const invalidProcessor = {
  name: '',
  str_id: '',
  processor: '',
};

describe('useExportOptions', () => {
  beforeEach(() => {
    mockGetBasicQuery.mockReset();
  });

  it('Given no processors, then return default options', () => {
    mockGetBasicQuery.mockReturnValue({
      abort: () => {},
      data: [],
      error: null,
      isError: false,
      isPending: false,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isFetched: true,
      isFetchedAfterMount: true,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      fetchStatus: 'idle',
      promise: Promise.resolve(),
      errorUpdateCount: 0,
      isFetching: false,
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(result.current).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
    ]);
  });

  it('Given valid processors, then return default and processor options', () => {
    mockGetBasicQuery.mockReturnValue({
      data: [validProcessor],
      error: null,
      isLoading: false,
      abort: () => {},
      isError: false,
      isPending: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      errorUpdateCount: 0,
      isFetched: false,
      isFetchedAfterMount: false,
      isFetching: false,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: function (
        _options?: RefetchOptions
      ): Promise<QueryObserverResult<unknown, Error>> {
        throw new Error('Function not implemented.');
      },
      fetchStatus: 'fetching',
      promise: Promise.resolve(),
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(result.current).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
    ]);
  });

  it('Given invalid processors, then filter out invalid processor options', () => {
    mockGetBasicQuery.mockReturnValue({
      abort: () => {},
      data: [invalidProcessor],
      error: null,
      isError: false,
      isPending: false,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isFetched: true,
      isFetchedAfterMount: true,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      fetchStatus: 'idle',
      promise: Promise.resolve(),
      errorUpdateCount: 0,
      isFetching: false,
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(result.current).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
    ]);
  });
});
