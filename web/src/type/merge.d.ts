import type {
  DisplayNameFormatter,
  UniqueKeyGetter,
} from '@/utils/displayUtils';

export type MergeFieldType = 'array' | 'object' | 'string' | 'number';
export type MergeStrategy = 'concat' | 'replace' | 'merge' | 'append' | 'sum';

export interface MergeFieldConfig {
  field: string;
  type?: MergeFieldType;
  mergeStrategy: MergeStrategy;
  displayName?: string;
  unique?: boolean;
}

export interface MergeConfig {
  enabled: boolean;
  targetField?: string;
  targetTable: string;
  mergeableFields: MergeFieldConfig[];
  confirmationMessage?: string;
  targetSelector?: {
    table: string;
    labelField: string;
    valueField: string;
  };
  displayNameFormatter?: DisplayNameFormatter;
  uniqueKeyGetter?: UniqueKeyGetter;
}
