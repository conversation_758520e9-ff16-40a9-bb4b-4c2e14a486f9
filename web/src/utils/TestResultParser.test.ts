import { AIModel } from 'common/constants/prompt';

import {
  ParsedR<PERSON>ult,
  ParserFactory,
  TestResultParser,
} from './TestResultParser';

const GEMINI_MODEL = AIModel.GEMINI;
const OTHER_MODELS: AIModel[] = [
  AIModel.CHATGPT,
  AIModel.GROK,
  AIModel.CLAUDE,
  AIModel.LLAMA_INDEX,
  AIModel.MISTRAL,
];

describe('ParserFactory', () => {
  it('should return GeminiParser for AIModel.GEMINI', () => {
    const parser = ParserFactory.getParser(GEMINI_MODEL);
    expect(parser.constructor.name).toBe('GeminiParser');
  });

  it('should return DefaultParser for other models', () => {
    for (const model of OTHER_MODELS) {
      const parser = ParserFactory.getParser(model);
      expect(parser.constructor.name).toBe('DefaultParser');
    }
  });
});

const CHATGPT_MODEL = AIModel.CHATGPT;

const VALID_PARSED_RESULT: ParsedResult = {
  statement_date: '01/01/2024',
  policy_number: 'P123',
  commission_amount: 100,
  customer_name: 'John Doe',
  agent_name: 'Agent Smith',
  carrier_name: 'CarrierX',
  paying_entity: null,
  premium_amount: 200,
  commission_rate: 0.1,
  effective_date: '01/01/2024',
  period_date: '01/01/2024',
  transaction_type: null,
  product_type: 'Life',
  product_name: 'Life Plan',
  compensation_type: 'Standard',
  statement_number: null,
  agent_id: 'A1',
  group_number: null,
};

describe('TestResultParser', () => {
  it('should parse GEMINI result correctly', () => {
    const geminiData = JSON.stringify({
      parts: [{ text: JSON.stringify([VALID_PARSED_RESULT]) }],
    });
    const results = [
      {
        model: GEMINI_MODEL,
        result: {
          data: geminiData,
          extraction: { id: 1, str_id: '1', method: 'test' },
        },
      },
    ];
    const parsed = TestResultParser.parseResults(results);
    expect(parsed[GEMINI_MODEL]).toEqual([VALID_PARSED_RESULT]);
  });

  it('should parse default (non-GEMINI) result correctly', () => {
    const chatgptData = JSON.stringify([VALID_PARSED_RESULT]);
    const results = [
      {
        model: CHATGPT_MODEL,
        result: {
          data: chatgptData,
          extraction: { id: 2, str_id: '2', method: 'test' },
        },
      },
    ];
    const parsed = TestResultParser.parseResults(results);
    expect(parsed[CHATGPT_MODEL]).toEqual([VALID_PARSED_RESULT]);
  });

  it('should return error object if result has error', () => {
    const results = [
      {
        model: CHATGPT_MODEL,
        error: 'Some error',
        result: undefined,
      },
    ];
    const parsed = TestResultParser.parseResults(results);
    expect(parsed[CHATGPT_MODEL]).toEqual({ error: 'Some error' });
  });

  it('should return [] if parse fails', () => {
    const results = [
      {
        model: CHATGPT_MODEL,
        result: {
          data: 'not a json',
          extraction: { id: 4, str_id: '4', method: 'test' },
        },
      },
    ];
    const parsed = TestResultParser.parseResults(results);
    expect(parsed[CHATGPT_MODEL]).toEqual([]);
  });
});
