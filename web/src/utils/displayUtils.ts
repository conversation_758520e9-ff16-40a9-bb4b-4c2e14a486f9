export type DisplayNameFormatter = (item: any) => string | null;

export type UniqueKeyGetter = (item: any) => string | null;

export const getDisplayName = (
  item: any,
  customFormatter?: DisplayNameFormatter
): string => {
  if (customFormatter && typeof customFormatter === 'function') {
    const result = customFormatter(item);
    if (result) return result;
  }

  if (typeof item === 'object' && item !== null) {
    if (item.name) return item.name;
    if (item.label) return item.label;
    if (item.title) return item.title;
    if (item.str_id) return item.str_id;
    if (item.id) return `ID: ${item.id}`;

    const keys = Object.keys(item);
    if (keys.length === 1) {
      return `${keys[0]}: ${item[keys[0]]}`;
    }

    return JSON.stringify(item);
  }

  return item?.toString() || 'N/A';
};

export const getUniqueKey = (
  item: any,
  customKeyGetter?: UniqueKeyGetter
): string => {
  if (customKeyGetter && typeof customKeyGetter === 'function') {
    const result = customKeyGetter(item);
    if (result) return result;
  }

  if (typeof item === 'object' && item !== null) {
    if (item.str_id) return item.str_id;
    if (item.id) return String(item.id);
    return JSON.stringify(item);
  }

  return String(item);
};

export const removeDuplicates = <T>(
  array: T[],
  keyGetter?: UniqueKeyGetter
): T[] => {
  if (!Array.isArray(array)) return array;

  const uniqueItems: T[] = [];
  const seenKeys = new Set<string>();

  array.forEach((item) => {
    const key = getUniqueKey(item, keyGetter);
    if (!seenKeys.has(key)) {
      seenKeys.add(key);
      uniqueItems.push(item);
    }
  });

  return uniqueItems;
};
