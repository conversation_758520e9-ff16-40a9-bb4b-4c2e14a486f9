const RECONCILIATION_CONFIG_TYPE = 'reconciliation' as const;

export type AccountConfig = {
  id: number;
  notes: string | null;
  type: 'reconciliation';
  value: {
    version: string;
  };
};

// Utility function to get reconciliation version
export const getReconciliationVersion = (
  accountConfigs: AccountConfig[]
): string | undefined => {
  if (!accountConfigs || !Array.isArray(accountConfigs)) {
    return '';
  }

  const reconciliationConfig = accountConfigs.find(
    (config) => config.type === RECONCILIATION_CONFIG_TYPE
  );

  return reconciliationConfig?.value?.version;
};
