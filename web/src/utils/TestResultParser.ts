import { AIModel } from 'common/constants/prompt';

interface ParsedResult {
  statement_date: string;
  policy_number: string;
  commission_amount: number;
  customer_name: string;
  agent_name: string;
  carrier_name: string;
  paying_entity: string | null;
  premium_amount: number;
  commission_rate: number;
  effective_date: string;
  period_date: string;
  transaction_type: string | null;
  product_type: string;
  product_name: string;
  compensation_type: string;
  statement_number: string | null;
  agent_id: string;
  group_number: string | null;
}

interface TestResult {
  model: AIModel;
  error?: string;
  result?: {
    data: string;
    extraction: {
      id: number;
      str_id: string;
      method: string;
    };
  };
}

abstract class BaseParser {
  abstract parse(data: string): ParsedResult[];
}

class DefaultParser extends BaseParser {
  parse(data: string): ParsedResult[] {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('Error parsing default result:', error);
      return [];
    }
  }
}

class GeminiParser extends BaseParser {
  parse(data: string): ParsedResult[] {
    try {
      const parsed = JSON.parse(data);
      const text = parsed.parts[0].text;
      const result = JSON.parse(text);

      // Convert string numbers to actual numbers
      return result;
    } catch (error) {
      console.error('Error parsing Gemini result:', error);
      return [];
    }
  }
}

export class ParserFactory {
  private static parsers: { [key in AIModel]?: BaseParser } = {
    [AIModel.GEMINI]: new GeminiParser(),
  };

  static getParser(model: AIModel): BaseParser {
    const parser = this.parsers[model];
    if (!parser) {
      return new DefaultParser();
    }
    return parser;
  }
}

export class TestResultParser {
  static parseResults(results: TestResult[]): {
    [key in AIModel]?: ParsedResult[] | { error: string };
  } {
    const parsedResults: {
      [key in AIModel]?: ParsedResult[] | { error: string };
    } = {};

    for (const result of results) {
      try {
        if (result.error) {
          parsedResults[result.model] = { error: result.error };
          continue;
        }

        const parser = ParserFactory.getParser(result.model);
        parsedResults[result.model] = parser.parse(result.result?.data ?? '');
      } catch (error) {
        console.error(
          `Error parsing results for model ${result.model}:`,
          error
        );
        parsedResults[result.model] = [];
      }
    }

    return parsedResults;
  }
}

export type { ParsedResult, TestResult };
