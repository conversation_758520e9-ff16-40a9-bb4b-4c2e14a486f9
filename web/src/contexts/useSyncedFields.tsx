import { useEffect } from 'react';

import API from '@/services/API';

const useSyncedFields = (table: string) => {
  const { data, abort } = API.getBasicQuery(
    'data_processing/sync/synced-fields'
  );
  const { data: workerSyncedFields } = API.getBasicQuery(
    'data_processing/sync/worker-synced-fields'
  );

  useEffect(() => {
    return () => abort();
    /*
     * <PERSON>t disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Nov/2024
     * MISSED REFs: 'abort'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const syncedFields = data?.[table];

  return {
    syncedFields,
    workerSyncedFields,
    isSyncedField: (
      data: { [key: string]: any; sync_id?: string },
      key: string,
      config?: { overrideFields?: string[] }
    ) => {
      const fields =
        workerSyncedFields?.[data.sync_worker]?.[table] ?? syncedFields;

      return (
        data.sync_id &&
        fields?.includes(key) &&
        (!config?.overrideFields || !config?.overrideFields?.includes(key))
      );
    },
  };
};
export const useSyncedFieldsNew = () => {
  const { data, abort } = API.getBasicQuery(
    'data_processing/sync/worker-synced-fields'
  );

  useEffect(() => {
    return () => abort();
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Nov/2024
     * MISSED REFs: 'abort'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    workerSyncedFields: data || {},
    isSyncedField: (
      data: { [key: string]: any; sync_id?: string },
      syncedFields: string[],
      key: string,
      config?: { overrideFields?: string[] }
    ) => {
      // RiskTag syncs documents data back to BenefitPoint, and saves the entity_id in the sync_id field without sync worker
      return (data.sync_id &&
        data.sync_worker &&
        syncedFields?.includes(key) &&
        (!config?.overrideFields ||
          !config?.overrideFields?.includes(key))) as boolean;
    },
  };
};

export default useSyncedFields;
