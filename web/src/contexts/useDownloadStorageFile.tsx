import { captureException } from '@sentry/react';
import { removeLeadingTrailingChar } from 'common/helpers';
import { useCallback, useEffect, useState } from 'react';
import { AxiosResponse } from 'axios';

import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';

type downloadParams = {
  endpoint_str_id: string;
  file_preview_type: 'override' | 'original' | 'logo';
  endpoint?: 'documents' | 'accounts';
};

const useDownloadStorageFile = () => {
  const [file, setFile] = useState<File | null>(null);
  const [blobFile, setBlobFile] = useState<Blob | null>(null);

  const [downloadFileParams, setDownloadFileParams] = useState<downloadParams>(
    {} as downloadParams
  );

  const { showSnackbar } = useSnackbar();

  const downloader = API.getMutation('storage/download', 'POST', {
    rawData: true,
  });

  const downloadFile = useCallback(
    async (downloadFileParams: downloadParams) => {
      const { endpoint_str_id, file_preview_type, endpoint } =
        downloadFileParams;
      if (!file_preview_type || !endpoint_str_id) return;
      try {
        const res: AxiosResponse = await downloader.mutateAsync({
          endpoint_str_id,
          file_preview_type,
          endpoint,
        });

        if (res.status !== 200) {
          showSnackbar(`Error: ${res.statusText}`, 'error');
          return null;
        }

        const filename = removeLeadingTrailingChar(
          res.headers['content-disposition']?.split('=')[1] || 'documents',
          '"'
        );
        const blob = res.data;
        const file = new File([blob], filename, { type: blob.type });

        setFile(file);
        setBlobFile(blob);
        return file;
      } catch (error: any) {
        console.error(
          typeof error === 'object' ? JSON.stringify(error) : error
        );
        captureException(error);
        showSnackbar(
          typeof error === 'object' ? JSON.stringify(error) : error,
          'error'
        );
        return null;
      }
    },
    /*
     * Lint disabled due to regression in the exhaustive-deps rule
     * PLT-2299 https://linear.app/fintary/issue/PLT-2299/usedownloadstoragefile-hook-lint-fix
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [downloadFileParams]
  );

  useEffect(() => {
    const { endpoint_str_id, file_preview_type } = downloadFileParams;
    if (endpoint_str_id && file_preview_type) {
      downloadFile(downloadFileParams);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downloadFileParams]);

  return { file, blobFile, downloadFile, setDownloadFileParams };
};

export default useDownloadStorageFile;
