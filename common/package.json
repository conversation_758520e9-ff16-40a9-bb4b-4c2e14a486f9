{"name": "common", "version": "0.24.12", "private": true, "main": "index.js", "scripts": {"test:unit": "vitest run --config vitest.config.unit.ts", "test:watch": "vitest --config vitest.config.unit.ts", "dev": "tsc -w", "release": "npm run release --prefix ../", "changelog": "npm run changelog --prefix ../", "deep-clean": "rm -rf node_modules", "lint": "eslint . --max-warnings=0", "lint-fix": "eslint . --fix"}, "dependencies": {"@prisma/client": "^6.6.0", "chrono-node": "2.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "currency": "^4.1.0", "dayjs": "^1.11.13", "nanoid": "^5.1.5", "pretty-ms": "^9.2.0", "reflect-metadata": "^0.2.2", "typescript": "^5.8.3", "zod-openapi": "^4.2.4"}, "devDependencies": {"vitest": "^3.1.1", "vitest-mock-extended": "^3.1.0"}}