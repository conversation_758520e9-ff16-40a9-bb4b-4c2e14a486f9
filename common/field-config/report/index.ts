import { TransactionParty } from 'common/globalTypes';
import UILabels from 'common/constants/UILabels';
import { FieldTypes } from 'common/constants';
import { FieldConfig } from 'common/field-config/shared/types/field';

import { ReportTextFormatter } from './text-formatter';
import { buildReceivableValueFilter } from './helpers/transactions.helpers';
import { Roles } from '../shared/types/roles';

export type ReportOptions = {
  isFeatureFlagEnabled?: (featureFlag: string) => boolean;
  account_id?: string | null;
  deDupeFields?: unknown;
};

export const getReportFieldConfig = ({
  mode = 'default',
  options,
  additionalConfig,
}: {
  mode?: string | null;
  options?: ReportOptions;
  additionalConfig?: {
    timezone?: string;
    account_id?: string;
  };
}) => {
  const labels = new UILabels(mode);
  const textFormatter = new ReportTextFormatter(additionalConfig);

  return {
    useNewTable: true,
    label: labels.getLabel('transactions', 'title'),
    labelSimple: labels.getLabel('transactions', 'titleSimple'),
    table: 'report_data',
    copyable: true,
    outstandingFieldsInMobileView: [
      'policy_id',
      'customer_name',
      'effective_date',
      'carrier_name',
      'premium_amount',
    ],
    stickyColumns: ['policy_id'],
    dynamicSelectsConfig: [
      {
        table: 'companies',
        queryParamName: 'companyNames',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['writing_carrier_name', 'carrier_name'],
        mapKey: 'company_name',
      },
      {
        table: 'contacts',
        queryParamName: 'str_id',
        queryParamValue: [], // Will be collected from data
        collectDataFields: [
          'contacts',
          'contacts_split',
          'contacts_commission_split',
          'agent_payout_rate_override',
        ],
        // Will be used to collect data for queryParamValue
        dataExtractors: {},
        mapKey: 'str_id',
      },
      {
        table: 'documents',
        queryParamName: 'str_id',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['document_id'],
        mapKey: 'str_id',
      },
      {
        table: 'companies/products',
        queryParamName: 'product_name',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['product_name'],
        mapKey: 'product_name',
      },
      {
        table: 'commission_schedules/agent_commission_schedule_profiles',
        queryParamName: 'str_id',
        queryParamValue: [], // Will be collected from data
        collectDataFields: ['commission_profile_id'],
        mapKey: 'str_id',
      },
    ],
    filters: {
      transaction_type: {
        label: 'Transaction type',
      },
      group_name: {
        label: 'Group name',
      },
      agent_name: {
        label: 'Agent name',
      },
      contacts: {
        label: 'Agents',
      },
      document_id: {
        label: 'Document',
      },
      policy_status: {
        label: 'Status',
      },
      product_name: {
        label: 'Product name',
      },
      product_type: {
        label: 'Product type',
      },
      writing_carrier_name: {
        label: 'Master company',
      },
      account_type: {
        label: 'Account type',
      },
      tags: {
        label: 'Tags',
      },
    },
    fields: {
      policy_id: {
        sticky: 'left',
        label: labels.getLabel('transactions', 'salesId'),
        bulkEdit: true,
        matches: [
          'policy id',
          'policy no',
          'policy number',
          'policy #',
          'policy_number',
          'policyid',
        ],
        enabled: true,
        reconciler: true,
        copyable: true,
        textFormatter: textFormatter.policyIdTextFormatter,
      },
      agent_name: {
        label: labels.getLabel('transactions', 'agent'),
        bulkEdit: true,
        matches: [
          'assigned agent',
          'agent',
          'agt',
          'writing agent',
          'writing agt',
          'agent name',
          'agt name',
          'assigned_agent',
          'producer name',
          'assigned producer',
        ],
        enabled: true,
        global: true,
        textFormatter: textFormatter.textFormatter,
      },
      customer_name: {
        bulkEdit: true,
        label: 'Customer name',
        matches: [
          'customer name',
          'customer',
          'insuree',
          'client',
          'client name',
          'insured name',
          'policy holder',
          'policy holder name',
          'subscriber',
          'name of insured',
          'customername',
          'account name',
        ],
        style: {
          pl: 2,
        },
        required: false,
        reconciler: true,
        enabled: true,
        type: FieldTypes.CUSTOM,
        textFormatter: textFormatter.customerNameTextFormatter,
      },
      dba: {
        label: 'DBA',
        matches: ['dba', 'aka'],
        required: false,
        reconciler: true,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      internal_id: {
        bulkEdit: true,
        label: 'Internal ID',
        matches: ['internal id', 'internalid'],
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      effective_date: {
        bulkEdit: true,
        label: labels.getLabel('transactions', 'startDate'),
        matches: [
          'policy effective date',
          'effective date',
          'effective',
          'eff date',
          'in force date',
          'effective_date',
          'effective_date2',
          'effectivedate',
        ],
        enabled: true,
        required: false,
        type: 'date',
        textFormatter: textFormatter.effectiveDateTextFormatter,
      },
      signed_date: {
        label: 'Signed date',
        type: 'date',
        enabled: true,
        required: false,
        bulkEdit: true,
        textFormatter: textFormatter.signedDateTextFormatter,
      },
      policy_date: {
        label: 'Policy date',
        type: 'date',
        enabled: true,
        required: false,
        bulkEdit: true,
        textFormatter: textFormatter.policyDateTextFormatter,
      },
      receivable_value_agency_rate: buildReceivableValueFilter(
        TransactionParty.AGENCY
      ),
      receivable_value_agent_rate: buildReceivableValueFilter(
        TransactionParty.AGENT
      ),
      receivable_value_override_rate: buildReceivableValueFilter(
        TransactionParty.POLICY
      ),
      writing_carrier_name: {
        width: 350,
        label: labels.getLabel('transactions', 'payingEntity'),
        bulkEdit: true,
        matches: [
          'mga/broker',
          'mga',
          'broker',
          'carrier name',
          'carrier',
          'carriername',
        ],
        enabled: true,
        required: false,
        reconciler: true,
        global: true,
        type: FieldTypes.CUSTOM,
        table: 'companies',
        field: 'company_name',
        textFormatter: textFormatter.writingCarrierNameTextFormatter,
      },
      writing_carrier_id: {
        label: `${labels.getLabel('transactions', 'payingEntity')} ID`,
        exportOnly: true,
        isRelationalField: true,
        enabled: true,
        textFormatter: textFormatter.writingCarrierIdTextFormatter,
        ref: 'writing_carrier_name',
      },
      premium_amount: {
        label: labels.getLabel('transactions', 'annualizedRevenue'),
        description:
          'Annualized premium amount as recorded in production reports',
        matches: [
          'policy premium',
          'premium amount',
          'premium amt',
          'premium paid',
          'premium',
          'premium - annualized',
          'annualized_premium',
          'annualized_premium2',
          'premiumamount',
        ],
        required: false,
        enabled: true,
        type: 'currency',
        textFormatter: textFormatter.premiumAmountTextFormatter,
      },
      excess_amount: {
        bulkEdit: true,
        label: 'Excess',
        description: 'Amount of money required to pay when making a claim',
        matches: [],
        required: false,
        enabled: true,
        type: 'currency',
        textFormatter: textFormatter.excessAmountTextFormatter,
      },
      product_type: {
        bulkEdit: true,
        label: 'Product type',
        matches: [
          'product type',
          'product line',
          'line_of_business',
          'producttype',
        ],
        enabled: true,
        textFormatter: textFormatter.textFormatter,
      },
      product_sub_type: {
        label: 'Product sub type',
        matches: [],
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      product_name: {
        width: 250,
        label: 'Product name',
        bulkEdit: true,
        matches: ['product name', 'product'],
        reconciler: true,
        enabled: mode === 'insurance',
        type: FieldTypes.CUSTOM,
        table: 'companies/products',
        queryParamName: 'company_name',
        queryParamValue: 'writing_carrier_name',
        field: 'product_name',
        tip: 'Master company is required to set product name',
        textFormatter: textFormatter.productNameTextFormatter,
      },
      product_option_name: {
        label: 'Product option name',
        matches: ['option', 'product option'],
        reconciler: true,
        enabled: mode === 'insurance',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      cancellation_date: {
        label: 'Cancellation date',
        enabled: true,
        type: 'date',
        bulkEdit: true,
        textFormatter: textFormatter.cancellationDateTextFormatter,
      },
      reinstatement_date: {
        label: 'Reinstatement date',
        enabled: true,
        type: 'date',
        bulkEdit: true,
        textFormatter: textFormatter.reinstatementDateTextFormatter,
      },
      config: {
        label: 'Allied payment rule',
        enabled: options?.isFeatureFlagEnabled?.('paymentAllocation'),
        type: 'custom',
        access: [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST],
        textFormatter: textFormatter.configTextFormatter,
      },
      transaction_type: {
        label: 'Transaction type',
        matches: [
          'policy business type',
          'transaction type',
          'commission type',
          'policy type',
          'transactiontype',
        ],
        required: false,
        enabled: mode === 'insurance',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      commissions_expected: {
        label: 'Commissions due',
        description: 'Commission amount due as recorded in production reports',
        matches: [
          'agency commission total',
          'commissions expected',
          'commission expected',
          'commissionsexpected',
        ],
        required: false,
        enabled: mode === 'insurance',
        type: 'currency',
        bulkEdit: true,
        textFormatter: textFormatter.commissionsExpectedTextFormatter,
      },
      policy_status: {
        label: 'Status',
        description: 'Policy status',
        matches: ['status', 'active', 'policystatus'],
        required: false,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      account_type: {
        label: 'Account type',
        required: false,
        enabled: true,
        matches: ['account type', 'accounttype'],
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      geo_state: {
        label: 'State',
        type: FieldTypes.CUSTOM,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      split_percentage: {
        label: 'Split percentage',
        enabled: true,
        matches: ['split percentage'],
        type: FieldTypes.PERCENTAGE,
        bulkEdit: true,
        textFormatter: textFormatter.splitPercentageTextFormatter,
      },
      group_name: {
        label: 'Group name',
        matches: ['group name'],
        required: false,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      payment_mode: {
        label: 'Payment mode',
        matches: ['payment mode'],
        required: false,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      policy_term_months: {
        label: 'Policy term (months)',
        required: false,
        enabled: true,
        matches: ['policy term', 'policyterm'],
        type: 'integer',
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      commissionable_premium_amount: {
        label: 'Target premium',
        matches: ['target premium', 'commissionable premium'],
        enabled: true,
        type: 'currency',
        bulkEdit: true,
        textFormatter: textFormatter.commissionablePremiumAmountTextFormatter,
      },
      notes: {
        label: 'Notes',
        matches: ['notes', 'note', 'remark', 'notation', 'memo'],
        required: false,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      group_id: {
        label: 'Group ID',
        matches: [
          'group id',
          'group number',
          'group no',
          'grp #',
          'grp number',
          'groupid',
        ],
        enabled: mode === 'insurance',
        required: false,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      contacts: {
        label: 'Agents',
        bulkEdit: true,
        required: false,
        enabled: true,
        type: FieldTypes.CUSTOM,
        multiple: true,
        textFormatter: textFormatter.contactsTextFormatter,
      },
      contacts_id: {
        label: 'Agents IDs',
        exportOnly: true,
        isRelationalField: true,
        enabled: true,
        textFormatter: textFormatter.contactsIdTextFormatter,
        ref: 'contacts',
      },
      contacts_split: {
        label: 'Agent policy split',
        enabled: true,
        type: 'custom',
        table: 'contacts',
        access: [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST],
        bulkEdit: true,
        textFormatter: textFormatter.contactsSplitTextFormatter,
      },
      contacts_commission_split: {
        label: 'Agent commission split',
        enabled: true,
        type: 'custom',
        table: 'contacts',
        access: [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST],
        bulkEdit: true,
        textFormatter: textFormatter.contactsSplitTextFormatter,
      },
      issue_age: {
        label: 'Issue age',
        required: false,
        enabled: true,
        matches: ['issue age'],
        type: 'integer',
        bulkEdit: true,
        textFormatter: textFormatter.issueAgeTextFormatter,
      },
      customer_paid_premium_amount: {
        label: 'Customer Paid Premium Amount',
        required: false,
        enabled: true,
        matches: ['Basis', 'customer paid premium amount'],
        bulkEdit: true,
        textFormatter: textFormatter.customerPaidPremiumAmountTextFormatter,
      },
      aggregation_id: {
        label: 'Aggregation ID',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      aggregation_primary: {
        label: 'Head of household',
        type: 'boolean',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.aggregationPrimaryTextFormatter,
      },
      statement_data: {
        width: 250,
        label: 'Commissions',
        required: false,
        enabled: true,
        type: FieldTypes.CUSTOM,
        table: 'contacts',
        readOnly: true,
        textFormatter: textFormatter.statementDataTextFormatter,
      },
      children_report_data: {
        label: 'Linked policies',
        required: false,
        enabled: true,
        type: FieldTypes.CUSTOM,
        readOnly: true,
        textFormatter: textFormatter.childrenReportDataTextFormatter,
      },
      type: {
        id: 'type',
        label: 'Type',
        readOnly: true,
        hidden: true,
        textFormatter: textFormatter.textFormatter,
      },
      tags: {
        label: 'Tags',
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.tagsTextFormatter,
      },
      document_id: {
        label: 'Document',
        matches: ['document id', 'document'],
        enabled: true,
        required: false,
        type: FieldTypes.CUSTOM,
        table: 'documents',
        queryParamValue: 'report',
        queryParamName: 'type',
        bulkEdit: true,
        textFormatter: textFormatter.documentIdTextFormatter,
      },
      document_relationship_id: {
        label: 'Document ID',
        enabled: true,
        exportOnly: true,
        isRelationalField: true,
        textFormatter: textFormatter.documentRelationshipIdTextFormatter,
        ref: 'document_id',
      },
      processing_status: {
        label: 'Processing status',
        matches: ['new', 'processed', 'frozen'],
        type: FieldTypes.SELECT,
        enabled: true,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
      first_payment_date: {
        label: 'First payment date',
        enabled: true,
        type: FieldTypes.DATE,
        bulkEdit: true,
        textFormatter: textFormatter.firstPaymentDateTextFormatter,
      },
      first_processed_date: {
        label: 'First processed date',
        enabled: true,
        type: FieldTypes.DATE,
        bulkEdit: true,
        textFormatter: textFormatter.firstProcessedDateTextFormatter,
      },
      commission_profile_id: {
        label: 'Comp profile',
        enabled: true,
        required: false,
        readOnly: true,
        type: FieldTypes.DYNAMIC_SELECT,
        table: 'commission_schedules/agent_commission_schedule_profiles',
        textFormatter: textFormatter.commissionProfileIdTextFormatter,
      },
      agent_payout_rate_override: {
        label: 'Agent payout rate override',
        table: 'contacts',
        enabled: true,
        type: FieldTypes.CUSTOM,
        bulkEdit: true,
        textFormatter: textFormatter.agentPayoutRateOverrideTextFormatter,
      },
      sync_id: {
        label: 'Sync id',
        enabled: true,
        type: FieldTypes.TEXT,
        bulkEdit: true,
        textFormatter: textFormatter.textFormatter,
      },
    } as Record<string, FieldConfig>,
  };
};

export const mergeConfigsWithRenderInstance = (
  config: any,
  renderConfig: any
) => {
  Object.entries(config).forEach(([k, v]) => {
    if (k === 'fields') {
      Object.entries(v).forEach(([kField, vField]) => {
        if (vField.exportOnly) {
          return;
        }

        renderConfig.fields[kField] = {
          ...renderConfig.fields[kField],
          ...vField,
        };
      });
    } else {
      renderConfig[k] = v;
    }
  });
};
