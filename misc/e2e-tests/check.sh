#!/bin/dash

if [ $# -eq 0 ]; then
  echo "Usage: $0 <test-type>"
  echo "test-type should be 'all' or 'monitor'."
  exit 1
fi

TEST_TYPE=$1
TEST_FAILED=0

echo "Running $TEST_TYPE test..."

check_timeout() {
  if command -v timeout >/dev/null 2>&1; then
    timeout "$1" "$2" "$3" "$4" "$5"
  else
    echo "Warning: timeout command not found, running without timeout..."
    "$2" "$3" "$4" "$5"
  fi
}

case "$TEST_TYPE" in
  all)
    echo "Running health probe for all..."
    node ./src/utils/health-probe.ts &

    echo "Running all checker..."
    check_timeout 6000 npx playwright test

    if [ $? -ne 0 ]; then
      echo "Playwright tests failed."
      TEST_FAILED=1
    fi
    
    echo "Exporting test report..."
    npm run test-result
  ;;
  monitor)
    echo "Running health probe for monitor..."
    node ./src/utils/health-probe.ts &

    echo "Running monitor checker..."
    check_timeout 1800 npx playwright test --grep @Monitor

    if [ $? -ne 0 ]; then
      echo "Playwright tests failed or timed out."
      TEST_FAILED=1
    fi
    
    echo "Exporting test report..."
    npm run test-result
  ;;
  *)
    echo "Invalid test type: $TEST_TYPE"
    echo "Use 'all' or 'monitor'"
    exit 1
  ;;
esac

pkill -9 node

if [ $TEST_FAILED -eq 1 ]; then
  echo "One or more tests failed."
  exit 1
else
  echo "All tests passed."
  exit 0
fi