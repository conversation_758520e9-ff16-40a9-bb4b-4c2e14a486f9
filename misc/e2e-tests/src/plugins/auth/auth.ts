import { expect, type Page, Locator } from '@playwright/test';
// Locators
import accountLocatorsGenerator from '@plugins/locators/account/account';
// Data
import { BASE_URL } from '@assets/data/authData';

// Google OAuth
const googleOAuth = async (
  page: Page,
  email: string,
  password: string
): Promise<void> => {
  // Wait for popup page
  const [popup] = await Promise.all([
    page.waitForEvent('popup'),
    page.locator('text="Google"').click(),
  ]);

  await popup.waitForSelector('input[type="email"]', { state: 'visible' });
  await popup.fill('input[type="email"]', email);
  await popup.click('#identifierNext');

  await popup.waitForSelector('input[type="password"]', { state: 'visible' });
  await popup.fill('input[type="password"]', password);
  await popup.click('#passwordNext');

  // Wait for the popup to close or redirect
  await popup.waitForEvent('close');

  // Verify the signed-in state on the original page
  const info: Locator = page.locator(`text=Signed in as ${email}`);
  await info.waitFor({ state: 'visible', timeout: 10000 });
  await expect(info).toBeVisible();
};

const auth = async (
  page: Page,
  email: string,
  password: string,
  url?: string
): Promise<void> => {
  const accountLocators = accountLocatorsGenerator(page);
  const { signInButton, emailInput, passwd } = accountLocators;
  // Sign-in action
  if (url) {
    await page.goto(url);
  } else {
    await page.goto(BASE_URL);
  }

  await signInButton.click();
  await emailInput.fill(email);
  await passwd.fill(password);
  await signInButton.click();
  await page.waitForLoadState('load');
  await page
    .locator(`text=Signed in as ${email}`)
    .waitFor({ state: 'visible' });
};

export { googleOAuth, auth };
