import { type Page, Locator } from '@playwright/test';

const accountLocatorsGenerator = (page: Page) => {
  // Sign in workflow
  const emailInput: Locator = page.locator('#emailAddress');
  const passwd: Locator = page.locator('#password');
  const signInButton: Locator = page.locator('//button[text()="Sign in"]');
  const sendSignInLinkButton: Locator = page.locator(
    '//button[text()="Send sign-in link"]'
  );
  const resetPasswordButton: Locator = page.locator(
    '//button[text()="Forgot password?"]'
  );

  // Sign up workflow
  const firstName: Locator = page.locator('#first_name');
  const lastName: Locator = page.locator('#last_name');
  const phoneInput: Locator = page.locator('#phone');
  const nameInput: Locator = page.locator('#name');
  const email: Locator = page.locator('#email');
  const descriptionInput: Locator = page.locator('#description');

  const accountLocators = {
    signInButton,
    sendSignInLinkButton,
    resetPasswordButton,
    emailInput,
    passwd,

    firstName,
    lastName,
    phoneInput,
    nameInput,
    email,
    descriptionInput,
  };

  return accountLocators;
};

export default accountLocatorsGenerator;
