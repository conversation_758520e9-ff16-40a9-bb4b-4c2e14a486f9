import { test, expect, Locator, Page, BrowserContext } from '@playwright/test';
// Page Objects
import AccountPage from '@pages/account-page';
// Types
import { BASE_URL, EMAILS, PASSWORD } from '@assets/data/authData';

let accountPage: AccountPage;
let page: Page;
const EMAIL: string = EMAILS['base'];

test.beforeAll(async ({ browser }) => {
  const context: BrowserContext = await browser.newContext();
  page = await context.newPage();
  // Initialize AccountPage instance
  accountPage = new AccountPage(page, context);
  page = accountPage.page;
});

test.beforeEach(async () => {
  await page.goto(BASE_URL);
  await page.waitForLoadState('networkidle');
});

test.afterAll(async () => {
  await page.close();
});

test.describe('Sign in/out Flow Tests', () => {
  test.describe('Sign in Dialog Functionality', () => {
    test('Should send sign-in link', { tag: ['@Basic1'] }, async () => {
      const { emailInput, sendSignInLinkButton } = accountPage.accountLocators;

      // Send sign-in link
      await emailInput.fill(EMAIL);
      await sendSignInLinkButton.click();

      // Verify confirmation message
      const info: Locator = page.locator(`text=Sent sign-in email to ${EMAIL}`);
      await expect(info).toBeVisible();
    });

    test('Should send reset password link', { tag: ['@Basic'] }, async () => {
      const { emailInput, resetPasswordButton } = accountPage.accountLocators;

      // Send reset password link
      await emailInput.fill(EMAIL);
      await resetPasswordButton.click();

      // Verify confirmation message
      const info: Locator = page.locator(
        `text=Sent password reset e-mail to ${EMAIL}`
      );
      await expect(info).toBeVisible();
    });

    test(
      'Should successfully sign in with valid credentials and sign out',
      { tag: ['@Basic'] },
      async () => {
        const { emailInput, passwd, signInButton } =
          accountPage.accountLocators;

        // Sign in with valid email and password
        await emailInput.fill(EMAIL);
        await passwd.fill(PASSWORD);
        await signInButton.click();

        // Verify successful sign-in message
        const info: Locator = page.locator(`text=Signed in as ${EMAIL}`);
        await expect(info).toBeVisible();
      }
    );
  });
});
